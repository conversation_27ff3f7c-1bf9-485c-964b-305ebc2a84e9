import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { useAppStore } from '../store';
import logger from './productionLogger';

export interface DeviceInfoType {
  deviceId: string;
  appVersion: string;
  buildNumber: string;
  platform: 'ios' | 'android';
  osVersion: string;
  deviceModel: string;
  deviceBrand: string;
  isEmulator: boolean;
  hasNotch: boolean;
  isTablet: boolean;
  deviceName?: string;
  systemName?: string;
  bundleId?: string;
  carrier?: string;
  totalMemory?: number;
  usedMemory?: number;
  batteryLevel?: number;
  isLandscape?: boolean;
  screenWidth?: number;
  screenHeight?: number;
  timezone?: string;
  locale?: string;
}

class DeviceInfoService {
  private cachedDeviceInfo: DeviceInfoType | null = null;

  /**
   * Get comprehensive device information
   */
  async getDeviceInfo(): Promise<DeviceInfoType> {
    // Return cached info if available and recent
    if (this.cachedDeviceInfo) {
      return this.cachedDeviceInfo;
    }

    try {
      logger.info('Collecting device information', null, 'device');

      // Collect device info with individual error handling for better debugging
      const deviceInfo: DeviceInfoType = {
        // Basic device info - collect each one individually to identify failures
        deviceId: await this.safeGetDeviceId(),
        appVersion: this.safeGetAppVersion(),
        buildNumber: this.safeGetBuildNumber(),
        platform: Platform.OS as 'ios' | 'android',
        osVersion: this.safeGetOSVersion(),
        deviceModel: await this.safeGetDeviceModel(),
        deviceBrand: await this.safeGetDeviceBrand(),
        isEmulator: await this.safeGetIsEmulator(),
        hasNotch: this.safeGetHasNotch(),
        isTablet: this.safeGetIsTablet(),

        // Extended device info - with safe method calls
        deviceName: await this.safeGetDeviceName(),
        systemName: this.safeGetSystemName(),
        bundleId: this.safeGetBundleId(),
        carrier: await this.safeGetCarrier(),
        totalMemory: await this.safeGetTotalMemory(),
        usedMemory: await this.safeGetUsedMemory(),
        batteryLevel: await this.safeGetBatteryLevel(),
        isLandscape: await this.safeGetIsLandscape(),
        timezone: this.safeGetTimezone(),
        locale: await this.safeGetLocale(),
      };

      // Get screen dimensions
      try {
        if (typeof DeviceInfo.getScreenDimensions === 'function') {
          const { width, height } = await DeviceInfo.getScreenDimensions();
          deviceInfo.screenWidth = width;
          deviceInfo.screenHeight = height;
        } else {
          // Fallback to React Native Dimensions
          const { Dimensions } = require('react-native');
          const { width, height } = Dimensions.get('screen');
          deviceInfo.screenWidth = width;
          deviceInfo.screenHeight = height;
        }
      } catch (error) {
        logger.warn('Failed to get screen dimensions', error, 'device');
        try {
          // Final fallback to React Native Dimensions
          const { Dimensions } = require('react-native');
          const { width, height } = Dimensions.get('screen');
          deviceInfo.screenWidth = width;
          deviceInfo.screenHeight = height;
        } catch (fallbackError) {
          logger.warn('Failed to get screen dimensions fallback', fallbackError, 'device');
        }
      }

      // Cache the device info
      this.cachedDeviceInfo = deviceInfo;

      // Store in app store for easy access
      try {
        const { setCacheItem } = useAppStore.getState();
        setCacheItem('deviceInfo', deviceInfo, 24 * 60 * 60 * 1000); // Cache for 24 hours
      } catch (storeError) {
        logger.warn('Failed to cache device info in app store', storeError, 'device');
      }

      logger.info('Device information collected successfully', {
        platform: deviceInfo.platform,
        model: deviceInfo.deviceModel,
        osVersion: deviceInfo.osVersion,
        appVersion: deviceInfo.appVersion,
        deviceId: deviceInfo.deviceId !== 'unknown' ? 'available' : 'unknown',
      }, 'device');

      return deviceInfo;
    } catch (error) {
      logger.error('Failed to collect device information', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        platform: Platform.OS,
      }, 'device');

      // Return minimal fallback device info
      const fallbackInfo: DeviceInfoType = {
        deviceId: 'unknown',
        appVersion: '1.0.0',
        buildNumber: '1',
        platform: Platform.OS as 'ios' | 'android',
        osVersion: Platform.Version.toString(),
        deviceModel: 'unknown',
        deviceBrand: 'unknown',
        isEmulator: false,
        hasNotch: false,
        isTablet: false,
        systemName: Platform.OS === 'ios' ? 'iOS' : 'Android',
        bundleId: 'com.vendy.app',
        timezone: 'UTC',
      };

      this.cachedDeviceInfo = fallbackInfo;
      return fallbackInfo;
    }
  }

  // Safe getter methods with individual error handling
  private async safeGetDeviceId(): Promise<string> {
    try {
      const deviceId = await DeviceInfo.getUniqueId();
      return deviceId || 'unknown';
    } catch (error) {
      logger.warn('Failed to get device ID', error, 'device');
      return 'unknown';
    }
  }

  private safeGetAppVersion(): string {
    try {
      return DeviceInfo.getVersion() || '1.0.0';
    } catch (error) {
      logger.warn('Failed to get app version', error, 'device');
      return '1.0.0';
    }
  }

  private safeGetBuildNumber(): string {
    try {
      return DeviceInfo.getBuildNumber() || '1';
    } catch (error) {
      logger.warn('Failed to get build number', error, 'device');
      return '1';
    }
  }

  private safeGetOSVersion(): string {
    try {
      return DeviceInfo.getSystemVersion() || Platform.Version.toString();
    } catch (error) {
      logger.warn('Failed to get OS version', error, 'device');
      return Platform.Version.toString();
    }
  }

  private async safeGetDeviceModel(): Promise<string> {
    try {
      const model = await DeviceInfo.getModel();
      return model || 'unknown';
    } catch (error) {
      logger.warn('Failed to get device model', error, 'device');
      return 'unknown';
    }
  }

  private async safeGetDeviceBrand(): Promise<string> {
    try {
      const brand = await DeviceInfo.getBrand();
      return brand || 'unknown';
    } catch (error) {
      logger.warn('Failed to get device brand', error, 'device');
      return 'unknown';
    }
  }

  private async safeGetIsEmulator(): Promise<boolean> {
    try {
      return await DeviceInfo.isEmulator();
    } catch (error) {
      logger.warn('Failed to get emulator status', error, 'device');
      return false;
    }
  }

  private safeGetHasNotch(): boolean {
    try {
      return DeviceInfo.hasNotch();
    } catch (error) {
      logger.warn('Failed to get notch status', error, 'device');
      return false;
    }
  }

  private safeGetIsTablet(): boolean {
    try {
      return DeviceInfo.isTablet();
    } catch (error) {
      logger.warn('Failed to get tablet status', error, 'device');
      return false;
    }
  }

  private safeGetTimezone(): string {
    try {
      // Try different methods for getting timezone
      if (typeof DeviceInfo.getTimezone === 'function') {
        return DeviceInfo.getTimezone() || 'UTC';
      } else if (typeof DeviceInfo.getTimeZone === 'function') {
        return DeviceInfo.getTimeZone() || 'UTC';
      } else {
        // Fallback to JavaScript Date timezone
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        return timezone || 'UTC';
      }
    } catch (error) {
      logger.warn('Failed to get timezone', error, 'device');
      try {
        // Final fallback to JavaScript Date timezone
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        return timezone || 'UTC';
      } catch (fallbackError) {
        return 'UTC';
      }
    }
  }

  private async safeGetLocale(): Promise<string | undefined> {
    try {
      // Try different methods for getting locale
      if (typeof DeviceInfo.getLocales === 'function') {
        const locales = await DeviceInfo.getLocales();
        return locales[0]?.languageTag;
      } else if (typeof DeviceInfo.getDeviceLocale === 'function') {
        return await DeviceInfo.getDeviceLocale();
      } else if (typeof DeviceInfo.getLocale === 'function') {
        return DeviceInfo.getLocale();
      } else {
        // Fallback to JavaScript navigator language
        const locale = navigator?.language || navigator?.languages?.[0];
        return locale;
      }
    } catch (error) {
      logger.warn('Failed to get locale', error, 'device');
      try {
        // Final fallback to JavaScript navigator language
        const locale = navigator?.language || navigator?.languages?.[0];
        return locale;
      } catch (fallbackError) {
        return undefined;
      }
    }
  }

  private async safeGetDeviceName(): Promise<string | undefined> {
    try {
      return await DeviceInfo.getDeviceName();
    } catch (error) {
      logger.warn('Failed to get device name', error, 'device');
      return undefined;
    }
  }

  private safeGetSystemName(): string {
    try {
      return DeviceInfo.getSystemName();
    } catch (error) {
      logger.warn('Failed to get system name', error, 'device');
      return Platform.OS === 'ios' ? 'iOS' : 'Android';
    }
  }

  private safeGetBundleId(): string {
    try {
      return DeviceInfo.getBundleId();
    } catch (error) {
      logger.warn('Failed to get bundle ID', error, 'device');
      return 'com.vendy.app';
    }
  }

  private async safeGetCarrier(): Promise<string | undefined> {
    try {
      return await DeviceInfo.getCarrier();
    } catch (error) {
      logger.warn('Failed to get carrier', error, 'device');
      return undefined;
    }
  }

  private async safeGetTotalMemory(): Promise<number | undefined> {
    try {
      return await DeviceInfo.getTotalMemory();
    } catch (error) {
      logger.warn('Failed to get total memory', error, 'device');
      return undefined;
    }
  }

  private async safeGetUsedMemory(): Promise<number | undefined> {
    try {
      return await DeviceInfo.getUsedMemory();
    } catch (error) {
      logger.warn('Failed to get used memory', error, 'device');
      return undefined;
    }
  }

  private async safeGetBatteryLevel(): Promise<number | undefined> {
    try {
      return await DeviceInfo.getBatteryLevel();
    } catch (error) {
      logger.warn('Failed to get battery level', error, 'device');
      return undefined;
    }
  }

  private async safeGetIsLandscape(): Promise<boolean | undefined> {
    try {
      return await DeviceInfo.isLandscape();
    } catch (error) {
      logger.warn('Failed to get landscape status', error, 'device');
      return undefined;
    }
  }

  /**
   * Get device info optimized for FCM token registration
   */
  async getFcmDeviceInfo(): Promise<Partial<DeviceInfoType>> {
    try {
      logger.info('Getting device info for FCM registration', null, 'device');
      const fullDeviceInfo = await this.getDeviceInfo();

      // Log what we got
      const unknownFields = Object.entries(fullDeviceInfo)
        .filter(([key, value]) => value === 'unknown')
        .map(([key]) => key);

      if (unknownFields.length > 0) {
        logger.warn('Some device info fields are unknown', { unknownFields }, 'device');
      }

      // Return only the essential info for FCM registration
      const fcmDeviceInfo = {
        deviceId: fullDeviceInfo.deviceId,
        platform: fullDeviceInfo.platform,
        osVersion: fullDeviceInfo.osVersion,
        deviceModel: fullDeviceInfo.deviceModel,
        deviceBrand: fullDeviceInfo.deviceBrand,
        appVersion: fullDeviceInfo.appVersion,
        buildNumber: fullDeviceInfo.buildNumber,
        isEmulator: fullDeviceInfo.isEmulator,
        deviceName: fullDeviceInfo.deviceName,
        timezone: fullDeviceInfo.timezone,
        locale: fullDeviceInfo.locale,
      };

      logger.info('FCM device info prepared', {
        hasDeviceId: fcmDeviceInfo.deviceId !== 'unknown',
        hasDeviceModel: fcmDeviceInfo.deviceModel !== 'unknown',
        hasDeviceBrand: fcmDeviceInfo.deviceBrand !== 'unknown',
        platform: fcmDeviceInfo.platform,
      }, 'device');

      return fcmDeviceInfo;
    } catch (error) {
      logger.error('Failed to get FCM device info', error, 'device');
      throw error;
    }
  }

  /**
   * Test device info collection and log detailed results
   */
  async testDeviceInfoCollection(): Promise<void> {
    logger.info('Starting device info collection test', null, 'device');

    try {
      // Test each method individually
      logger.info('Testing individual device info methods...', null, 'device');

      const tests = [
        { name: 'getUniqueId', method: () => DeviceInfo.getUniqueId() },
        { name: 'getVersion', method: () => DeviceInfo.getVersion() },
        { name: 'getBuildNumber', method: () => DeviceInfo.getBuildNumber() },
        { name: 'getSystemVersion', method: () => DeviceInfo.getSystemVersion() },
        { name: 'getModel', method: () => DeviceInfo.getModel() },
        { name: 'getBrand', method: () => DeviceInfo.getBrand() },
        { name: 'isEmulator', method: () => DeviceInfo.isEmulator() },
        { name: 'hasNotch', method: () => DeviceInfo.hasNotch() },
        { name: 'isTablet', method: () => DeviceInfo.isTablet() },
        { name: 'safeGetTimezone', method: () => this.safeGetTimezone() },
        { name: 'safeGetLocale', method: () => this.safeGetLocale() },
      ];

      for (const test of tests) {
        try {
          const result = await test.method();
          logger.info(`✅ ${test.name}: ${result}`, null, 'device');
        } catch (error) {
          logger.error(`❌ ${test.name} failed`, error, 'device');
        }
      }

      // Test full collection
      logger.info('Testing full device info collection...', null, 'device');
      const fullInfo = await this.getDeviceInfo();
      logger.info('Full device info collection completed', {
        deviceId: fullInfo.deviceId,
        platform: fullInfo.platform,
        model: fullInfo.deviceModel,
        brand: fullInfo.deviceBrand,
        unknownCount: Object.values(fullInfo).filter(v => v === 'unknown').length,
      }, 'device');

    } catch (error) {
      logger.error('Device info collection test failed', error, 'device');
    }
  }

  /**
   * Get device info from app store cache
   */
  getCachedDeviceInfo(): DeviceInfoType | null {
    try {
      const { getCacheItem } = useAppStore.getState();
      return getCacheItem('deviceInfo') as DeviceInfoType | null;
    } catch (error) {
      logger.warn('Failed to get cached device info', error, 'device');
      return null;
    }
  }

  /**
   * Clear cached device info (useful for testing or when device info changes)
   */
  clearCache(): void {
    this.cachedDeviceInfo = null;
    try {
      const { removeCacheItem } = useAppStore.getState();
      removeCacheItem('deviceInfo');
    } catch (error) {
      logger.warn('Failed to clear device info cache', error, 'device');
    }
  }

  /**
   * Get device fingerprint for security purposes
   */
  async getDeviceFingerprint(): Promise<string> {
    const deviceInfo = await this.getDeviceInfo();
    
    const fingerprintData = [
      deviceInfo.deviceId,
      deviceInfo.platform,
      deviceInfo.deviceModel,
      deviceInfo.deviceBrand,
      deviceInfo.osVersion,
      deviceInfo.bundleId,
    ].join('|');

    // Simple hash function (you might want to use a proper crypto library)
    let hash = 0;
    for (let i = 0; i < fingerprintData.length; i++) {
      const char = fingerprintData.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36);
  }

  /**
   * Check if device info has changed (useful for security monitoring)
   */
  async hasDeviceInfoChanged(): Promise<boolean> {
    const cachedInfo = this.getCachedDeviceInfo();
    if (!cachedInfo) return true;

    const currentInfo = await this.getDeviceInfo();
    
    // Compare key fields that shouldn't change
    const keyFields: (keyof DeviceInfoType)[] = [
      'deviceId', 'platform', 'deviceModel', 'deviceBrand', 'bundleId'
    ];

    return keyFields.some(field => cachedInfo[field] !== currentInfo[field]);
  }
}

export const deviceInfoService = new DeviceInfoService();
export default deviceInfoService;
