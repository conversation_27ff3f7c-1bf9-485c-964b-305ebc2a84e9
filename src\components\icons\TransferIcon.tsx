import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface TransferIconProps {
  size?: number;
  color?: string;
}

const TransferIcon: React.FC<TransferIconProps> = ({ 
  size = 24, 
  color = '#000000' 
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M20 4V16H18V7.83L3.41 22.41L2 21L16.17 6H8V4H20ZM2 12H4V20H12V22H2V12Z"
        fill={color}
      />
    </Svg>
  );
};

export default TransferIcon;