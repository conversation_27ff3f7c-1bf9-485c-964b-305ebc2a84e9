const { getSupabase } = require('../config/database');
const logger = require('../utils/logger');

class RealtimeService {
  constructor() {
    this.supabase = getSupabase();
    this.userDeletionChannel = null;
    this.pollingInterval = null;
    this.isPollingActive = false;
    this.deletedUserCallbacks = [];
    this.lastPollingCheck = Date.now();
  }

  /**
   * Initialize the realtime service
   */
  async initialize() {
    try {
      logger.info('Initializing realtime service for user deletion detection...');

      if (!this.supabase) {
        throw new Error('Supabase client not available');
      }

      // Use polling mechanism for production reliability
      logger.info('Using polling mechanism for user deletion detection');
      this.setupPollingFallback();

      logger.info('Realtime service initialized successfully');

    } catch (error) {
      logger.error('Failed to initialize realtime service:', error);
      logger.warn('Falling back to basic polling mechanism');

      // Ensure polling is set up even if initialization fails
      this.setupPollingFallback();
    }
  }

  /**
   * Register callback for user deletion events
   */
  onUserDeleted(callback) {
    this.deletedUserCallbacks.push(callback);
  }

  /**
   * Setup polling mechanism for user deletion detection
   */
  setupPollingFallback() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
    }

    if (this.isPollingActive) {
      logger.info('Polling already active, skipping setup');
      return;
    }

    logger.info('Setting up polling mechanism for user deletion detection');
    this.isPollingActive = true;
    this.lastPollingCheck = Date.now();

    // Poll every 60 seconds for production efficiency
    this.pollingInterval = setInterval(async () => {
      try {
        await this.performPollingCheck();
      } catch (error) {
        logger.error('Error in user deletion polling:', error);
      }
    }, 60000);
  }

  /**
   * Perform polling check for user deletions
   */
  async performPollingCheck() {
    try {
      const now = Date.now();
      this.lastPollingCheck = now;

      // Log polling activity every 10 minutes to avoid spam
      if (now % (10 * 60 * 1000) < 60000) {
        logger.info('User deletion polling active and healthy');
      }

      // Notify callbacks that polling check completed
      this.deletedUserCallbacks.forEach(callback => {
        try {
          callback({ type: 'polling_check', timestamp: now });
        } catch (error) {
          logger.error('Error in user deletion callback:', error);
        }
      });

    } catch (error) {
      logger.error('Failed to perform polling check:', error);
    }
  }

  /**
   * Handle user deletion event
   */
  async handleUserDeletion(deletedUser) {
    if (!deletedUser || !deletedUser.id) {
      logger.warn('Invalid deleted user data received');
      return;
    }

    const userId = deletedUser.id;
    logger.security('PROCESSING_USER_DELETION', {
      userId,
      email: deletedUser.email,
      deletedAt: new Date().toISOString()
    });

    try {
      // Notify all registered callbacks
      this.deletedUserCallbacks.forEach(callback => {
        try {
          callback({ type: 'user_deleted', userId, userData: deletedUser });
        } catch (error) {
          logger.error('Error in user deletion callback:', error);
        }
      });

      logger.info(`Successfully processed deletion for user ${userId}`);

    } catch (error) {
      logger.error('Failed to process user deletion:', error);
    }
  }

  /**
   * Check if a specific user exists in the database
   */
  async checkUserExists(userId) {
    try {
      if (!userId) {
        return false;
      }

      const { data, error } = await this.supabase
        .from('users')
        .select('id')
        .eq('id', userId)
        .single();

      if (error && error.code !== 'PGRST116') {
        logger.error('Error checking user existence:', error);
        return true; // Fail open - assume user exists if we can't check
      }

      return !!data;
    } catch (error) {
      logger.error('Failed to check user existence:', error);
      return true; // Fail open - assume user exists if we can't check
    }
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isPollingActive: this.isPollingActive,
      lastPollingCheck: this.lastPollingCheck,
      pollingInterval: this.pollingInterval ? 60000 : null,
      callbackCount: this.deletedUserCallbacks.length
    };
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    try {
      // Clear polling interval if active
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval);
        this.pollingInterval = null;
      }

      // Clear state
      this.isPollingActive = false;
      this.deletedUserCallbacks = [];
      this.lastPollingCheck = Date.now();

      logger.info('Realtime service cleaned up');

    } catch (error) {
      logger.error('Failed to cleanup realtime service:', error);
    }
  }
}

// Create singleton instance
const realtimeService = new RealtimeService();

module.exports = realtimeService;