/**
 * Local Avatar Image Component
 * Displays local avatar images with proper require() handling
 * Supports both local assets and online URLs
 */

import React from 'react';
import { Image, ImageStyle, StyleProp } from 'react-native';
import logger from '../services/productionLogger';

interface LocalAvatarImageProps {
  avatarPath: string;
  style?: StyleProp<ImageStyle>;
  defaultSource?: any;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
}

/**
 * Component that handles both local avatar assets and online URLs
 */
const LocalAvatarImage: React.FC<LocalAvatarImageProps> = ({
  avatarPath,
  style,
  defaultSource,
  resizeMode = 'cover'
}) => {
  // Check if this is a local avatar path
  const isLocal = avatarPath.startsWith('assets/') &&
                  (avatarPath.includes('/male/') || avatarPath.includes('/female/')) &&
                  avatarPath.endsWith('.png');

  if (isLocal) {
    // Handle local avatar with direct require()
    try {
      let avatarSource;

      // Direct mapping to avoid complex service calls
      switch (avatarPath) {
        case 'assets/male/1.png':
          avatarSource = require('../../assets/male/1.png');
          break;
        case 'assets/male/2.png':
          avatarSource = require('../../assets/male/2.png');
          break;
        case 'assets/male/3.png':
          avatarSource = require('../../assets/male/3.png');
          break;
        case 'assets/female/1.png':
          avatarSource = require('../../assets/female/1.png');
          break;
        case 'assets/female/2.png':
          avatarSource = require('../../assets/female/2.png');
          break;
        case 'assets/female/3.png':
          avatarSource = require('../../assets/female/3.png');
          break;
        default:
          // Fallback to male 1
          avatarSource = require('../../assets/male/1.png');
      }

      return (
        <Image
          source={avatarSource}
          style={style}
          defaultSource={defaultSource}
          resizeMode={resizeMode}
        />
      );
    } catch (error) {
      // Ultimate fallback
      return (
        <Image
          source={require('../../assets/male/1.png')}
          style={style}
          resizeMode={resizeMode}
        />
      );
    }
  } else {
    // Handle online URL
    logger.debug('Rendering online avatar', { avatarPath }, 'local_avatar_image');
    
    return (
      <Image
        source={{ uri: avatarPath }}
        style={style}
        defaultSource={defaultSource}
        resizeMode={resizeMode}
      />
    );
  }
};

export default LocalAvatarImage;
