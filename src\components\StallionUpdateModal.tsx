/**
 * Stallion Update Modal Component
 * 
 * React Native component for displaying OTA update notifications and progress
 * Integrates with Stallion OTA service for seamless update experience
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  Alert,
  BackHandler,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import stallionOTAService, { UpdateStatus, UpdateInfo, UpdateProgress } from '../services/stallionOTAService';
import logger from '../services/productionLogger';

const { width, height } = Dimensions.get('window');

interface StallionUpdateModalProps {
  visible: boolean;
  onClose: () => void;
}

const StallionUpdateModal: React.FC<StallionUpdateModalProps> = ({ visible, onClose }) => {
  const [updateStatus, setUpdateStatus] = useState<UpdateStatus>(UpdateStatus.UP_TO_DATE);
  const [updateInfo, setUpdateInfo] = useState<UpdateInfo | null>(null);
  const [progress, setProgress] = useState<UpdateProgress | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);

  useEffect(() => {
    // Add event listener for update events
    const handleUpdateEvent = (status: UpdateStatus, info?: UpdateInfo, progressInfo?: UpdateProgress) => {
      setUpdateStatus(status);
      setUpdateInfo(info || null);
      setProgress(progressInfo || null);

      // Update local state flags
      setIsDownloading(status === UpdateStatus.DOWNLOADING);
      setIsInstalling(status === UpdateStatus.INSTALLING);

      // Handle automatic restart for mandatory updates
      if (status === UpdateStatus.RESTART_REQUIRED && info?.isMandatory) {
        setTimeout(() => {
          stallionOTAService.restartApp();
        }, 2000);
      }
    };

    stallionOTAService.addEventListener(handleUpdateEvent);

    // Cleanup listener on unmount
    return () => {
      stallionOTAService.removeEventListener(handleUpdateEvent);
    };
  }, []);

  useEffect(() => {
    // Prevent back button during mandatory updates
    if (updateInfo?.isMandatory && visible) {
      const backHandler = BackHandler.addEventListener('hardwareBackPress', () => true);
      return () => backHandler.remove();
    }
  }, [updateInfo?.isMandatory, visible]);

  const handleDownloadUpdate = async () => {
    try {
      logger.info('[UPDATE_MODAL] User initiated update download', null, 'update');
      await stallionOTAService.downloadAndInstall();
    } catch (error) {
      logger.error('[UPDATE_MODAL] Failed to download update', error, 'update');
      Alert.alert('Update Error', 'Failed to download update. Please try again later.');
    }
  };

  const handleSkipUpdate = () => {
    if (updateInfo?.isMandatory) {
      Alert.alert(
        'Mandatory Update',
        'This update is required to continue using the app.',
        [{ text: 'OK' }]
      );
      return;
    }

    logger.info('[UPDATE_MODAL] User skipped optional update', null, 'update');
    onClose();
  };

  const handleRestartApp = () => {
    logger.info('[UPDATE_MODAL] User initiated app restart', null, 'update');
    stallionOTAService.restartApp();
  };

  const getStatusMessage = (): string => {
    switch (updateStatus) {
      case UpdateStatus.CHECKING:
        return 'Checking for updates...';
      case UpdateStatus.DOWNLOADING:
        return `Downloading update... ${progress?.percentage || 0}%`;
      case UpdateStatus.INSTALLING:
        return 'Installing update...';
      case UpdateStatus.RESTART_REQUIRED:
        return 'Update installed successfully!';
      case UpdateStatus.ERROR:
        return 'Update failed. Please try again.';
      default:
        return updateInfo?.isMandatory 
          ? 'A mandatory update is available'
          : 'An update is available';
    }
  };

  const getProgressPercentage = (): number => {
    if (updateStatus === UpdateStatus.DOWNLOADING && progress) {
      return progress.percentage;
    }
    if (updateStatus === UpdateStatus.INSTALLING) {
      return 100;
    }
    return 0;
  };

  const isUpdateInProgress = isDownloading || isInstalling;
  const showProgressBar = updateStatus === UpdateStatus.DOWNLOADING || updateStatus === UpdateStatus.INSTALLING;

  if (!visible || updateStatus === UpdateStatus.UP_TO_DATE) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={updateInfo?.isMandatory ? undefined : onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modal}>
          <LinearGradient
            colors={['#667eea', '#764ba2']}
            style={styles.header}
          >
            <Text style={styles.title}>
              {updateInfo?.isMandatory ? 'Mandatory Update' : 'Update Available'}
            </Text>
          </LinearGradient>

          <View style={styles.content}>
            <Text style={styles.statusText}>
              {getStatusMessage()}
            </Text>

            {updateInfo && (
              <View style={styles.updateInfo}>
                <Text style={styles.versionText}>
                  Version {updateInfo.version} (Build {updateInfo.buildNumber})
                </Text>
                
                {updateInfo.description && (
                  <Text style={styles.descriptionText}>
                    {updateInfo.description}
                  </Text>
                )}

                {updateInfo.packageSize && (
                  <Text style={styles.sizeText}>
                    Size: {(updateInfo.packageSize / (1024 * 1024)).toFixed(1)} MB
                  </Text>
                )}
              </View>
            )}

            {showProgressBar && (
              <View style={styles.progressContainer}>
                <View style={styles.progressBar}>
                  <View 
                    style={[
                      styles.progressFill, 
                      { width: `${getProgressPercentage()}%` }
                    ]} 
                  />
                </View>
                <Text style={styles.progressText}>
                  {getProgressPercentage()}%
                </Text>
              </View>
            )}

            {updateStatus === UpdateStatus.ERROR && (
              <Text style={styles.errorText}>
                Update failed. Please check your internet connection and try again.
              </Text>
            )}
          </View>

          <View style={styles.actions}>
            {updateStatus === UpdateStatus.UPDATE_AVAILABLE && !isUpdateInProgress && (
              <>
                <TouchableOpacity
                  style={[styles.button, styles.primaryButton]}
                  onPress={handleDownloadUpdate}
                >
                  <Text style={styles.primaryButtonText}>
                    {updateInfo?.isMandatory ? 'Update Now' : 'Download Update'}
                  </Text>
                </TouchableOpacity>

                {!updateInfo?.isMandatory && (
                  <TouchableOpacity
                    style={[styles.button, styles.secondaryButton]}
                    onPress={handleSkipUpdate}
                  >
                    <Text style={styles.secondaryButtonText}>Later</Text>
                  </TouchableOpacity>
                )}
              </>
            )}

            {updateStatus === UpdateStatus.RESTART_REQUIRED && (
              <TouchableOpacity
                style={[styles.button, styles.primaryButton]}
                onPress={handleRestartApp}
              >
                <Text style={styles.primaryButtonText}>
                  {updateInfo?.isMandatory ? 'Restarting...' : 'Restart App'}
                </Text>
              </TouchableOpacity>
            )}

            {updateStatus === UpdateStatus.ERROR && (
              <>
                <TouchableOpacity
                  style={[styles.button, styles.primaryButton]}
                  onPress={handleDownloadUpdate}
                >
                  <Text style={styles.primaryButtonText}>Try Again</Text>
                </TouchableOpacity>

                {!updateInfo?.isMandatory && (
                  <TouchableOpacity
                    style={[styles.button, styles.secondaryButton]}
                    onPress={handleSkipUpdate}
                  >
                    <Text style={styles.secondaryButtonText}>Cancel</Text>
                  </TouchableOpacity>
                )}
              </>
            )}

            {isUpdateInProgress && (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color="#667eea" />
                <Text style={styles.loadingText}>
                  {updateStatus === UpdateStatus.DOWNLOADING ? 'Downloading...' : 'Installing...'}
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modal: {
    width: width * 0.9,
    maxWidth: 400,
    backgroundColor: '#fff',
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  header: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
  },
  content: {
    padding: 20,
  },
  statusText: {
    fontSize: 16,
    color: '#333',
    textAlign: 'center',
    marginBottom: 16,
  },
  updateInfo: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  versionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#495057',
    marginBottom: 8,
  },
  descriptionText: {
    fontSize: 14,
    color: '#6c757d',
    lineHeight: 20,
    marginBottom: 8,
  },
  sizeText: {
    fontSize: 12,
    color: '#868e96',
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressBar: {
    height: 6,
    backgroundColor: '#e9ecef',
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#667eea',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: '#6c757d',
    textAlign: 'center',
  },
  errorText: {
    fontSize: 14,
    color: '#dc3545',
    textAlign: 'center',
    marginBottom: 16,
  },
  actions: {
    padding: 20,
    paddingTop: 0,
  },
  button: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginBottom: 12,
    alignItems: 'center',
  },
  primaryButton: {
    backgroundColor: '#667eea',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#dee2e6',
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#fff',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6c757d',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
  },
  loadingText: {
    fontSize: 14,
    color: '#6c757d',
    marginLeft: 8,
  },
});

export default StallionUpdateModal;
