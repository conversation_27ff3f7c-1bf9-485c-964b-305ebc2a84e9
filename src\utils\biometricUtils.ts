import { Platform, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import productionLogger from '../services/productionLogger';

// Import biometric libraries
let ReactNativeBiometrics: any;
let TouchID: any;

try {
  ReactNativeBiometrics = require('react-native-biometrics').default;
} catch (e) {
  productionLogger.warn('ReactNativeBiometrics library not available', {
    source: 'biometricUtils',
    action: 'library_import',
    error: (e as any)?.message || 'Import failed'
  });
  ReactNativeBiometrics = null;
}

try {
  TouchID = require('react-native-touch-id');
} catch (e) {
  productionLogger.warn('TouchID library not available', {
    source: 'biometricUtils',
    action: 'library_import',
    error: (e as any)?.message || 'Import failed'
  });
  TouchID = null;
}

export interface BiometricCapability {
  available: boolean;
  type: 'fingerprint' | 'face' | 'voice' | 'iris' | 'none';
  error?: string;
}

/**
 * Check if biometric authentication is available and what type
 */
export const checkBiometricCapability = async (): Promise<BiometricCapability> => {
  try {
    // Try react-native-biometrics first (more reliable)
    if (ReactNativeBiometrics) {
      const rnBiometrics = new ReactNativeBiometrics();
      const { available, biometryType } = await rnBiometrics.isSensorAvailable();
      
      if (available) {
        let type: BiometricCapability['type'] = 'fingerprint';
        
        switch (biometryType) {
          case 'FaceID':
            type = 'face';
            break;
          case 'TouchID':
          case 'Biometrics':
            type = 'fingerprint';
            break;
          default:
            type = 'fingerprint';
        }
        
        return { available: true, type };
      }
    }
    
    // Fallback to TouchID library
    if (TouchID) {
      const biometryType = await TouchID.isSupported();
      if (biometryType) {
        let type: BiometricCapability['type'] = 'fingerprint';
        
        if (biometryType === 'FaceID') {
          type = 'face';
        } else if (biometryType === 'TouchID' || biometryType === true) {
          type = 'fingerprint';
        }
        
        return { available: true, type };
      }
    }
    
    // No biometric support found
    return {
      available: false,
      type: 'none',
      error: 'No biometric authentication available',
    };
  } catch (error: any) {
    productionLogger.error('Failed to check biometric capability', {
      source: 'biometricUtils',
      action: 'check_capability',
      error: error?.message || 'Unknown error',
      platform: Platform.OS
    });
    return {
      available: false,
      type: 'none',
      error: 'Unable to check biometric capability',
    };
  }
};

/**
 * Check if user has biometric authentication enabled in the app
 */
export const isBiometricEnabled = async (): Promise<boolean> => {
  try {
    const enabled = await AsyncStorage.getItem('biometricEnabled');
    return enabled === 'true';
  } catch (error: any) {
    productionLogger.error('Failed to check biometric enabled status', {
      source: 'biometricUtils',
      action: 'check_enabled_status',
      error: error?.message || 'AsyncStorage error'
    });
    return false;
  }
};

export interface BiometricAuthOptions {
  cancelButtonText?: string;
  fallbackButtonText?: string;
  disableDeviceFallback?: boolean;
}

/**
 * Authenticate using biometric
 */
export const authenticateWithBiometric = async (
  promptMessage: string = 'Authenticate to continue',
  options: BiometricAuthOptions = {}
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Check if biometric is available
    const capability = await checkBiometricCapability();
    if (!capability.available) {
      return { success: false, error: 'Biometric authentication not available' };
    }

    const {
      cancelButtonText = 'Cancel',
      fallbackButtonText = 'Use PIN instead',
      disableDeviceFallback = false
    } = options;

    let authSuccess = false;

    // Try react-native-biometrics first
    if (ReactNativeBiometrics) {
      const rnBiometrics = new ReactNativeBiometrics();
      const result = await rnBiometrics.simplePrompt({
        promptMessage,
        cancelButtonText,
        fallbackButtonText: disableDeviceFallback ? undefined : fallbackButtonText,
      });
      productionLogger.info('Biometric authentication completed', {
        source: 'biometricUtils',
        action: 'authenticate',
        library: 'ReactNativeBiometrics',
        success: result.success,
        hasError: !!result.error
      });
      authSuccess = result.success;
      
      // If not successful, check if it was cancelled or failed authentication
      if (!result.success && result.error) {
        // Return the specific error so we can handle it properly
        return { success: false, error: result.error };
      } else if (!result.success) {
        // No specific error but failed - likely user cancelled
        return { success: false, error: 'Authentication cancelled' };
      }
    } 
    // Fallback to TouchID
    else if (TouchID) {
      const biometricOptions = {
        title: 'Biometric Authentication',
        subtitle: promptMessage,
        description: capability.type === 'face' 
          ? 'Look at the camera to authenticate' 
          : 'Place your finger on the sensor',
        fallbackLabel: disableDeviceFallback ? '' : fallbackButtonText,
        cancelLabel: cancelButtonText,
        passcodeFallback: !disableDeviceFallback,
      };
      
      await TouchID.authenticate(promptMessage, biometricOptions);
      authSuccess = true;
    } else {
      return { success: false, error: 'Biometric authentication not available' };
    }

    return { success: authSuccess };
  } catch (error: any) {
    productionLogger.error('Biometric authentication failed', {
      source: 'biometricUtils',
      action: 'authenticate',
      error: error?.message || 'Authentication error',
      errorCode: error?.code,
      errorName: error?.name,
      platform: Platform.OS,
      userInfo: error?.userInfo
    });
    
    // Handle user cancellation or "Use PIN" selection
    if (error.message && (
      error.message.includes('cancelled') || 
      error.message.includes('canceled') ||
      error.message.includes('UserCancel') ||
      error.message.includes('fallback') ||
      error.code === 'UserCancel' ||
      error.code === 'UserFallback'
    )) {
      return { success: false, error: 'Authentication cancelled' };
    }
    
    return { success: false, error: error.message || 'Biometric authentication failed' };
  }
};

/**
 * Get biometric type display name
 */
export const getBiometricDisplayName = (type: BiometricCapability['type']): string => {
  switch (type) {
    case 'face':
      return Platform.OS === 'ios' ? 'Face ID' : 'Face Recognition';
    case 'fingerprint':
      return Platform.OS === 'ios' ? 'Touch ID' : 'Fingerprint';
    case 'voice':
      return 'Voice Recognition';
    case 'iris':
      return 'Iris Recognition';
    default:
      return 'Biometric';
  }
};