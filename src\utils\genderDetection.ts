/**
 * Gender Detection Utility
 * Determines gender from first names to select appropriate avatar images
 * Uses a comprehensive database of names with high accuracy
 */

import logger from '../services/productionLogger';

// Comprehensive gender name database
const MALE_NAMES = new Set([
  // Common English male names
  'aaron', 'abraham', 'adam', 'adrian', 'alan', 'albert', 'alex', 'alexander', 'alfred', 'andrew',
  'anthony', 'antonio', 'arthur', 'austin', 'benjamin', 'bernard', 'billy', 'bobby', 'brandon', 'brian',
  'bruce', 'bryan', 'carl', 'carlos', 'charles', 'chris', 'christopher', 'clarence', 'craig', 'daniel',
  'david', 'dennis', 'donald', 'douglas', 'edward', 'eric', 'eugene', 'frank', 'fred', 'gary',
  'george', 'gerald', 'gregory', 'harold', 'harry', 'henry', 'howard', 'jack', 'jacob', 'james',
  'jason', 'jeffrey', 'jeremy', 'jerry', 'jesse', 'jimmy', 'john', 'johnny', 'jonathan', 'jordan',
  'jose', 'joseph', 'joshua', 'juan', 'justin', 'keith', 'kenneth', 'kevin', 'larry', 'lawrence',
  'louis', 'mark', 'martin', 'matthew', 'michael', 'nicholas', 'patrick', 'paul', 'peter', 'philip',
  'ralph', 'raymond', 'richard', 'robert', 'roger', 'ronald', 'roy', 'russell', 'ryan', 'samuel',
  'scott', 'sean', 'stephen', 'steven', 'thomas', 'timothy', 'todd', 'victor', 'walter', 'wayne',
  'william', 'willie',
  
  // African names (common in Nigeria and other African countries)
  'adebayo', 'adebola', 'adedayo', 'adedoyin', 'adekunle', 'adeleke', 'ademola', 'adeola', 'adesola', 'adewale',
  'adeyemi', 'akeem', 'akin', 'akinola', 'akintunde', 'babatunde', 'biodun', 'bolaji', 'chidi', 'chijioke',
  'chinedu', 'chukwu', 'damilola', 'emeka', 'femi', 'gbenga', 'ibrahim', 'ikechukwu', 'jide', 'kemi',
  'kunle', 'lanre', 'lekan', 'musa', 'nnamdi', 'obinna', 'okafor', 'olumide', 'olusegun', 'oluwatobi',
  'oluwatosin', 'segun', 'seun', 'tayo', 'temitope', 'tunde', 'uche', 'yemi', 'yusuf',
  
  // Arabic/Islamic names
  'abdul', 'abdullah', 'ahmad', 'ahmed', 'ali', 'hassan', 'hussein', 'ibrahim', 'ismail', 'khalid',
  'mahmoud', 'mohammed', 'muhammad', 'mustafa', 'omar', 'rashid', 'salim', 'tariq', 'yusuf', 'zakaria',
  
  // Other international names
  'andre', 'antonio', 'carlos', 'diego', 'fernando', 'francisco', 'gabriel', 'giovanni', 'jose',
  'juan', 'luis', 'manuel', 'marco', 'mario', 'miguel', 'pablo', 'pedro', 'rafael', 'ricardo',
  'roberto', 'sergio', 'victor'
]);

const FEMALE_NAMES = new Set([
  // Common English female names
  'abigail', 'alice', 'amanda', 'amy', 'andrea', 'angela', 'anna', 'anne', 'annie', 'ashley',
  'barbara', 'betty', 'beverly', 'brenda', 'carol', 'carolyn', 'catherine', 'cheryl', 'christina', 'christine',
  'cynthia', 'deborah', 'debra', 'denise', 'diana', 'diane', 'donna', 'doris', 'dorothy', 'elizabeth',
  'emily', 'emma', 'evelyn', 'frances', 'gloria', 'grace', 'helen', 'irene', 'jacqueline', 'janet',
  'janice', 'jean', 'jennifer', 'jessica', 'joan', 'joyce', 'judith', 'judy', 'julia', 'julie',
  'karen', 'kathleen', 'kathryn', 'kelly', 'kimberly', 'laura', 'linda', 'lisa', 'lois', 'louise',
  'margaret', 'maria', 'marie', 'marilyn', 'martha', 'mary', 'michelle', 'nancy', 'nicole', 'norma',
  'pamela', 'patricia', 'paula', 'phyllis', 'rachel', 'rebecca', 'robin', 'rose', 'ruby', 'ruth',
  'sandra', 'sara', 'sarah', 'sharon', 'shirley', 'stephanie', 'susan', 'tammy', 'teresa', 'theresa',
  'virginia', 'wanda',
  
  // African names (common in Nigeria and other African countries)
  'adebisi', 'adedoyin', 'adeola', 'adesola', 'adunni', 'aisha', 'ajoke', 'amina', 'bisi', 'bukola',
  'bunmi', 'chioma', 'damilola', 'ebere', 'fatima', 'folake', 'funmi', 'grace', 'halima', 'ifeoma',
  'kemi', 'khadija', 'lola', 'mariam', 'ngozi', 'nneka', 'omolara', 'ronke', 'sade', 'shade',
  'temitope', 'titilayo', 'tolani', 'yemi', 'zainab',
  
  // Arabic/Islamic names
  'aisha', 'amina', 'fatima', 'hafsa', 'khadija', 'layla', 'mariam', 'nadia', 'safiya', 'yasmin',
  'zainab', 'zahra',
  
  // Other international names
  'adriana', 'alejandra', 'ana', 'andrea', 'angela', 'beatriz', 'carmen', 'claudia', 'cristina',
  'elena', 'esperanza', 'eva', 'gloria', 'isabel', 'juana', 'laura', 'leticia', 'lucia', 'luz',
  'maria', 'martha', 'monica', 'patricia', 'pilar', 'rosa', 'sandra', 'silvia', 'sofia', 'teresa'
]);

// Names that can be both male and female (unisex)
const UNISEX_NAMES = new Set([
  'alex', 'andrea', 'angel', 'ashley', 'avery', 'bailey', 'blake', 'cameron', 'casey', 'charlie',
  'chris', 'dakota', 'drew', 'emery', 'hayden', 'jamie', 'jordan', 'kelly', 'kendall', 'kerry',
  'kim', 'leslie', 'logan', 'madison', 'morgan', 'parker', 'pat', 'quinn', 'reese', 'riley',
  'robin', 'sage', 'sam', 'shannon', 'skylar', 'taylor', 'terry', 'tracy', 'val', 'wesley',
  // African unisex names
  'ade', 'damilola', 'temitope', 'yemi'
]);

export type Gender = 'male' | 'female' | 'unisex';

export interface GenderDetectionResult {
  gender: Gender;
  confidence: 'high' | 'medium' | 'low';
  source: 'database' | 'pattern' | 'fallback';
}

/**
 * Detect gender from a first name
 * @param firstName The first name to analyze
 * @returns GenderDetectionResult with gender, confidence level, and detection source
 */
export function detectGender(firstName: string): GenderDetectionResult {
  if (!firstName || typeof firstName !== 'string' || firstName.trim() === '') {
    logger.debug('Invalid or empty firstName provided to gender detection', {
      firstName: firstName || 'undefined',
      type: typeof firstName
    }, 'gender_detection');
    return {
      gender: 'unisex',
      confidence: 'low',
      source: 'fallback'
    };
  }

  // Clean and normalize the name
  const cleanName = firstName.trim().toLowerCase().replace(/[^a-z]/g, '');

  if (!cleanName || cleanName.length === 0) {
    logger.debug('Name contains no valid characters after cleaning', {
      originalName: firstName,
      cleanName
    }, 'gender_detection');
    return {
      gender: 'unisex',
      confidence: 'low',
      source: 'fallback'
    };
  }

  logger.debug('Detecting gender for name', { originalName: firstName, cleanName }, 'gender_detection');

  // Check exact match in databases
  if (MALE_NAMES.has(cleanName)) {
    logger.debug('Gender detected from male names database', { name: cleanName }, 'gender_detection');
    return {
      gender: 'male',
      confidence: 'high',
      source: 'database'
    };
  }

  if (FEMALE_NAMES.has(cleanName)) {
    logger.debug('Gender detected from female names database', { name: cleanName }, 'gender_detection');
    return {
      gender: 'female',
      confidence: 'high',
      source: 'database'
    };
  }

  if (UNISEX_NAMES.has(cleanName)) {
    logger.debug('Gender detected as unisex from database', { name: cleanName }, 'gender_detection');
    return {
      gender: 'unisex',
      confidence: 'high',
      source: 'database'
    };
  }

  // Pattern-based detection for names not in database
  const patternResult = detectGenderByPattern(cleanName);
  if (patternResult) {
    logger.debug('Gender detected from pattern analysis', { name: cleanName, result: patternResult }, 'gender_detection');
    return patternResult;
  }

  // Fallback to unisex
  logger.debug('Gender detection fallback to unisex', { name: cleanName }, 'gender_detection');
  return {
    gender: 'unisex',
    confidence: 'low',
    source: 'fallback'
  };
}

/**
 * Detect gender using linguistic patterns
 * @param name Cleaned name to analyze
 * @returns GenderDetectionResult or null if no pattern matches
 */
function detectGenderByPattern(name: string): GenderDetectionResult | null {
  // Common female name endings
  const femaleEndings = ['a', 'ia', 'ina', 'ita', 'ella', 'ette', 'ine', 'lyn', 'lynn'];
  // Common male name endings
  const maleEndings = ['o', 'us', 'er', 'on', 'an', 'en'];

  // Check female patterns
  for (const ending of femaleEndings) {
    if (name.endsWith(ending) && name.length > ending.length) {
      return {
        gender: 'female',
        confidence: 'medium',
        source: 'pattern'
      };
    }
  }

  // Check male patterns
  for (const ending of maleEndings) {
    if (name.endsWith(ending) && name.length > ending.length) {
      return {
        gender: 'male',
        confidence: 'medium',
        source: 'pattern'
      };
    }
  }

  return null;
}

/**
 * Get a random avatar image path based on gender
 * @param gender The detected gender
 * @returns string Path to avatar image
 */
export function getRandomAvatarForGender(gender: Gender): string {
  const maleAvatars = ['1.png', '2.png', '3.png'];
  const femaleAvatars = ['1.png', '2.png', '3.png'];

  let avatarFolder: string;
  let avatarFiles: string[];

  if (gender === 'female') {
    avatarFolder = 'female';
    avatarFiles = femaleAvatars;
  } else {
    // Default to male for 'male' and 'unisex'
    avatarFolder = 'male';
    avatarFiles = maleAvatars;
  }

  // Get random avatar from the appropriate folder
  const randomIndex = Math.floor(Math.random() * avatarFiles.length);
  const selectedAvatar = avatarFiles[randomIndex];

  const avatarPath = `assets/${avatarFolder}/${selectedAvatar}`;
  
  logger.info('Selected avatar for gender', {
    gender,
    avatarFolder,
    selectedAvatar,
    avatarPath
  }, 'gender_detection');

  return avatarPath;
}

/**
 * Get all available avatar options for a specific gender
 * @param gender The detected gender
 * @returns string[] Array of avatar image paths
 */
export function getAllAvatarsForGender(gender: Gender): string[] {
  const maleAvatars = ['1.png', '2.png', '3.png'];
  const femaleAvatars = ['1.png', '2.png', '3.png'];

  let avatarFolder: string;
  let avatarFiles: string[];

  if (gender === 'female') {
    avatarFolder = 'female';
    avatarFiles = femaleAvatars;
  } else {
    // Default to male for 'male' and 'unisex'
    avatarFolder = 'male';
    avatarFiles = maleAvatars;
  }

  const avatarPaths = avatarFiles.map(file => `assets/${avatarFolder}/${file}`);
  
  logger.debug('Generated avatar options for gender', {
    gender,
    avatarFolder,
    count: avatarPaths.length,
    avatarPaths
  }, 'gender_detection');

  return avatarPaths;
}

export default {
  detectGender,
  getRandomAvatarForGender,
  getAllAvatarsForGender
};
