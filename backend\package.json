{"name": "vendy-backend", "version": "1.0.0", "description": "Secure backend API for Vendy VTU application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "verify-realtime": "node scripts/verifyRealtimeSetup.js"}, "keywords": ["vtu", "fintech", "sms", "api", "secure"], "author": "Vendy Team", "license": "MIT", "dependencies": {"@getbrevo/brevo": "^2.2.0", "@supabase/supabase-js": "^2.38.4", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "events": "^3.3.0", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "firebase-admin": "^13.4.0", "form-data": "^4.0.4", "fs": "^0.0.1-security", "google-auth-library": "^10.1.0", "helmet": "^7.1.0", "hpp": "^0.2.3", "ioredis": "^5.6.1", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.2", "node-cron": "^4.2.1", "path": "^0.12.7", "ws": "^8.18.3", "xss": "^1.0.14"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=16.0.0"}}