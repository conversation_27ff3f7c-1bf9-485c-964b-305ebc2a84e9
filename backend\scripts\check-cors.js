#!/usr/bin/env node

/**
 * PayVendy Backend - CORS Configuration Checker
 * 
 * Checks and validates CORS configuration for the backend server.
 */

require('dotenv').config();

console.log('🔍 Checking PayVendy Backend CORS Configuration...\n');

// Check environment variables
const allowedOrigins = process.env.ALLOWED_ORIGINS;
const nodeEnv = process.env.NODE_ENV;
const port = process.env.PORT || 8000;

console.log('📋 Current Configuration:');
console.log(`   Environment: ${nodeEnv}`);
console.log(`   Server Port: ${port}`);
console.log(`   Allowed Origins: ${allowedOrigins}`);

if (!allowedOrigins) {
  console.log('\n❌ ALLOWED_ORIGINS not set in environment variables');
  console.log('   Add ALLOWED_ORIGINS to your .env file');
  process.exit(1);
}

const origins = allowedOrigins.split(',').map(origin => origin.trim());

console.log('\n🌐 Parsed Origins:');
origins.forEach((origin, index) => {
  console.log(`   ${index + 1}. ${origin}`);
});

// Check for common admin panel origins
const commonAdminOrigins = [
  'http://localhost:3001',
  'http://localhost:3000',
  'http://127.0.0.1:3001',
  'http://127.0.0.1:3000'
];

console.log('\n✅ Admin Panel Origins Check:');
commonAdminOrigins.forEach(adminOrigin => {
  const isAllowed = origins.includes(adminOrigin);
  console.log(`   ${isAllowed ? '✅' : '❌'} ${adminOrigin} ${isAllowed ? '(allowed)' : '(blocked)'}`);
});

// Check for wildcard usage
if (origins.includes('*')) {
  console.log('\n⚠️  WARNING: Wildcard (*) origin detected!');
  console.log('   Wildcard origins do not work with credentials: true');
  console.log('   Consider using specific origins instead');
}

console.log('\n🚀 Recommendations:');
console.log('   1. Ensure admin panel origin is in ALLOWED_ORIGINS');
console.log('   2. Restart backend server after changing .env');
console.log('   3. Check browser console for CORS errors');
console.log('   4. Verify admin panel is running on expected port');

console.log('\n📝 Example .env configuration:');
console.log('   ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8081');

console.log('\n🔧 To test CORS:');
console.log(`   curl -H "Origin: http://localhost:3001" -H "Access-Control-Request-Method: POST" -H "Access-Control-Request-Headers: content-type" -X OPTIONS http://localhost:${port}/api/v1/admin/login`);
