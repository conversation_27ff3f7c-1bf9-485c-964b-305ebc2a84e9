import { Platform } from 'react-native';
import { useAppStore, subscribeToAuthChanges, subscribeToNetworkChanges } from '../store';
import { featureFlags } from './featureFlagService';
import { performanceService } from './performanceService';
import { crashReporting } from './crashReportingService';
import { secureStorage } from './secureStorageService';
import { apiService } from './apiService';
import logger from './productionLogger';
import backgroundProcessor from './backgroundProcessor';
import deviceInfoService from './deviceInfoService';

// Type definitions
interface DeviceInfoType {
  deviceId: string;
  appVersion: string;
  buildNumber: string;
  platform: 'ios' | 'android';
  osVersion: string;
  deviceModel: string;
  deviceBrand: string;
  isEmulator: boolean;
  hasNotch: boolean;
  isTablet: boolean;
}

interface InitializationStatus {
  isInitialized: boolean;
  isInitializing: boolean;
}

class AppInitializationService {
  private isInitialized = false;
  private initPromise: Promise<boolean> | null = null;

  async initialize(): Promise<boolean> {
    // Prevent multiple initializations
    if (this.isInitialized) {
      return true;
    }

    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this.performInitialization();
    return this.initPromise;
  }

  private async performInitialization(): Promise<boolean> {
    const perfId = performanceService.startTiming('app_initialization');
    
    try {
      logger.info('Starting app initialization', null, 'init');

      // 1. Initialize device info
      const deviceInfo = await this.initializeDeviceInfo();
      logger.info('Device info initialized', null, 'init');

      // 2. Initialize crash reporting
      await this.initializeCrashReporting(deviceInfo);
      logger.info('Crash reporting initialized', null, 'init');

      // 3. Initialize performance monitoring
      await this.initializePerformanceMonitoring();
      logger.info('Performance monitoring initialized', null, 'init');



      // 3b. Initialize background processor
      backgroundProcessor.initialize();
      logger.info('Background processor initialized', null, 'init');

      // 4. Initialize secure storage
      await this.initializeSecureStorage();
      logger.info('Secure storage initialized', null, 'init');

      // 5. Initialize feature flags
      await this.initializeFeatureFlags(deviceInfo);
      logger.info('Feature flags initialized', null, 'init');

      // 6. Initialize store subscriptions
      this.initializeStoreSubscriptions();
      logger.info('Store subscriptions initialized', null, 'init');

      // 7. Restore authentication state
      await this.restoreAuthenticationState();
      logger.info('Authentication state restored', null, 'init');

      // 8. Initialize network monitoring
      this.initializeNetworkMonitoring();
      logger.info('Network monitoring initialized', null, 'init');

      // 9. Cleanup old data
      await this.cleanupOldData();
      logger.info('Old data cleaned up', null, 'init');

      this.isInitialized = true;
      performanceService.endTiming(perfId);
      
      logger.info('App initialization completed successfully', {
        duration: Date.now() - (performanceService as any).startTime
      }, 'init');
      return true;

    } catch (error) {
      performanceService.endTiming(perfId);
      logger.error('App initialization failed', error, 'init');
      
      // Record initialization failure
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;
      
      crashReporting.recordNonFatalError('App initialization failed', {
        error: errorMessage,
        stack: errorStack,
      });

      return false;
    }
  }

  private async initializeDeviceInfo(): Promise<DeviceInfoType> {
    try {
      logger.info('Initializing device information using enhanced device info service', null, 'init');

      // Test device info collection in development
      if (__DEV__) {
        await deviceInfoService.testDeviceInfoCollection();
      }

      // Use the enhanced device info service
      const fullDeviceInfo = await deviceInfoService.getDeviceInfo();

      // Convert to the format expected by this service
      const deviceInfo: DeviceInfoType = {
        deviceId: fullDeviceInfo.deviceId,
        appVersion: fullDeviceInfo.appVersion,
        buildNumber: fullDeviceInfo.buildNumber,
        platform: fullDeviceInfo.platform,
        osVersion: fullDeviceInfo.osVersion,
        deviceModel: fullDeviceInfo.deviceModel,
        deviceBrand: fullDeviceInfo.deviceBrand,
        isEmulator: fullDeviceInfo.isEmulator,
        hasNotch: fullDeviceInfo.hasNotch,
        isTablet: fullDeviceInfo.isTablet,
      };

      // Store device info in app store for easy access
      const { setCacheItem } = useAppStore.getState();
      setCacheItem('deviceInfo', deviceInfo, 24 * 60 * 60 * 1000); // Cache for 24 hours

      logger.info('Device information initialized successfully', {
        platform: deviceInfo.platform,
        model: deviceInfo.deviceModel,
        brand: deviceInfo.deviceBrand,
        osVersion: deviceInfo.osVersion,
        appVersion: deviceInfo.appVersion,
        deviceId: deviceInfo.deviceId !== 'unknown' ? 'available' : 'unknown',
        hasUnknownFields: Object.values(deviceInfo).includes('unknown'),
      }, 'init');

      return deviceInfo;
    } catch (error) {
      logger.error('Failed to initialize device info', error, 'init');
      return {
        deviceId: 'unknown',
        appVersion: '1.0.0',
        buildNumber: '1',
        platform: Platform.OS as 'ios' | 'android',
        osVersion: 'unknown',
        deviceModel: 'unknown',
        deviceBrand: 'unknown',
        isEmulator: false,
        hasNotch: false,
        isTablet: false,
      };
    }
  }

  private async initializeCrashReporting(deviceInfo: DeviceInfoType): Promise<void> {
    try {
      // Set user context for crash reporting
      const { user } = useAppStore.getState();
      if (user?.id) {
        crashReporting.setUserId(user.id);
      }

      // Add device info breadcrumb
      crashReporting.addBreadcrumb({
        category: 'device',
        message: 'Device info collected',
        level: 'info',
        data: deviceInfo,
      });

      // Add app start breadcrumb
      crashReporting.addBreadcrumb({
        category: 'app',
        message: 'App initialization started',
        level: 'info',
        data: {
          appVersion: deviceInfo.appVersion,
          platform: deviceInfo.platform,
        },
      });

    } catch (error) {
      logger.warn('Failed to initialize crash reporting', error, 'init');
    }
  }

  private async initializePerformanceMonitoring(): Promise<void> {
    try {
      // Enable performance monitoring based on feature flag or environment
      const shouldEnable = (typeof __DEV__ !== 'undefined' && __DEV__) ||
        (featureFlags.features && typeof featureFlags.features.betaFeatures === 'function' && featureFlags.features.betaFeatures());
      performanceService.setEnabled(shouldEnable);

      // Track app startup performance
      performanceService.startTiming('app_startup');

    } catch (error) {
      logger.warn('Failed to initialize performance monitoring', error, 'init');
    }
  }

  private async initializeSecureStorage(): Promise<void> {
    try {
      // Check if biometric authentication is available
      const biometricAvailable = await secureStorage.isBiometricAvailable();
      const biometryType = await secureStorage.getBiometryType();

      // Store biometric info in cache
      const { setCacheItem } = useAppStore.getState();
      setCacheItem('biometricInfo', {
        available: biometricAvailable,
        type: biometryType,
      });

      logger.info('Biometric info collected', { biometricAvailable, biometryType }, 'init');

    } catch (error) {
      logger.warn('Failed to initialize secure storage', error, 'init');
    }
  }

  private async initializeFeatureFlags(deviceInfo: DeviceInfoType): Promise<void> {
    try {
      const { user } = useAppStore.getState();

      await featureFlags.initialize({
        userId: user?.id || undefined, // Explicitly set to undefined if no user
        userGroup: user?.preferences?.language || 'default',
        appVersion: deviceInfo.appVersion,
        platform: deviceInfo.platform,
        deviceId: deviceInfo.deviceId,
      });

      // Update store with feature flags
      const { setFeatureFlag } = useAppStore.getState();
      const enabledFlags = featureFlags.getEnabledFlags();
      
      enabledFlags.forEach(flagKey => {
        setFeatureFlag(flagKey, true);
      });

    } catch (error) {
      logger.warn('Failed to initialize feature flags', error, 'init');
    }
  }

  private initializeStoreSubscriptions(): void {
    try {
      // Subscribe to auth changes
      subscribeToAuthChanges((isAuthenticated: boolean) => {
        crashReporting.addBreadcrumb({
          category: 'auth',
          message: `User ${isAuthenticated ? 'logged in' : 'logged out'}`,
          level: 'info',
        });

        // Update crash reporting user context
        if (isAuthenticated) {
          const { user } = useAppStore.getState();
          if (user?.id) {
            crashReporting.setUserId(user.id);
          }
        } else {
          crashReporting.setUserId('');
        }
      });

      // Subscribe to network changes
      subscribeToNetworkChanges((isOnline: boolean) => {
        crashReporting.addBreadcrumb({
          category: 'network',
          message: `Network ${isOnline ? 'connected' : 'disconnected'}`,
          level: isOnline ? 'info' : 'warning',
        });
      });

    } catch (error) {
      logger.warn('Failed to initialize store subscriptions', error, 'init');
    }
  }

  private async restoreAuthenticationState(): Promise<void> {
    try {
      // Get tokens from secure storage with improved error handling
      logger.debug('🔍 [INIT] Checking for stored authentication tokens...', null, 'init');
      const { accessToken, refreshToken } = await secureStorage.getAuthTokens();

      if (accessToken && refreshToken) {
        logger.debug('✅ [INIT] Found stored tokens, verifying with backend...', null, 'init');
        // Update store with tokens
        const { setAuthTokens, setAuthenticated } = useAppStore.getState();
        setAuthTokens(accessToken, refreshToken);

        // Verify token validity by making a test API call
        try {
          logger.info('🔍 [INIT] Verifying stored tokens with backend...', null, 'init');
          console.log('🔍 [INIT] Making token check request to /auth/token-check');

          const response = await apiService.get('/auth/token-check', {}, {
            timeout: 25000, // 15 seconds
            retries: 2,
          });

          console.log('✅ [INIT] Token verification response:', response.status, response.data);

          if (response.status === 200) {
            setAuthenticated(true);
            logger.info('✅ [INIT] Authentication state restored successfully', null, 'init');
          } else {
            console.log('❌ [INIT] Token verification failed with status:', response.status);
            // Token is invalid, clear it
            await this.clearInvalidTokens();
          }
        } catch (apiError) {
          console.log('❌ [INIT] Token verification error:', apiError);

          // Check if it's a tunnel/network error (503, 502, etc.)
          const isNetworkError = apiError && typeof apiError === 'object' && 'status' in apiError &&
                                 typeof (apiError as any).status === 'number' &&
                                 [502, 503, 504].includes((apiError as any).status);

          if (isNetworkError) {
            console.log('🌐 [INIT] Network/Tunnel error detected - backend may be unavailable');
            console.log('🔄 [INIT] Skipping token verification due to network issues');

            // Don't clear tokens for network errors - backend might just be temporarily down
            // Keep user logged in and they can try again later
            logger.warn('Network error during token verification - keeping user logged in', {
              error: apiError instanceof Error ? apiError.message : String(apiError),
              status: apiError.status,
              note: 'User remains authenticated, will retry on next app start'
            }, 'init');

            // Set authenticated to true but with a flag that verification failed
            setAuthenticated(true);
            return;
          }

          // Check if it's a 404 error (endpoint not found) and try fallback
          if (apiError && typeof apiError === 'object' && 'status' in apiError && apiError.status === 404) {
            console.log('🔄 [INIT] /auth/token-check endpoint not found, trying fallback...');
            try {
              // Test if backend is accessible at all
              const testResponse = await apiService.get('/auth/test', {}, {
                timeout: 5000,
                retries: 1,
                skipAuth: true // Test endpoint doesn't require auth
              });

              if (testResponse.status === 200) {
                console.log('✅ [INIT] Backend is accessible, but /auth/token-check endpoint missing');
                // Backend is working, but verify endpoint doesn't exist
                // For now, assume tokens are valid if backend is accessible
                setAuthenticated(true);
                logger.info('✅ [INIT] Authentication assumed valid (fallback)', null, 'init');
              } else {
                await this.clearInvalidTokens();
              }
            } catch (fallbackError) {
              console.log('❌ [INIT] Backend not accessible:', fallbackError);
              await this.clearInvalidTokens();
            }
          } else {
            // Other errors (401, 403, etc.) - clear tokens
            logger.warn('Token verification failed', {
              error: apiError instanceof Error ? apiError.message : String(apiError),
              status: apiError && typeof apiError === 'object' && 'status' in apiError ? apiError.status : 'unknown',
              response: apiError && typeof apiError === 'object' && 'response' in apiError ? apiError.response : 'none'
            }, 'init');
            await this.clearInvalidTokens();
          }
        }
      } else {
        // No tokens found - this is normal on first app launch
        logger.debug('🔍 [INIT] No stored tokens found (expected on first launch)', null, 'init');
      }

    } catch (error) {
      // Only log as warning if it's an unexpected error
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (error && !errorMessage.includes('not found') && !errorMessage.includes('No such key')) {
        logger.warn('Failed to restore authentication state', error, 'init');
      } else {
        logger.debug('No authentication state to restore (expected on first launch)', null, 'init');
      }
    }
  }

  private async clearInvalidTokens(): Promise<void> {
    try {
      await secureStorage.clearAuthData();
      const { resetAuth } = useAppStore.getState();
      resetAuth();
      logger.info('Cleared invalid tokens', null, 'init');
    } catch (error) {
      logger.error('Failed to clear invalid tokens', error, 'init');
    }
  }

  private initializeNetworkMonitoring(): void {
    try {
      // Network monitoring is handled in the API service
      // This is just a placeholder for any additional network setup
      logger.info('Network monitoring is handled by API service', null, 'init');
    } catch (error) {
      logger.warn('Failed to initialize network monitoring', error, 'init');
    }
  }

  private async cleanupOldData(): Promise<void> {
    try {
      // Clean up old crash reports
      await crashReporting.clearOldErrors(7); // Keep 7 days

      // Clean up old performance metrics
      await performanceService.clearOldMetrics(7); // Keep 7 days

      // Clean up expired cache items
      const { clearCache } = useAppStore.getState();
      // Cache cleanup is handled automatically in the store

      logger.info('Old data cleanup completed', null, 'init');

    } catch (error) {
      logger.warn('Failed to cleanup old data', error, 'init');
    }
  }

  // Public methods for external use
  isAppInitialized(): boolean {
    return this.isInitialized;
  }

  async reinitialize(): Promise<boolean> {
    this.isInitialized = false;
    this.initPromise = null;
    return this.initialize();
  }

  // Get initialization status
  getInitializationStatus(): InitializationStatus {
    return {
      isInitialized: this.isInitialized,
      isInitializing: this.initPromise !== null && !this.isInitialized,
    };
  }
}

export const appInit = new AppInitializationService();
export default appInit;