import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface SecurityIconProps {
  size?: number;
  color?: string;
}

const SecurityIcon: React.FC<SecurityIconProps> = ({ 
  size = 24, 
  color = '#007AFF' 
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1ZM12 7C13.1 7 14 7.9 14 9S13.1 11 12 11S10 10.1 10 9S10.9 7 12 7ZM12 17C10.33 17 8.94 16.16 8.24 14.9C8.66 14.34 10.26 14 12 14S15.34 14.34 15.76 14.9C15.06 16.16 13.67 17 12 17Z"
        fill={color}
      />
    </Svg>
  );
};

export default SecurityIcon;