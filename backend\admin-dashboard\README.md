# Vendy Admin Dashboard

A comprehensive admin dashboard for managing <PERSON><PERSON><PERSON>'s OTA (Over-The-Air) updates system.

## Features

- 🚀 **OTA Updates Management**: Deploy, manage, and monitor app updates
- 📊 **Analytics Dashboard**: Track update deployment statistics
- 🔐 **Secure Authentication**: Admin-only access with JWT tokens
- 📱 **Responsive Design**: Works on desktop, tablet, and mobile
- 🎨 **Modern UI**: Clean, professional interface with dark/light themes
- 📈 **Real-time Monitoring**: Live update status and analytics
- 🔄 **File Upload**: Drag-and-drop bundle uploads with progress tracking
- 🛡️ **Security**: Rate limiting, CORS protection, and secure headers

## Quick Start

### Prerequisites

- Node.js 16+ 
- npm or yarn
- PM2 (for production)
- Nginx (for reverse proxy)
- SSL certificate (Let's Encrypt recommended)

### Installation

1. **Clone and setup:**
   ```bash
   git clone <repository-url>
   cd admin-dashboard
   npm install
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Development mode:**
   ```bash
   npm run dev
   ```

4. **Production deployment:**
   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   ```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment mode | `production` |
| `ADMIN_PORT` | Server port | `3001` |
| `API_BASE_URL` | Backend API URL | `https://api.payvendy.name.ng` |
| `SESSION_SECRET` | Session encryption key | Generated |

### API Integration

The dashboard communicates with your main API at `/api/v1/admin/ota/` endpoints:

- `GET /api/v1/admin/ota/updates` - List all updates
- `POST /api/v1/admin/ota/updates` - Deploy new update
- `PUT /api/v1/admin/ota/updates/:id` - Modify update
- `DELETE /api/v1/admin/ota/updates/:id` - Delete update

## Usage

### Accessing the Dashboard

1. Navigate to `https://admin.payvendy.name.ng`
2. Login with your admin credentials
3. Use the sidebar to navigate between sections

### Deploying Updates

1. Go to **OTA Updates** section
2. Click **Deploy Update**
3. Upload your bundle file (ZIP format)
4. Fill in version details and description
5. Choose platform (Android/iOS/Both)
6. Set mandatory flag if needed
7. Click **Deploy Update**

### Managing Updates

- **Activate/Deactivate**: Toggle update availability
- **View Details**: See full update information
- **Delete**: Remove update (with confirmation)
- **Filter**: Filter by platform or status

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Admin User    │───▶│  Admin Dashboard │───▶│   Backend API   │
│                 │    │   (Port 3001)    │    │   (Port 8000)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │      Nginx       │
                       │  (Port 80/443)   │
                       └──────────────────┘
```

## Security

### Authentication
- JWT-based authentication
- HTTP-only cookies for token storage
- Automatic token refresh
- Session timeout handling

### Security Headers
- Content Security Policy (CSP)
- X-Frame-Options
- X-XSS-Protection
- Strict-Transport-Security
- X-Content-Type-Options

### Rate Limiting
- Login attempts: 5 per minute
- API requests: 30 per minute
- File uploads: Special handling

## Deployment

### Manual Deployment

1. **Prepare server:**
   ```bash
   sudo apt update
   sudo apt install nginx certbot python3-certbot-nginx
   npm install -g pm2
   ```

2. **Deploy application:**
   ```bash
   ./deploy.sh
   ```

3. **Verify deployment:**
   ```bash
   curl -f https://admin.payvendy.name.ng/health
   ```

### Automated Deployment

Use PM2 ecosystem for automated deployments:

```bash
pm2 deploy production setup
pm2 deploy production
```

## Monitoring

### PM2 Monitoring
```bash
pm2 status
pm2 logs vendy-admin-dashboard
pm2 monit
```

### Log Files
- Application logs: `./logs/`
- Nginx logs: `/var/log/nginx/`
- System logs: `journalctl -u nginx`

### Health Checks
- Application: `https://admin.payvendy.name.ng/health`
- API connectivity: Built-in dashboard checks

## Troubleshooting

### Common Issues

1. **Dashboard not loading:**
   ```bash
   pm2 logs vendy-admin-dashboard
   sudo nginx -t
   ```

2. **API connection failed:**
   - Check `API_BASE_URL` in `.env`
   - Verify backend API is running
   - Check network connectivity

3. **SSL certificate issues:**
   ```bash
   sudo certbot renew --dry-run
   sudo nginx -t && sudo systemctl reload nginx
   ```

4. **File upload failures:**
   - Check file size limits in Nginx
   - Verify disk space
   - Check upload permissions

### Debug Mode

Enable debug logging:
```bash
NODE_ENV=development pm2 restart vendy-admin-dashboard
```

## API Documentation

### Authentication

All admin endpoints require authentication:

```javascript
// Login
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "your-password"
}

// Response
{
  "success": true,
  "user": { "id": "...", "email": "...", "role": "admin" }
}
```



## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

Copyright © 2025 Vendy. All rights reserved.

## Support

For support and questions:
- Email: <EMAIL>
- Documentation: [Link to docs]
- Issues: [Link to issue tracker]
