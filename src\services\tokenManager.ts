import AsyncStorage from '@react-native-async-storage/async-storage';
import logger from './productionLogger';
import secureStorage from './secureStorageService';

/**
 * Centralized Token Management Service
 * Handles all token-related operations with proper error handling and infinite loop prevention
 */
class TokenManager {
  private static instance: TokenManager;
  private refreshInProgress = false;
  private refreshPromise: Promise<boolean> | null = null;
  private refreshAttempts = 0;
  private maxRefreshAttempts = 2;
  private refreshCooldown = 10000; // 10 seconds
  private lastRefreshAttempt = 0;

  private readonly TOKEN_KEY = 'auth_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';

  private constructor() {}

  static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager();
    }
    return TokenManager.instance;
  }

  /**
   * Check if we should attempt a token refresh
   */
  private shouldAttemptRefresh(): boolean {
    const now = Date.now();
    
    // Check cooldown period
    if (now - this.lastRefreshAttempt < this.refreshCooldown) {
      logger.warn('Token refresh attempted too soon, cooling down', {
        timeSinceLastAttempt: now - this.lastRefreshAttempt,
        cooldownPeriod: this.refreshCooldown
      }, 'token_manager');
      return false;
    }

    // Check max attempts
    if (this.refreshAttempts >= this.maxRefreshAttempts) {
      logger.error('Maximum token refresh attempts exceeded', {
        attempts: this.refreshAttempts,
        maxAttempts: this.maxRefreshAttempts
      }, 'token_manager');
      return false;
    }

    return true;
  }

  /**
   * Refresh token with proper concurrency control and infinite loop prevention
   */
  async refreshToken(): Promise<boolean> {
    // If refresh is already in progress, wait for it
    if (this.refreshInProgress && this.refreshPromise) {
      logger.info('Token refresh already in progress, waiting...', null, 'token_manager');
      return await this.refreshPromise;
    }

    // Check if we should attempt refresh
    if (!this.shouldAttemptRefresh()) {
      return false;
    }

    // Start refresh process
    this.refreshInProgress = true;
    this.lastRefreshAttempt = Date.now();
    this.refreshAttempts++;

    this.refreshPromise = this.performTokenRefresh();

    try {
      const result = await this.refreshPromise;
      
      if (result) {
        // Reset attempts on success
        this.refreshAttempts = 0;
        logger.info('Token refresh successful', null, 'token_manager');
      } else {
        logger.warn('Token refresh failed', {
          attempt: this.refreshAttempts,
          maxAttempts: this.maxRefreshAttempts
        }, 'token_manager');

        // If max attempts reached, clear tokens
        if (this.refreshAttempts >= this.maxRefreshAttempts) {
          logger.error('All token refresh attempts failed, clearing tokens', null, 'token_manager');
          await this.clearAllTokens();
          this.resetRefreshState();
        }
      }

      return result;
    } finally {
      this.refreshInProgress = false;
      this.refreshPromise = null;
    }
  }

  /**
   * Perform the actual token refresh
   */
  private async performTokenRefresh(): Promise<boolean> {
    try {
      const refreshToken = await this.getRefreshToken();
      if (!refreshToken) {
        logger.warn('No refresh token available', null, 'token_manager');
        return false;
      }

      logger.info('Attempting token refresh', {
        attempt: this.refreshAttempts,
        maxAttempts: this.maxRefreshAttempts
      }, 'token_manager');

      // Import apiService dynamically to avoid circular dependencies
      const { apiService } = await import('./apiService');
      
      const response = await apiService.post('/auth/refresh-token', {
        refreshToken: refreshToken,
      }, {
        skipAuth: true,
        timeout: 15000,
        retries: 1
      });

      if (response.data?.accessToken) {
        // Store new tokens
        await this.storeTokens(
          response.data.accessToken,
          response.data.refreshToken || refreshToken
        );

        // Update API service token
        apiService.setAuthToken(response.data.accessToken);

        logger.info('Token refresh completed successfully', null, 'token_manager');
        return true;
      }

      logger.warn('Token refresh failed: No access token in response', null, 'token_manager');
      return false;

    } catch (error: any) {
      logger.error('Token refresh error', error, 'token_manager');

      // Handle specific error cases
      if (error.status === 401 || error.status === 403) {
        // Token is invalid/expired/blacklisted
        logger.warn('Refresh token is invalid, expired, or blacklisted', {
          status: error.status,
          code: error.response?.code
        }, 'token_manager');
        
        // Clear tokens immediately for these errors
        await this.clearAllTokens();
        this.resetRefreshState();
        return false;
      }

      return false;
    }
  }

  /**
   * Store tokens securely
   */
  private async storeTokens(accessToken: string, refreshToken: string): Promise<void> {
    try {
      // Store in secure storage
      await secureStorage.storeAuthTokens(accessToken, refreshToken);
      
      // Store in AsyncStorage as backup
      await AsyncStorage.setItem(this.TOKEN_KEY, accessToken);
      await AsyncStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
      
      logger.debug('Tokens stored successfully', null, 'token_manager');
    } catch (error) {
      logger.error('Failed to store tokens', error, 'token_manager');
      throw error;
    }
  }

  /**
   * Get refresh token
   */
  private async getRefreshToken(): Promise<string | null> {
    try {
      // Try secure storage first
      const secureTokens = await secureStorage.getAuthTokens();
      if (secureTokens?.refreshToken) {
        return secureTokens.refreshToken;
      }

      // Fallback to AsyncStorage
      const refreshToken = await AsyncStorage.getItem(this.REFRESH_TOKEN_KEY);
      return refreshToken;
    } catch (error) {
      logger.error('Failed to get refresh token', error, 'token_manager');
      return null;
    }
  }

  /**
   * Clear all tokens
   */
  async clearAllTokens(): Promise<void> {
    try {
      // Clear from secure storage
      await secureStorage.clearAuthTokens();
      
      // Clear from AsyncStorage
      await AsyncStorage.multiRemove([this.TOKEN_KEY, this.REFRESH_TOKEN_KEY]);
      
      // Clear from API service
      const { apiService } = await import('./apiService');
      apiService.setAuthToken(null);
      
      logger.info('All tokens cleared successfully', null, 'token_manager');
    } catch (error) {
      logger.error('Failed to clear tokens', error, 'token_manager');
    }
  }

  /**
   * Reset refresh state
   */
  private resetRefreshState(): void {
    this.refreshAttempts = 0;
    this.refreshInProgress = false;
    this.refreshPromise = null;
    this.lastRefreshAttempt = 0;
  }

  /**
   * Check if refresh is currently in progress
   */
  isRefreshInProgress(): boolean {
    return this.refreshInProgress;
  }

  /**
   * Get current refresh attempts count
   */
  getRefreshAttempts(): number {
    return this.refreshAttempts;
  }

  /**
   * Force reset refresh state (use with caution)
   */
  forceResetRefreshState(): void {
    logger.warn('Force resetting token refresh state', null, 'token_manager');
    this.resetRefreshState();
  }

  /**
   * Check if token refresh is available
   */
  async canRefreshToken(): Promise<boolean> {
    const refreshToken = await this.getRefreshToken();
    return !!refreshToken && this.shouldAttemptRefresh();
  }
}

// Export singleton instance
export const tokenManager = TokenManager.getInstance();
export default tokenManager;
