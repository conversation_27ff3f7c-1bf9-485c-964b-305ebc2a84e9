import { apiService } from './apiService';
import { logger as productionLogger } from './productionLogger';
import { UserProfile, UserProfileResponse, UserBalanceResponse } from '../types/user';
import AvatarService from './avatarService';

class UserService {
  /**
   * Get current user's profile data
   */
  async getUserProfile(): Promise<UserProfileResponse> {
    try {
      productionLogger.info('Getting user profile', { service: 'userService', action: 'getUserProfile' });

      const response = await apiService.get('/user/profile', {}, {
        timeout: 10000, // 10 seconds timeout
        retries: 2, // 2 retries
        cache: true, // Enable caching
        cacheTime: 5 * 60 * 1000, // Cache for 5 minutes
      });

      productionLogger.info('Profile response received', {
        service: 'userService',
        action: 'getUserProfile',
        hasData: !!response?.data,
        responseStructure: response ? Object.keys(response) : 'no response'
      });

      // Log the full response structure for debugging
      console.log('🔍 [USER_SERVICE] Full response:', JSON.stringify(response, null, 2));

      // Safe response validation with proper null checks
      if (!response || typeof response !== 'object') {
        throw new Error('No response received from API');
      }

      if (!response.data || typeof response.data !== 'object') {
        throw new Error('No data in API response');
      }

      // Check response status with safe property access
      const responseData = response.data;
      const status = responseData.status;

      if (status === 'success') {
        // Safe access to nested data
        const userData = responseData.data;
        if (userData && typeof userData === 'object') {
          productionLogger.info('Profile data retrieved successfully', {
            service: 'userService',
            action: 'getUserProfile',
            userId: userData.user?.id || 'unknown'
          });
          return userData;
        } else {
          throw new Error('Invalid user data structure in response');
        }
      } else {
        const errorMessage = responseData.message || 'Failed to get user profile';
        throw new Error(errorMessage);
      }
    } catch (error: any) {
      // Safe error logging with null checks
      const safeError = error || {};
      const errorDetails = {
        message: safeError.message || 'Unknown error',
        status: safeError.status || 'No status',
        response: safeError.response || 'No response',
        stack: safeError.stack || 'No stack trace',
        name: safeError.name || 'Unknown error type'
      };

      console.log('🔍 [USER_SERVICE] Full error:', errorDetails);

      // Safe logger call
      try {
        if (productionLogger && typeof productionLogger.error === 'function') {
          productionLogger.error('Failed to get user profile', error, 'userService');
        }
      } catch (logError) {
        console.error('Failed to log error:', logError);
      }

      // Provide more specific error messages with safe property access
      const errorStatus = safeError.status;
      const errorName = safeError.name;
      const errorMessage = safeError.message || 'Unknown error';

      if (errorStatus === 401) {
        throw new Error('Authentication required. Please login again.');
      } else if (errorStatus === 404) {
        throw new Error('User profile not found.');
      } else if (errorName === 'AbortError' || errorMessage === 'Aborted') {
        throw new Error('Request timed out. Please check your connection.');
      } else if (errorMessage && errorMessage.includes('Network')) {
        throw new Error('Network error. Please check your connection.');
      } else {
        // Safe access to nested properties
        const responseMessage = safeError.response?.data?.message;
        throw new Error(responseMessage || errorMessage || 'Failed to get user profile');
      }
    }
  }

  /**
   * Update current user's profile data
   */
  async updateUserProfile(profileData: Partial<UserProfile>): Promise<UserProfileResponse> {
    try {
      productionLogger.info('Updating user profile', { service: 'userService', action: 'updateUserProfile', fieldsCount: Object.keys(profileData).length });
      
      const response = await apiService.put('/user/profile', profileData, {
        timeout: 15000, // 15 seconds timeout for updates
        retries: 1, // 1 retry for updates
      });
      
      productionLogger.info('Profile update response received', { service: 'userService', action: 'updateUserProfile', hasData: !!response?.data });
      
      if (response.data.status === 'success') {
        productionLogger.info('Profile updated successfully', { service: 'userService', action: 'updateUserProfile', userId: response.data.data?.user?.id });
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to update user profile');
      }
    } catch (error: any) {
      productionLogger.error('Failed to update user profile', { service: 'userService', action: 'updateUserProfile', error: error.message, statusCode: error.status });
      
      // Provide more specific error messages
      if (error.status === 401) {
        throw new Error('Authentication required. Please login again.');
      } else if (error.status === 400) {
        throw new Error('Invalid profile data provided.');
      } else if (error.name === 'AbortError' || error.message === 'Aborted') {
        throw new Error('Request timed out. Please try again.');
      } else if (error.message?.includes('Network')) {
        throw new Error('Network error. Please check your connection.');
      } else {
        throw new Error(error.response?.data?.message || error.message || 'Failed to update user profile');
      }
    }
  }

  /**
   * Get current user's balance
   */
  async getUserBalance(): Promise<UserBalanceResponse> {
    try {
      productionLogger.info('Getting user balance', { service: 'userService', action: 'getUserBalance' });
      
      const response = await apiService.get('/user/balance', {}, {
        timeout: 10000, // 10 seconds timeout
        retries: 2, // 2 retries
        cache: true, // Enable caching
        cacheTime: 2 * 60 * 1000, // Cache for 2 minutes (balance changes frequently)
      });
      
      productionLogger.info('Balance response received', { service: 'userService', action: 'getUserBalance', hasData: !!response?.data });
      
      if (response.data.status === 'success') {
        productionLogger.info('Balance data retrieved successfully', { service: 'userService', action: 'getUserBalance', balance: response.data.data?.balance, currency: response.data.data?.currency });
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to get user balance');
      }
    } catch (error: any) {
      productionLogger.error('Failed to get user balance', { service: 'userService', action: 'getUserBalance', error: error.message, statusCode: error.status });
      
      // Provide more specific error messages
      if (error.status === 401) {
        throw new Error('Authentication required. Please login again.');
      } else if (error.name === 'AbortError' || error.message === 'Aborted') {
        throw new Error('Request timed out. Please check your connection.');
      } else if (error.message?.includes('Network')) {
        throw new Error('Network error. Please check your connection.');
      } else {
        throw new Error(error.response?.data?.message || error.message || 'Failed to get user balance');
      }
    }
  }

  /**
   * Refresh user profile data (clears cache and fetches fresh data)
   */
  async refreshUserProfile(): Promise<UserProfileResponse> {
    try {
      productionLogger.info('Refreshing user profile', { service: 'userService', action: 'refreshUserProfile' });
      
      // Force fresh data by disabling cache
      const response = await apiService.get('/user/profile', {}, {
        timeout: 10000,
        retries: 2,
        cache: false, // Disable cache for refresh
      });
      
      if (response.data.status === 'success') {
        productionLogger.info('Profile refreshed successfully', { service: 'userService', action: 'refreshUserProfile', userId: response.data.data?.user?.id });
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to refresh user profile');
      }
    } catch (error: any) {
      productionLogger.error('Failed to refresh user profile', { service: 'userService', action: 'refreshUserProfile', error: error.message });
      throw error; // Re-throw to let caller handle
    }
  }

  /**
   * Generate and set avatar for user using LOCAL assets
   */
  async generateAndSetAvatar(userData: {
    firstName?: string;
    lastName?: string;
    email?: string;
    name?: string;
  }): Promise<string> {
    try {
      productionLogger.info('Setting local avatar for user', {
        hasFirstName: !!(userData?.firstName),
        hasEmail: !!(userData?.email)
      });

      // Detect gender from name
      let isFemaleName = false;
      if (userData?.firstName) {
        const name = userData.firstName.toLowerCase();
        // Simple female name detection
        const femaleNames = ['sarah', 'mary', 'jennifer', 'lisa', 'nancy', 'karen', 'betty', 'helen', 'sandra', 'donna', 'carol', 'ruth', 'sharon', 'michelle', 'laura', 'emily', 'kimberly', 'deborah', 'dorothy', 'amy', 'angela', 'ashley', 'brenda', 'emma', 'olivia', 'cynthia', 'marie', 'janet', 'catherine', 'frances', 'christine', 'samantha', 'debra', 'rachel', 'carolyn', 'janet', 'virginia', 'maria', 'heather', 'diane', 'julie', 'joyce', 'victoria', 'kelly', 'christina', 'joan', 'evelyn', 'judith', 'megan', 'cheryl', 'andrea', 'hannah', 'jacqueline', 'martha', 'gloria', 'teresa', 'sara', 'janice', 'marie', 'julia', 'heather', 'anne', 'alice', 'kathryn', 'louise', 'lori', 'roberta', 'pamela', 'nicole', 'rita', 'anna', 'elizabeth', 'grace', 'rose', 'diana', 'beverly', 'denise', 'marilyn', 'amber', 'danielle', 'abigail', 'brittany', 'aisha', 'fatima', 'amina', 'zainab', 'khadija', 'mariam', 'ngozi', 'chioma', 'adunni', 'bukola', 'funmi', 'kemi', 'shade', 'bisi', 'folake'];
        isFemaleName = femaleNames.includes(name);
      }

      // Select random avatar from appropriate folder
      const avatarNumber = Math.floor(Math.random() * 3) + 1; // 1, 2, or 3
      const avatarFolder = isFemaleName ? 'female' : 'male';
      const localAvatarPath = `assets/${avatarFolder}/${avatarNumber}.png`;

      productionLogger.info('Local avatar selected', {
        localAvatarPath,
        isFemaleName,
        firstName: userData?.firstName
      });

      // Save to profile
      await this.updateUserProfile({
        avatar: localAvatarPath,
        picture: localAvatarPath
      });

      return localAvatarPath;

    } catch (err) {
      productionLogger.warn('Using fallback avatar', { error: String(err) });
      // Fallback to male avatar 1
      const fallbackPath = 'assets/male/1.png';

      try {
        await this.updateUserProfile({
          avatar: fallbackPath,
          picture: fallbackPath
        });
      } catch (updateErr) {
        // Ignore update errors
      }

      return fallbackPath;
    }
  }

  /**
   * Get avatar options for user to choose from with enhanced gender detection
   */
  async getAvatarOptions(userData: {
    firstName?: string;
    lastName?: string;
    email?: string;
    name?: string;
  }): Promise<string[]> {
    try {
      productionLogger.info('Getting avatar options for user with gender detection', {
        service: 'userService',
        action: 'getAvatarOptions',
        hasFirstName: !!userData.firstName,
        hasLastName: !!userData.lastName,
        hasEmail: !!userData.email
      });

      const avatarOptions = await AvatarService.generateAvatarOptions({
        name: userData.firstName || userData.name,
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        size: 200,
        useLocalAvatars: true // Enable local avatars with gender detection
      });

      productionLogger.info('Avatar options generated with gender detection', {
        service: 'userService',
        action: 'getAvatarOptions',
        optionsCount: avatarOptions.length
      });

      return avatarOptions;

    } catch (error: any) {
      productionLogger.error('Failed to get avatar options', {
        service: 'userService',
        action: 'getAvatarOptions',
        error: error instanceof Error ? error.message : (typeof error === 'string' ? error : JSON.stringify(error)),
        errorStack: error instanceof Error ? error.stack : undefined
      });

      // Return fallback avatar as single option
      return [AvatarService.getFallbackAvatar(userData)];
    }
  }

  /**
   * Set user avatar from URL
   */
  async setUserAvatar(avatarUrl: string): Promise<UserProfileResponse> {
    try {
      productionLogger.info('Setting user avatar', {
        service: 'userService',
        action: 'setUserAvatar',
        avatarUrl
      });

      const response = await this.updateUserProfile({
        avatar: avatarUrl,
        picture: avatarUrl // Also set picture field for compatibility
      });

      productionLogger.info('User avatar set successfully', {
        service: 'userService',
        action: 'setUserAvatar'
      });

      return response;

    } catch (error: any) {
      productionLogger.error('Failed to set user avatar', {
        service: 'userService',
        action: 'setUserAvatar',
        error: error.message
      });
      throw error;
    }
  }
}

export const userService = new UserService();