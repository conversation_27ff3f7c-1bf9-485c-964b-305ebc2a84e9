/**
 * User Validation Routes
 * 
 * These routes handle critical security validations to ensure users
 * still exist and have valid permissions, especially important for cached authentication.
 */

const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const authService = require('../services/authService');
const userService = require('../services/userService');
const logger = require('../utils/logger');
const { requireAdmin } = require('../middleware/auth');

/**
 * @route   GET /api/v1/auth/validate-user
 * @desc    Validate if user still exists and has valid permissions
 * @access  Private (requires valid token)
 */
router.get('/validate-user', authService.protect.bind(authService), async (req, res) => {
  const startTime = Date.now();
  const clientIP = req.ip || req.connection.remoteAddress;
  
  try {
    const userId = req.user.id;
    const userIdFromQuery = req.query.userId;
    
    logger.info(`🔍 [USER-VALIDATION] Validating user ${userId} from ${clientIP}`);
    
    // Security check: ensure user can only validate themselves
    if (userIdFromQuery && userIdFromQuery !== userId) {
      logger.warn('UNAUTHORIZED_VALIDATION_ATTEMPT', {
        requestedUserId: userIdFromQuery,
        actualUserId: userId,
        clientIP
      });
      
      return res.status(403).json({
        status: 'error',
        message: 'Unauthorized: Cannot validate other users'
      });
    }

    // Check if user exists in database
    const user = await userService.getUserById(userId);
    if (!user) {
      logger.warn('USER_NOT_FOUND_IN_VALIDATION', { userId, clientIP });
      
      return res.status(200).json({
        status: 'success',
        data: {
          userExists: false,
          isActive: false,
          hasValidToken: false,
          reason: 'user_not_found',
          validationTime: Date.now() - startTime
        }
      });
    }

    // Check if user account is active
    const isActive = user.isActive !== false && !user.isDeleted && !user.isSuspended;
    
    if (!isActive) {
      logger.security('INACTIVE_USER_DETECTED', {
        userId,
        isDeleted: user.isDeleted,
        isSuspended: user.isSuspended,
        isActive: user.isActive,
        clientIP
      });
    }

    // Check if current token is still valid
    // For now, we assume token is valid if user exists and is active
    // In production, you'd check against token revocation lists
    const hasValidToken = isActive;

    if (!hasValidToken) {
      logger.security('INVALID_TOKEN_DETECTED', { userId, clientIP });
    }

    // Get user permissions for comparison
    const permissions = {
      canAccessApp: isActive && hasValidToken,
      setupComplete: user.setupComplete || false,
      hasProfileSetup: !!user.firstName,
      hasPinSetup: !!user.pin,
      hasBiometricSetup: user.biometricEnabled || false,
      isEmailVerified: user.isEmailVerified || false,
      isPhoneVerified: user.isPhoneVerified || false,
      lastLoginAt: user.lastLoginAt,
      accountCreatedAt: user.createdAt
    };

    // Log successful validation
    logger.info(`✅ [USER-VALIDATION] User validation completed for ${userId}`, {
      userExists: true,
      isActive,
      hasValidToken,
      duration: Date.now() - startTime
    });

    // Return validation result
    res.status(200).json({
      status: 'success',
      data: {
        userExists: true,
        isActive,
        hasValidToken,
        permissions,
        validationTime: Date.now() - startTime,
        serverTime: new Date().toISOString()
      }
    });

  } catch (error) {
    logger.error(`❌ [USER-VALIDATION] Validation failed for user`, {
      error: error.message,
      stack: error.stack,
      clientIP,
      duration: Date.now() - startTime
    });

    res.status(500).json({
      status: 'error',
      message: 'User validation failed',
      validationTime: Date.now() - startTime
    });
  }
});

/**
 * @route   POST /api/v1/auth/validate-batch
 * @desc    Validate multiple users at once (admin only)
 * @access  Private (admin only)
 */
router.post('/validate-batch',
  authService.protect.bind(authService),
  requireAdmin,
  [
    body('userIds').isArray().withMessage('userIds must be an array'),
    body('userIds.*').isString().withMessage('Each userId must be a string')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { userIds } = req.body;
      const adminUserId = req.user.id;
      const clientIP = req.ip || req.connection.remoteAddress;

      logger.info(`🔍 [BATCH-VALIDATION] Admin ${adminUserId} validating ${userIds.length} users from ${clientIP}`);

      const validationResults = [];

      for (const userId of userIds) {
        try {
          const user = await userService.getUserById(userId);
          const userExists = !!user;
          const isActive = user ? (user.isActive !== false && !user.isDeleted && !user.isSuspended) : false;
          
          validationResults.push({
            userId,
            userExists,
            isActive,
            lastLoginAt: user?.lastLoginAt || null,
            accountStatus: user?.isDeleted ? 'deleted' : user?.isSuspended ? 'suspended' : isActive ? 'active' : 'inactive'
          });
        } catch (userError) {
          validationResults.push({
            userId,
            userExists: false,
            isActive: false,
            error: 'Validation failed'
          });
        }
      }

      logger.info(`✅ [BATCH-VALIDATION] Batch validation completed`, {
        adminUserId,
        totalUsers: userIds.length,
        validUsers: validationResults.filter(r => r.userExists).length,
        activeUsers: validationResults.filter(r => r.isActive).length
      });

      res.status(200).json({
        status: 'success',
        data: {
          validationResults,
          summary: {
            totalUsers: userIds.length,
            validUsers: validationResults.filter(r => r.userExists).length,
            activeUsers: validationResults.filter(r => r.isActive).length,
            deletedUsers: validationResults.filter(r => r.accountStatus === 'deleted').length,
            suspendedUsers: validationResults.filter(r => r.accountStatus === 'suspended').length
          }
        }
      });

    } catch (error) {
      logger.error(`❌ [BATCH-VALIDATION] Batch validation failed`, error);
      res.status(500).json({
        status: 'error',
        message: 'Batch validation failed'
      });
    }
  }
);

/**
 * @route   POST /api/v1/auth/invalidate-user-cache
 * @desc    Force invalidate user cache (admin only)
 * @access  Private (admin only)
 */
router.post('/invalidate-user-cache',
  authService.protect.bind(authService),
  requireAdmin,
  [
    body('userId').isString().withMessage('userId is required'),
    body('reason').optional().isString().withMessage('reason must be a string')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { userId, reason } = req.body;
      const adminUserId = req.user.id;
      const clientIP = req.ip || req.connection.remoteAddress;

      logger.security('ADMIN_CACHE_INVALIDATION', {
        adminUserId,
        targetUserId: userId,
        reason: reason || 'No reason provided',
        clientIP
      });

      // In a real implementation, you might:
      // 1. Add the user to a cache invalidation list
      // 2. Send a push notification to force app refresh
      // 3. Revoke specific tokens
      // 4. Update a cache invalidation timestamp

      // For now, we'll just log the action
      logger.info(`🗑️ [CACHE-INVALIDATION] Admin ${adminUserId} invalidated cache for user ${userId}`, {
        reason,
        timestamp: new Date().toISOString()
      });

      res.status(200).json({
        status: 'success',
        message: 'User cache invalidation requested',
        data: {
          userId,
          invalidatedAt: new Date().toISOString(),
          invalidatedBy: adminUserId,
          reason
        }
      });

    } catch (error) {
      logger.error(`❌ [CACHE-INVALIDATION] Cache invalidation failed`, error);
      res.status(500).json({
        status: 'error',
        message: 'Cache invalidation failed'
      });
    }
  }
);

/**
 * @route   GET /api/v1/auth/validation-stats
 * @desc    Get validation statistics (admin only)
 * @access  Private (admin only)
 */
router.get('/validation-stats',
  authService.protect.bind(authService),
  requireAdmin,
  async (req, res) => {
    try {
      const adminUserId = req.user.id;
      
      // Get validation statistics from the last 24 hours
      // For now, return basic stats - implement detailed stats later
      const stats = {
        totalValidations: 0,
        successfulValidations: 0,
        failedValidations: 0,
        deletedUsersDetected: 0,
        suspendedUsersDetected: 0,
        revokedTokensDetected: 0,
        lastValidationTime: new Date().toISOString()
      };
      
      logger.info(`📊 [VALIDATION-STATS] Admin ${adminUserId} requested validation statistics`);

      res.status(200).json({
        status: 'success',
        data: {
          stats,
          generatedAt: new Date().toISOString(),
          generatedBy: adminUserId
        }
      });

    } catch (error) {
      logger.error(`❌ [VALIDATION-STATS] Failed to get validation statistics`, error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to get validation statistics'
      });
    }
  }
);

module.exports = router;
