import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList, Image } from 'react-native';
import ModalBase from './ModalBase';

const PRODUCTS = [
  { key: 'airtime', label: 'Airtime to Cash', color: '#B2F0E6', icon: require('../../assets/icons/change.png'), isImage: true },
  { key: 'virtualcard', label: 'Virtual Dollar', color: '#D6F5B0', icon: require('../../assets/icons/credit-card.png'), isImage: true },
  { key: 'cable', label: 'Cable/TV', color: '#FFD6B0', icon: require('../../assets/icons/smart-tv.png'), isImage: true },
  { key: 'betting', label: 'Betting', color: '#B0C6FF', icon: require('../../assets/icons/betting.png'), isImage: true },
  { key: 'voucher', label: 'Agent', color: '#B0FFD6', icon: require('../../assets/icons/employee.png'), isImage: true },
  { key: 'giftcard', label: 'Gift Card', color: '#E6B0FF', icon: require('../../assets/icons/gift-cardd.png'), isImage: true },
];

interface MoreProductsModalProps {
  visible: boolean;
  onClose: () => void;
  onSuggest: () => void;
}

const MoreProductsModal: React.FC<MoreProductsModalProps> = ({ visible, onClose, onSuggest }) => {
  const [giftCardImage, setGiftCardImage] = useState(PRODUCTS[5].icon);

  return (
    <ModalBase visible={visible} onClose={onClose} variant="slideUp" heightPercent={70}>
      <Text style={styles.title}>More Products</Text>
      <Text style={styles.subtitle}>Need to do more? Here is more.</Text>
<FlatList
        getItemLayout={(data, index) => ({ length: 100, offset: 100 * index, index })}
        windowSize={3}
        data={PRODUCTS}
        numColumns={3}
        keyExtractor={item => item.key}
        contentContainerStyle={styles.grid}
        renderItem={({ item }) => {
          let icon = item.icon;
          if (item.key === 'giftcard') {
            icon = giftCardImage;
          }
          return (
            <View style={styles.productItem}>
              <View
                style={[
                  styles.iconCircle,
                  item.isImage && item.key !== 'giftcard' ? null : (!item.isImage ? { backgroundColor: item.color } : null),
                ]}
              >
                {item.isImage ? (
                  <Image source={icon} style={styles.iconImage} resizeMode="contain" />
                ) : (
                  <Text style={styles.iconText}>{item.icon}</Text>
                )}
              </View>
              <Text style={styles.productLabel}>{item.label}</Text>
            </View>
          );
        }}
      />
      <View style={styles.bottomSection}>
        <Text style={styles.bottomText}>Can't find what you're looking for?</Text>
        <TouchableOpacity style={styles.suggestButton} onPress={onSuggest} activeOpacity={0.8}>
          <Text style={styles.suggestButtonText}>Suggest a Product</Text>
        </TouchableOpacity>
      </View>
    </ModalBase>
  );
};

const styles = StyleSheet.create({
  title: {
    fontSize: 22,
    fontWeight: '700',
    color: '#fff',
    marginBottom: 4,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: '#bdbdbd',
    marginBottom: 18,
    textAlign: 'center',
  },
  grid: {
    alignItems: 'center',
    marginBottom: 18,
    gap: 24, // add vertical gap between rows (if supported)
    paddingHorizontal: 12, // add horizontal padding
  },
  productItem: {
    width: 80, // increased from 70
    alignItems: 'center',
    marginBottom: 18,
    marginHorizontal: 12, // increased for better spacing
  },
  iconCircle: {
    width: 48, // increased from 40
    height: 48, // increased from 40
    borderRadius: 24, // adjusted for new size
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 6,
    // backgroundColor removed, now set inline per item
  },
  iconImage: {
    width: 28,
    height: 28,
  },
  iconText: {
    fontSize: 26, // increased from 22
  },
  productLabel: {
    color: '#fff',
    fontSize: 13,
    textAlign: 'center',
  },
  bottomSection: {
    alignItems: 'center',
    marginTop: 8,
    width: '100%',
  },
  bottomText: {
    color: '#bdbdbd',
    fontSize: 13,
    marginBottom: 8,
    textAlign: 'center',
  },
  suggestButton: {
    backgroundColor: '#232323',
    borderRadius: 18,
    paddingVertical: 14,
    paddingHorizontal: 32,
    marginTop: 0,
  },
  suggestButtonText: {
    color: '#fff',
    fontSize: 15,
    fontWeight: '600',
  },
});

export default MoreProductsModal;
