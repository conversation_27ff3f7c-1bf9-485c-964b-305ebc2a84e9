const crypto = require('crypto');
const logger = require('../utils/logger');

class OTPService {
  constructor() {
    // In-memory storage for OTPs (in production, use Redis or database)
    this.otpStore = new Map();
    this.maxAttempts = 3;
    this.otpExpiry = 5 * 60 * 1000; // 5 minutes
  }

  /**
   * Generate OTP for user
   * @param {string} userId - User ID
   * @param {string} purpose - Purpose of OTP (verification, reset, pin_reset, etc.)
   * @returns {string} - Generated OTP
   */
  async generateOTP(userId, purpose = 'verification') {
    try {
      console.log(`🔐 [OTP] Generating OTP for user ${userId}, purpose: ${purpose}`);

      // Generate 4-digit OTP
      const otp = crypto.randomInt(1000, 9999).toString();
      
      // Create OTP record
      const otpRecord = {
        otp: otp,
        purpose: purpose,
        userId: userId,
        attempts: 0,
        createdAt: Date.now(),
        expiresAt: Date.now() + this.otpExpiry
      };

      // Store OTP with composite key (userId + purpose)
      const otpKey = `${userId}_${purpose}`;
      this.otpStore.set(otpKey, otpRecord);

      console.log(`✅ [OTP] OTP generated successfully: ${otp} (expires in 5 minutes)`);
      
      // Clean up expired OTPs periodically
      this.cleanupExpiredOTPs();

      return otp;
    } catch (error) {
      console.log('💥 [OTP] Generate OTP error:', error);
      logger.error('Generate OTP error:', error);
      throw error;
    }
  }

  /**
   * Verify OTP
   * @param {string} userId - User ID
   * @param {string} otp - OTP to verify
   * @param {string} purpose - Purpose of OTP
   * @returns {boolean} - Whether OTP is valid
   */
  async verifyOTP(userId, otp, purpose = 'verification') {
    try {
      console.log(`🔍 [OTP] Verifying OTP for user ${userId}, purpose: ${purpose}`);

      const otpKey = `${userId}_${purpose}`;
      const otpRecord = this.otpStore.get(otpKey);

      if (!otpRecord) {
        console.log('❌ [OTP] No OTP found for user and purpose');
        return false;
      }

      // Check if OTP has expired
      if (Date.now() > otpRecord.expiresAt) {
        console.log('❌ [OTP] OTP has expired');
        this.otpStore.delete(otpKey);
        return false;
      }

      // Check if max attempts exceeded
      if (otpRecord.attempts >= this.maxAttempts) {
        console.log('❌ [OTP] Max verification attempts exceeded');
        this.otpStore.delete(otpKey);
        return false;
      }

      // Increment attempt count
      otpRecord.attempts++;

      // Verify OTP
      if (otpRecord.otp !== otp) {
        console.log(`❌ [OTP] Invalid OTP. Attempt ${otpRecord.attempts}/${this.maxAttempts}`);
        
        // Delete if max attempts reached
        if (otpRecord.attempts >= this.maxAttempts) {
          this.otpStore.delete(otpKey);
        }
        
        return false;
      }

      // OTP is valid - remove it from store
      this.otpStore.delete(otpKey);
      console.log('✅ [OTP] OTP verified successfully');
      
      return true;
    } catch (error) {
      console.log('💥 [OTP] Verify OTP error:', error);
      logger.error('Verify OTP error:', error);
      return false;
    }
  }

  /**
   * Resend OTP (generates new OTP)
   * @param {string} userId - User ID
   * @param {string} purpose - Purpose of OTP
   * @returns {string} - New OTP
   */
  async resendOTP(userId, purpose = 'verification') {
    try {
      console.log(`🔄 [OTP] Resending OTP for user ${userId}, purpose: ${purpose}`);

      // Remove existing OTP
      const otpKey = `${userId}_${purpose}`;
      this.otpStore.delete(otpKey);

      // Generate new OTP
      return await this.generateOTP(userId, purpose);
    } catch (error) {
      console.log('💥 [OTP] Resend OTP error:', error);
      logger.error('Resend OTP error:', error);
      throw error;
    }
  }

  /**
   * Check if OTP exists for user and purpose
   * @param {string} userId - User ID
   * @param {string} purpose - Purpose of OTP
   * @returns {boolean} - Whether OTP exists
   */
  async hasValidOTP(userId, purpose = 'verification') {
    const otpKey = `${userId}_${purpose}`;
    const otpRecord = this.otpStore.get(otpKey);
    
    if (!otpRecord) {
      return false;
    }

    // Check if expired
    if (Date.now() > otpRecord.expiresAt) {
      this.otpStore.delete(otpKey);
      return false;
    }

    return true;
  }

  /**
   * Get remaining attempts for OTP
   * @param {string} userId - User ID
   * @param {string} purpose - Purpose of OTP
   * @returns {number} - Remaining attempts
   */
  async getRemainingAttempts(userId, purpose = 'verification') {
    const otpKey = `${userId}_${purpose}`;
    const otpRecord = this.otpStore.get(otpKey);
    
    if (!otpRecord) {
      return 0;
    }

    return Math.max(0, this.maxAttempts - otpRecord.attempts);
  }

  /**
   * Clean up expired OTPs
   */
  cleanupExpiredOTPs() {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, record] of this.otpStore.entries()) {
      if (now > record.expiresAt) {
        this.otpStore.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 [OTP] Cleaned up ${cleanedCount} expired OTPs`);
    }
  }

  /**
   * Get OTP statistics
   * @returns {Object} - OTP statistics
   */
  getStats() {
    const now = Date.now();
    let activeOTPs = 0;
    let expiredOTPs = 0;

    for (const record of this.otpStore.values()) {
      if (now > record.expiresAt) {
        expiredOTPs++;
      } else {
        activeOTPs++;
      }
    }

    return {
      total: this.otpStore.size,
      active: activeOTPs,
      expired: expiredOTPs
    };
  }

  /**
   * Clear all OTPs for a user
   * @param {string} userId - User ID
   */
  async clearUserOTPs(userId) {
    let clearedCount = 0;
    
    for (const [key, record] of this.otpStore.entries()) {
      if (record.userId === userId) {
        this.otpStore.delete(key);
        clearedCount++;
      }
    }

    console.log(`🧹 [OTP] Cleared ${clearedCount} OTPs for user ${userId}`);
  }
}

// Create singleton instance
const otpService = new OTPService();

// Cleanup expired OTPs every 5 minutes
setInterval(() => {
  otpService.cleanupExpiredOTPs();
}, 5 * 60 * 1000);

module.exports = otpService;
