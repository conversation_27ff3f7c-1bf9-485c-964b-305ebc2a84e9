# Stallion OTA Updates Setup Guide

This guide explains how to use React Native Stallion for Over-The-Air (OTA) updates in the Vendy app.

## 🚀 What is Stallion?

Stallion is a fully managed OTA update platform for React Native that allows you to:
- Deploy JavaScript bundle updates without app store releases
- Test updates before production deployment
- Automatically rollback failed updates
- Track update analytics and adoption rates

## 📦 Installation

The Stallion package is already installed in this project:

```bash
npm install react-native-stallion
```

## 🔧 Configuration

### 1. Stallion Config File

The `stallion.config.js` file in the project root contains all configuration:

```javascript
module.exports = {
  appName: 'payvendy',
  appVersion: '1.0.0',
  buildNumber: 1,
  checkOnStart: true,
  checkOnResume: true,
  autoUpdate: true,
  installMandatoryImmediately: true,
  enableRollback: true,
  rollbackThreshold: 3,
  debug: __DEV__,
};
```

### 2. App Integration

The app is already wrapped with the Stallion provider in `App.tsx`:

```tsx
import { Stallion } from 'react-native-stallion';

const App = () => {
  return (
    <Stallion>
      {/* Your app content */}
    </Stallion>
  );
};
```

### 3. OTA Service

The `stallionOTAService` handles all update logic:
- Automatic update checks on app start and resume
- Update progress tracking
- Error handling and rollbacks
- Event notifications

### 4. Update Modal

The `StallionUpdateModal` component provides a user-friendly interface for:
- Showing update availability
- Displaying download progress
- Handling mandatory vs optional updates
- Error states and retry functionality

## 🎯 How It Works

### Automatic Updates
1. App checks for updates on startup and when returning to foreground
2. If an update is available, the modal appears
3. User can choose to install (optional) or update is installed automatically (mandatory)
4. App restarts with the new bundle

### Manual Updates
You can also trigger updates programmatically:

```typescript
import stallionOTAService from './src/services/stallionOTAService';

// Check for updates
const updateInfo = await stallionOTAService.checkForUpdates();

// Download and install
if (updateInfo?.isAvailable) {
  await stallionOTAService.downloadAndInstall();
}
```

## 🚀 Publishing Updates

### 1. Sign Up with Stallion
First, create an account at [https://console.stalliontech.io/](https://console.stalliontech.io/)

### 2. Install Stallion CLI
```bash
npm install -g @stallion-tech/cli
```

### 3. Login to Stallion
```bash
stallion login
```

### 4. Initialize Your App
```bash
stallion init
```

### 5. Push Updates
```bash
# For Android
stallion push --platform android --message "Bug fixes and improvements"

# For iOS
stallion push --platform ios --message "Bug fixes and improvements"

# For both platforms
stallion push --message "Bug fixes and improvements"
```

### 6. Advanced Push Options
```bash
# Mandatory update
stallion push --mandatory --message "Critical security update"

# Gradual rollout (10% of users)
stallion push --rollout 10 --message "Testing new feature"

# Target specific app version
stallion push --target-version "1.0.0" --message "Update for v1.0.0 users"
```

## 📊 Update Analytics

Stallion provides detailed analytics through their console:
- Update adoption rates
- Download success/failure rates
- Rollback statistics
- User engagement metrics

## 🔄 Rollback Strategy

### Automatic Rollbacks
- Stallion automatically detects app crashes after updates
- If crashes exceed the threshold (3 by default), it rolls back
- Users get the previous stable version automatically

### Manual Rollbacks
You can manually rollback updates through the Stallion console or CLI:

```bash
stallion rollback --version "1.0.1"
```

## 🛠️ Development Workflow

### 1. Development
- Make your changes to the React Native code
- Test thoroughly in development

### 2. Testing
- Use Stallion's testing framework to validate updates
- Test on different devices and OS versions

### 3. Staging
- Push to a staging environment first
- Use gradual rollout to test with a small user base

### 4. Production
- Push to all users once validated
- Monitor analytics for any issues

## 🔧 Troubleshooting

### Common Issues

1. **Update not appearing**
   - Check your app version matches the target version
   - Verify network connectivity
   - Check Stallion console for deployment status

2. **Update fails to install**
   - Check device storage space
   - Verify bundle integrity
   - Check for JavaScript errors in the new bundle

3. **App crashes after update**
   - Stallion will automatically rollback after 3 crashes
   - Check crash logs in your monitoring service
   - Test updates thoroughly before deployment

### Debug Mode
Enable debug logging in `stallion.config.js`:

```javascript
module.exports = {
  debug: true, // Enable for development
  // ... other config
};
```

## 📚 Additional Resources

- [Stallion Documentation](https://learn.stalliontech.io)
- [Stallion Console](https://console.stalliontech.io/)
- [GitHub Repository](https://github.com/stallion-tech/react-native-stallion)

## 🔐 Security

- All updates are signed and verified
- SSL/TLS encryption for all communications
- Bundle integrity checks before installation
- Automatic rollback on security issues

## 💡 Best Practices

1. **Always test updates thoroughly** before pushing to production
2. **Use gradual rollouts** for major changes
3. **Monitor analytics** after each deployment
4. **Keep rollback plans ready** for critical issues
5. **Version your updates properly** for easy tracking
6. **Test on multiple devices** and OS versions
7. **Use meaningful commit messages** for update tracking

## 🎉 Benefits

- ✅ **Instant deployments** - No app store approval needed
- ✅ **Reduced time to market** - Fix bugs and add features quickly
- ✅ **Better user experience** - Users get updates automatically
- ✅ **Risk mitigation** - Automatic rollbacks prevent major issues
- ✅ **Analytics insights** - Track update performance and adoption
- ✅ **Cost effective** - Reduce development and deployment costs
