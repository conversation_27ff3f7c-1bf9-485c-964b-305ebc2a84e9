/**
 * Shared user-related type definitions
 * Consolidates UserData interfaces from across the codebase
 */

// Base user data interface used throughout the app
export interface UserData {
  id: string;
  email?: string;
  phoneNumber?: string;
  firstName?: string;
  lastName?: string;
  authMethod: 'email' | 'phone' | 'google';
  isNewUser?: boolean;
  isEmailVerified?: boolean;
  isPhoneVerified?: boolean;
  pin?: string;
  avatar?: string;
  picture?: string;
  balance?: number;
  createdAt?: string;
  updatedAt?: string;
  
  // Setup-related fields
  pinVerifiedInSession?: boolean; // Track if PIN was verified in this session
  hasPinSetup?: boolean;
  hasBiometricSetup?: boolean;
  hasProfileSetup?: boolean;
  setupComplete?: boolean;
  
  // Allow additional fields for flexibility
  [key: string]: any;
}

// User profile interface for profile-related operations
export interface UserProfile {
  id: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  picture?: string;
  avatar?: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  balance: number;
  authProvider?: string;
  createdAt?: string;
}

// Setup status interface
export interface SetupStatus {
  hasPinSetup: boolean;
  hasBiometricSetup: boolean;
  hasProfileSetup: boolean;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  setupComplete: boolean;
}

// User preferences interface
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  currency: string;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
}

export interface NotificationSettings {
  push: boolean;
  email: boolean;
  marketing: boolean;
  security: boolean;
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'private';
  showOnlineStatus: boolean;
  allowDataCollection: boolean;
}

// Extended user interface for store
export interface User {
  id: string;
  email?: string;
  phoneNumber?: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  isVerified: boolean;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  createdAt: string;
  updatedAt: string;
  preferences: UserPreferences;
  profile: UserProfileDetails;
}

export interface UserProfileDetails {
  bio?: string;
  dateOfBirth?: string;
  gender?: string;
  location?: string;
  occupation?: string;
}

// Response interfaces
export interface UserProfileResponse {
  user: UserProfile;
}

export interface UserBalanceResponse {
  balance: number;
  currency: string;
}

export interface SetupStatusResponse {
  setupStatus: SetupStatus;
  user: UserData;
}

// Transaction and Balance interfaces
export interface Transaction {
  id: string;
  type: 'airtime' | 'data' | 'bills' | 'transfer' | 'deposit' | 'withdrawal';
  amount: number;
  currency: string;
  status: 'pending' | 'success' | 'failed' | 'cancelled';
  description: string;
  reference: string;
  metadata: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface Balance {
  available: number;
  pending: number;
  currency: string;
  lastUpdated: string;
}
