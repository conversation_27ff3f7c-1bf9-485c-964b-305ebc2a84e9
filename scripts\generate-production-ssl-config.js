#!/usr/bin/env node

/**
 * Production SSL Certificate Configuration Generator
 * Generates SSL certificate hashes for production domains
 */

const https = require('https');
const crypto = require('crypto');
const fs = require('fs');
const path = require('path');

// Production domains to configure
const productionDomains = [
  'api.vendy.com',
  'staging-api.vendy.com', 
  'supabase.co',
  // Add more production domains here
];

// Common certificate authorities and their hashes
const commonCertificateAuthorities = {
  'Let\'s Encrypt': {
    'ISRG Root X1': 'C5+lpZ7tcVwmwQIMcRtPbsQtWLABXhQzejna0wHFr8M=',
    'Let\'s Encrypt R3': 'jQJTbIh0grw0/1TkHSumWb+Fs0Ggogr621gT3PvPKG0=',
    'Let\'s Encrypt E1': 'J2/oqMTsdhFWW/n85tys6b4yDBtb6idZayIEBx7QTxA=',
  },
  'DigiCert': {
    'DigiCert Global Root G2': 'i7WTqTvh0OioIruIfFR4kMPnBqrS2rdiVPl/s2uC/CY=',
    'DigiCert TLS RSA SHA256 2020 CA1': 'VhQGmi/XwBuHqznoFHFHUGONNXBPkINm4fZfQTvTRHU=',
  },
  'Cloudflare': {
    'Cloudflare Inc ECC CA-3': 'gkehGfSbEgVOjDfzMvqt6X7F+MyVWx/1VihuqVvpW5s=',
  },
  'Amazon': {
    'Amazon Root CA 1': 'VjLZe/p3W/PJnd6lL8JVNBCGQBZynFLdZSTIqcO0SJ8=',
  }
};

/**
 * Get SSL certificate information for a domain
 */
function getCertificateInfo(hostname) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: hostname,
      port: 443,
      method: 'GET',
      rejectUnauthorized: false,
      timeout: 10000,
    };

    const req = https.request(options, (res) => {
      const cert = res.socket.getPeerCertificate(true);
      
      if (!cert || Object.keys(cert).length === 0) {
        reject(new Error('No certificate found'));
        return;
      }

      // Get certificate chain
      const certChain = [];
      let currentCert = cert;
      
      while (currentCert) {
        if (currentCert.raw) {
          const publicKeyDer = currentCert.pubkey;
          const publicKeyHash = crypto.createHash('sha256').update(publicKeyDer).digest('base64');
          
          certChain.push({
            subject: currentCert.subject?.CN || 'Unknown',
            issuer: currentCert.issuer?.CN || 'Unknown',
            publicKeyHash: publicKeyHash,
            validFrom: currentCert.valid_from,
            validTo: currentCert.valid_to,
            fingerprint: currentCert.fingerprint,
          });
        }
        
        currentCert = currentCert.issuerCertificate;
        if (currentCert === cert) break; // Prevent infinite loop
      }
      
      resolve({
        hostname: hostname,
        certChain: certChain,
        leafCert: certChain[0],
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

/**
 * Generate SSL pinning configuration
 */
function generateSSLConfig(certResults) {
  const config = {
    certificateHashes: {},
    sslPinningServicePins: [],
  };

  certResults.forEach(result => {
    if (result.success) {
      const { hostname, certChain, leafCert } = result.data;
      
      // Get primary and backup hashes
      const hashes = certChain.slice(0, 3).map(cert => cert.publicKeyHash);
      
      // Add to certificate hashes config
      config.certificateHashes[hostname] = hashes;
      
      // Add to SSL pinning service pins
      config.sslPinningServicePins.push({
        hostname: hostname,
        sha256Hashes: hashes.slice(0, 2), // Primary and intermediate
        backupHashes: hashes.slice(2), // Additional backups
      });
    }
  });

  return config;
}

/**
 * Generate configuration files
 */
function generateConfigFiles(config) {
  // Generate environment.ts update
  const envConfig = `
// Updated SSL Certificate Pinning Configuration
export const SSL_PINNING_CONFIG = {
  enabled: ENV_CONFIG.ENABLE_SSL_PINNING,
  domains: ENV_CONFIG.SSL_PINNING_DOMAINS,
  failOnMismatch: true,
  allowBackupPins: true,
  certificateHashes: ${JSON.stringify(config.certificateHashes, null, 4)},
};`;

  // Generate SSL pinning service update
  const sslServiceConfig = `
// Updated SSL Pinning Service Configuration
const productionPins = ${JSON.stringify(config.sslPinningServicePins, null, 2)};`;

  return {
    envConfig,
    sslServiceConfig,
  };
}

/**
 * Main function
 */
async function main() {
  console.log('🔐 Production SSL Certificate Configuration Generator');
  console.log('===================================================\n');

  const results = [];

  for (const domain of productionDomains) {
    try {
      console.log(`🔍 Checking ${domain}...`);
      const certInfo = await getCertificateInfo(domain);
      
      console.log(`✅ Certificate found for ${domain}`);
      console.log(`   Leaf Certificate: ${certInfo.leafCert.subject}`);
      console.log(`   Issuer: ${certInfo.leafCert.issuer}`);
      console.log(`   Valid Until: ${certInfo.leafCert.validTo}`);
      console.log(`   Certificate Chain Length: ${certInfo.certChain.length}`);
      console.log(`   Primary Hash: ${certInfo.leafCert.publicKeyHash}`);
      console.log('');
      
      results.push({
        domain,
        success: true,
        data: certInfo,
      });
      
    } catch (error) {
      console.log(`❌ Failed to get certificate for ${domain}: ${error.message}`);
      console.log('   This domain may not exist yet or may not be accessible');
      console.log('');
      
      results.push({
        domain,
        success: false,
        error: error.message,
      });
    }
  }

  // Generate configuration for successful domains
  const successfulResults = results.filter(r => r.success);
  
  if (successfulResults.length > 0) {
    console.log('📋 Generating SSL Configuration...');
    const config = generateSSLConfig(successfulResults);
    const configFiles = generateConfigFiles(config);
    
    // Save configuration files
    const outputDir = path.join(__dirname, '..', 'ssl-config-output');
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    fs.writeFileSync(
      path.join(outputDir, 'environment-ssl-config.ts'),
      configFiles.envConfig
    );
    
    fs.writeFileSync(
      path.join(outputDir, 'ssl-service-config.ts'),
      configFiles.sslServiceConfig
    );
    
    console.log(`✅ Configuration files generated in: ${outputDir}`);
    console.log('');
  }

  // Show summary
  console.log('📊 Summary');
  console.log('==========');
  console.log(`Total domains checked: ${productionDomains.length}`);
  console.log(`Successful: ${successfulResults.length}`);
  console.log(`Failed: ${results.filter(r => !r.success).length}`);
  console.log('');
  
  if (successfulResults.length > 0) {
    console.log('🔧 Next Steps:');
    console.log('1. Review generated configuration files');
    console.log('2. Update src/config/environment.ts with new certificate hashes');
    console.log('3. Update src/services/sslPinningService.ts with new pins');
    console.log('4. Test SSL pinning with production domains');
  } else {
    console.log('⚠️  No certificates were retrieved successfully.');
    console.log('   Make sure production domains are accessible and have valid SSL certificates.');
  }
  
  console.log('');
  console.log('📝 Note: Certificate hashes change when certificates are renewed!');
  console.log('   Set up monitoring for certificate expiration and renewal.');
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { getCertificateInfo, generateSSLConfig };
