/**
 * @format
 */

import 'react-native-gesture-handler';
import {AppRegistry} from 'react-native';
import {name as appName} from './app.json';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

// Import App component
import App from './App';

// Import Firebase config (but don't initialize manually since auto-init is now enabled)
// import { initFirebase } from './src/config/initFirebase';

// Firebase will auto-initialize from google-services.json via FirebaseInitProvider
// Manual initialization disabled to prevent conflicts
// initFirebase().catch(error => {
//   console.warn('[Index] Firebase initialization failed at startup:', error.message);
//   // Continue app startup even if Firebase fails
// });

AppRegistry.registerComponent(appName, () => (props) => (
  <GestureHandlerRootView style={{ flex: 1 }}>
    <App {...props} />
  </GestureHandlerRootView>
));
