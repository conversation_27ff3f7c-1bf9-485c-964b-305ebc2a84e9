/**
 * Run Avatar Column Migration
 * Adds avatar column to users table for local avatar support
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

async function runAvatarMigration() {
  try {
    console.log('🚀 Starting avatar column migration...');
    
    // Create Supabase client with service role key
    const supabase = createClient(supabaseUrl, supabaseServiceKey);
    
    // Read migration SQL
    const migrationPath = path.join(__dirname, '../migrations/add_avatar_column.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('📄 Executing migration SQL...');
    
    // Execute the migration
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    });
    
    if (error) {
      // Try direct SQL execution if RPC doesn't work
      console.log('⚠️  RPC failed, trying direct SQL execution...');
      
      // Split SQL into individual statements
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
      
      for (const statement of statements) {
        if (statement.includes('DO $$')) {
          // Handle DO blocks specially
          const { error: blockError } = await supabase.rpc('exec_sql', {
            sql: statement + ';'
          });
          if (blockError) {
            console.log('⚠️  DO block failed, trying manual column addition...');
            
            // Manually add avatar column
            const { error: alterError } = await supabase.rpc('exec_sql', {
              sql: 'ALTER TABLE users ADD COLUMN IF NOT EXISTS avatar TEXT;'
            });
            
            if (alterError) {
              console.error('❌ Failed to add avatar column:', alterError);
              throw alterError;
            }
          }
        } else if (statement.trim()) {
          const { error: stmtError } = await supabase.rpc('exec_sql', {
            sql: statement + ';'
          });
          if (stmtError) {
            console.warn('⚠️  Statement failed:', statement, stmtError);
          }
        }
      }
    }
    
    console.log('✅ Avatar column migration completed successfully!');
    
    // Verify the column was added
    const { data: columns, error: verifyError } = await supabase
      .from('information_schema.columns')
      .select('column_name')
      .eq('table_name', 'users')
      .eq('column_name', 'avatar');
    
    if (verifyError) {
      console.warn('⚠️  Could not verify column creation:', verifyError);
    } else if (columns && columns.length > 0) {
      console.log('✅ Avatar column verified in database');
    } else {
      console.log('⚠️  Avatar column not found, but migration may have succeeded');
    }
    
    console.log('🎉 Migration completed!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
runAvatarMigration();
