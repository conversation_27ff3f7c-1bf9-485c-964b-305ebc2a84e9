import React, { useState, useRef, useEffect, memo, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
  Alert,
  Animated,
  StatusBar,
  ImageBackground,
  BackHandler,
  AppState,
} from 'react-native';

import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../../types/navigation';
import { useTheme } from '../../components/ThemeContext';
import logger from '../../services/productionLogger';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import { apiService } from '../../services/apiService';
import GlassyBox from '../../components/GlassyBox';


type OtpVerificationScreenNavigationProp = StackNavigationProp<RootStackParamList, 'OtpVerification'>;

const OtpVerificationScreen: React.FC = memo(() => {
  const navigation = useNavigation<OtpVerificationScreenNavigationProp>();
  const route = useRoute();
  const { email, userData } = route.params as { email?: string; userData?: any };
  const { theme, isDark } = useTheme();

  const [code, setCode] = useState(['', '', '', '']);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [resendCooldown, setResendCooldown] = useState(0);
  const [canResend, setCanResend] = useState(false);
  const [isBackButtonPressed, setIsBackButtonPressed] = useState(false);
  const [isResendPressed, setIsResendPressed] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<'idle' | 'success' | 'error' | 'verifying'>('idle');
  const [appState, setAppState] = useState(AppState.currentState);
  const [hasLeftToCheckEmail, setHasLeftToCheckEmail] = useState(false);

  // Animation values
  const errorShakeAnim = useRef(new Animated.Value(0)).current;
  const bounceAnim = useRef(new Animated.Value(1)).current;

  // Input refs
  const inputRefs = useRef<TextInput[]>([]);

  useEffect(() => {
    // Auto-focus first input
    setTimeout(() => {
      inputRefs.current[0]?.focus();
    }, 300);

    // Start resend cooldown
    setResendCooldown(60);
    const interval = setInterval(() => {
      setResendCooldown((prev) => {
        if (prev <= 1) {
          setCanResend(true);
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Monitor app state to detect when user goes to check email
  useEffect(() => {
    const handleAppStateChange = (nextAppState: 'active' | 'background' | 'inactive' | 'unknown' | 'extension') => {
      console.log('🔐 [OTP_VERIFICATION] App state changed:', { from: appState, to: nextAppState });

      if (appState === 'active' && (nextAppState === 'background' || nextAppState === 'inactive')) {
        // User is leaving the app (possibly to check email)
        setHasLeftToCheckEmail(true);
        console.log('🔐 [OTP_VERIFICATION] User left app, possibly to check email');
      } else if ((appState === 'background' || appState === 'inactive') && nextAppState === 'active') {
        // User is returning to the app
        if (hasLeftToCheckEmail) {
          console.log('🔐 [OTP_VERIFICATION] User returned to app after checking email');
          // Focus the first input when they return
          setTimeout(() => {
            inputRefs.current[0]?.focus();
          }, 500);
        }
      }

      setAppState(nextAppState);
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [appState, hasLeftToCheckEmail]);

  // Prevent back navigation during OTP verification (security measure)
  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        // Exit app directly during OTP verification for security
        BackHandler.exitApp();
        return true; // Prevent default back action
      };

      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () => subscription.remove();
    }, [])
  );

  const triggerHaptic = useCallback((type: 'light' | 'medium' | 'heavy' | 'success' | 'error') => {
    const hapticOptions = {
      enableVibrateFallback: true,
      ignoreAndroidSystemSettings: false,
    };

    switch (type) {
      case 'light':
        ReactNativeHapticFeedback.trigger('impactLight', hapticOptions);
        break;
      case 'medium':
        ReactNativeHapticFeedback.trigger('impactMedium', hapticOptions);
        break;
      case 'heavy':
        ReactNativeHapticFeedback.trigger('impactHeavy', hapticOptions);
        break;
      case 'success':
        ReactNativeHapticFeedback.trigger('notificationSuccess', hapticOptions);
        break;
      case 'error':
        ReactNativeHapticFeedback.trigger('notificationError', hapticOptions);
        break;
    }
  }, []);

  const shakeInputs = useCallback(() => {
    Animated.sequence([
      Animated.timing(errorShakeAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
      Animated.timing(errorShakeAnim, { toValue: -10, duration: 100, useNativeDriver: true }),
      Animated.timing(errorShakeAnim, { toValue: 10, duration: 100, useNativeDriver: true }),
      Animated.timing(errorShakeAnim, { toValue: 0, duration: 100, useNativeDriver: true }),
    ]).start();
  }, [errorShakeAnim]);

  const setErrorWithTimeout = useCallback((message: string) => {
    setError(message);
    setVerificationStatus('error');
    triggerHaptic('error');
    shakeInputs();

    // Clear error after 3 seconds
    setTimeout(() => {
      setError('');
      setVerificationStatus('idle');
    }, 3000);
  }, [triggerHaptic, shakeInputs]);

  const handleCodeChange = useCallback((text: string, index: number) => {
    if (!/^\d*$/.test(text)) return; // Only allow digits

    const newCode = [...code];
    newCode[index] = text;
    setCode(newCode);

    // Clear error when user starts typing
    if (error) {
      setError('');
      setVerificationStatus('idle');
    }

    // Auto-focus next input
    if (text && index < 3) {
      inputRefs.current[index + 1]?.focus();
    }

    // Auto-verify when all 4 digits are entered
    if (newCode.join('').length === 4 && !loading) {
      setTimeout(() => handleVerifyOtp(newCode.join('')), 100);
    }
  }, [code, error, loading]);

  const handleKeyPress = useCallback((key: string, index: number) => {
    if (key === 'Backspace') {
      const newCode = [...code];
      if (newCode[index]) {
        // Clear current digit
        newCode[index] = '';
        setCode(newCode);
      } else if (index > 0) {
        // Move to previous input and clear it
        newCode[index - 1] = '';
        setCode(newCode);
        inputRefs.current[index - 1]?.focus();
      }
    }
  }, [code]);

  const handleVerifyOtp = useCallback(async (otpToVerify: string = code.join('')) => {
    if (otpToVerify.length !== 4) {
      setErrorWithTimeout('Please enter the complete 4-digit code');
      return;
    }

    setLoading(true);
    setVerificationStatus('verifying');
    logger.userAction('OTP_VERIFICATION_ATTEMPTED', { otpLength: otpToVerify.length });

    try {
      // Ensure email is available before making API call
      if (!email) {
        throw new Error('Email is required for OTP verification');
      }

      // Call API to verify PIN reset OTP
      const response = await apiService.verifyPinResetOTP(email, otpToVerify);

      console.log('🔐 [OTP_VERIFICATION] Full API response:', {
        hasResponse: !!response,
        hasData: !!response?.data,
        status: response?.data?.status,
        hasResetToken: !!response?.data?.data?.resetToken,
        resetTokenType: typeof response?.data?.data?.resetToken,
        fullResponse: response
      });

      if (response.data?.status === 'success') {
        logger.security('OTP_VERIFICATION_SUCCESS', null);
        setVerificationStatus('success');
        triggerHaptic('success');

        // Navigate directly to PIN setup with reset token
        console.log('🔐 [OTP_VERIFICATION] About to navigate to PIN setup with reset token');
        console.log('🔐 [OTP_VERIFICATION] Reset token:', response.data.data.resetToken);
        console.log('🔐 [OTP_VERIFICATION] User data:', userData);

        logger.info('Navigating to PIN reset setup', {
          hasResetToken: !!response.data.data.resetToken,
          hasUserData: !!userData,
          email: email,
          resetToken: response.data.data.resetToken?.substring(0, 10) + '...' // Log partial token for debugging
        }, 'auth');

        console.log('🔐 [OTP_VERIFICATION] Reset token received:', {
          hasResetToken: !!response.data.data.resetToken,
          resetTokenLength: response.data.data.resetToken?.length,
          resetTokenType: typeof response.data.data.resetToken,
          resetTokenPreview: response.data.data.resetToken ? `${response.data.data.resetToken.substring(0, 15)}...` : 'null'
        });

        // Validate reset token before navigation
        if (!response.data.data.resetToken) {
          console.error('🔐 [OTP_VERIFICATION] No reset token in response!');
          throw new Error('Reset token not received from server. Please try again.');
        }

        // Navigate directly to dedicated PIN reset screen
        try {
          console.log('🔐 [OTP_VERIFICATION] Calling navigation.replace to PinReset...');
          console.log('🔐 [OTP_VERIFICATION] Navigation params being passed:', {
            resetToken: response.data.data.resetToken ? `${response.data.data.resetToken.substring(0, 15)}...` : 'MISSING',
            hasUserData: !!userData,
            userDataEmail: userData?.email
          });

          navigation.replace('PinReset', {
            resetToken: response.data.data.resetToken,
            userData: userData
          });
          console.log('🔐 [OTP_VERIFICATION] Navigation replace call completed');
        } catch (navError) {
          console.error('🔐 [OTP_VERIFICATION] Navigation error:', navError);
          logger.error('Navigation to PIN reset failed', navError, 'auth');

          // Fallback: try regular navigate
          console.log('🔐 [OTP_VERIFICATION] Trying fallback navigation...');
          navigation.navigate('PinReset', {
            resetToken: response.data.data.resetToken,
            userData: userData
          });
        }
      } else {
        throw new Error(response.data?.message || response.message || 'Invalid code');
      }

    } catch (error: any) {
      logger.error('OTP verification failed', error, 'auth');
      setErrorWithTimeout(error.message || 'Invalid code. Please try again.');
      setCode(['', '', '', '']);
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 100);
    } finally {
      setLoading(false);
    }
  }, [code, email, navigation, triggerHaptic, setErrorWithTimeout]);

  const handleResendCode = useCallback(async () => {
    if (resendCooldown > 0) return;

    setLoading(true);
    logger.userAction('OTP_RESEND_REQUESTED', null);

    try {
      // Ensure email is available before making API call
      if (!email) {
        throw new Error('Email is required to resend OTP');
      }

      // Call API to resend PIN reset OTP
      const response = await apiService.sendPinResetOTP(email);

      if (response.data?.status === 'success') {
        triggerHaptic('success');
        setCanResend(false);
        setResendCooldown(60);

        // Start new cooldown
        const interval = setInterval(() => {
          setResendCooldown((prev) => {
            if (prev <= 1) {
              setCanResend(true);
              clearInterval(interval);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);

        Alert.alert('Code Sent', 'A new verification code has been sent to your email.');
      } else {
        throw new Error(response.data?.message || response.message || 'Failed to resend code');
      }

    } catch (error: any) {
      logger.error('Failed to resend OTP', error, 'auth');
      Alert.alert('Resend Failed', error.message || 'Unable to send new code. Please try again.');
    } finally {
      setLoading(false);
    }
  }, [resendCooldown, email, triggerHaptic]);



  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      paddingTop: 60,
      paddingHorizontal: 20,
      paddingBottom: 20,
    },
    backButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: 'rgba(255, 255, 255, 0.1)',
      alignItems: 'center',
      justifyContent: 'center',
    },
    content: {
      flex: 1,
      paddingHorizontal: 32,
      justifyContent: 'center',
      paddingBottom: 100,
    },
    title: {
      fontSize: 28,
      fontWeight: '700',
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: 12,
      letterSpacing: 0.3,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.muted,
      textAlign: 'center',
      marginBottom: 8,
      lineHeight: 22,
    },
    emailAddress: {
      fontSize: 16,
      fontWeight: '600',
      color: '#8000FF',
      textAlign: 'center',
      marginBottom: 16,
    },
    helpText: {
      fontSize: 14,
      color: theme.colors.muted,
      textAlign: 'center',
      marginBottom: 40,
      lineHeight: 20,
      fontStyle: 'italic',
    },
    codeContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      gap: 16,
      marginBottom: 40,
    },
    codeInput: {
      width: 56,
      height: 56,
      fontSize: 24,
      fontWeight: 'bold',
      textAlign: 'center',
      color: theme.colors.text,
      backgroundColor: 'transparent',
      borderBottomWidth: 2,
      borderBottomColor: theme.colors.border,
    },
    codeInputFilled: {
      borderBottomColor: '#8000FF',
      borderBottomWidth: 3,
    },
    codeInputVerifying: {
      borderBottomColor: '#F59E0B',
      borderBottomWidth: 3,
    },
    codeInputSuccess: {
      borderBottomColor: '#10B981',
      borderBottomWidth: 3,
    },
    codeInputError: {
      borderBottomColor: '#EF4444',
      borderBottomWidth: 3,
    },
    resendContainer: {
      alignItems: "center",
      marginBottom: 40,
    },
    resendButton: {
      padding: 12,
    },
    resendButtonText: {
      fontSize: 14,
      fontWeight: "500",
      textAlign: "center",
    },
    glassyResendButton: {
      borderRadius: 20,
      padding: 2,
    },
  })

  return (
    <View style={styles.container}>
      <StatusBar barStyle={isDark ? "light-content" : "dark-content"} backgroundColor="transparent" translucent={true} />
      <ImageBackground
        source={isDark ? require("../../../assets/images/bg.jpeg") : require("../../../assets/images/bg-white.jpeg")}
        style={StyleSheet.absoluteFillObject}
        resizeMode="cover"
      />

      {/* Header - No back button for security */}
      <SafeAreaView style={styles.header}>
        {/* Back navigation disabled during OTP verification for security */}
      </SafeAreaView>

      {/* Content */}
      <SafeAreaView style={styles.content}>
        <Text style={styles.title}>Enter verification code</Text>
        <Text style={styles.subtitle}>
          We sent a 4-digit code to
        </Text>
        <Text style={styles.emailAddress}>{email}</Text>

        {/* Helpful message about checking email */}
        <Text style={styles.helpText}>
          💡 You can safely check your email app - this screen will remain here when you return
        </Text>

        {/* Code Input */}
        <Animated.View
          style={[
            styles.codeContainer,
            { transform: [{ translateX: errorShakeAnim }] }
          ]}
        >
          {code.map((digit, index) => (
            <TextInput
              key={index}
              ref={(ref) => {
                if (ref) inputRefs.current[index] = ref
              }}
              style={[
                styles.codeInput,
                digit && verificationStatus === 'idle' && styles.codeInputFilled,
                verificationStatus === 'verifying' && styles.codeInputVerifying,
                verificationStatus === 'success' && styles.codeInputSuccess,
                verificationStatus === 'error' && styles.codeInputError
              ]}
              value={digit}
              onChangeText={(text) => handleCodeChange(text, index)}
              onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
              keyboardType="numeric"
              maxLength={1}
              autoFocus={index === 0}
              editable={!loading}
            />
          ))}
        </Animated.View>

        {/* Resend */}
        <View style={styles.resendContainer}>
          <GlassyBox style={styles.glassyResendButton}>
            <TouchableOpacity
              style={styles.resendButton}
              onPress={handleResendCode}
              disabled={!canResend}
              activeOpacity={0.7}
              onPressIn={() => {
                setIsResendPressed(true);
                Animated.spring(bounceAnim, {
                  toValue: 0.85,
                  useNativeDriver: true,
                  friction: 3,
                }).start();
              }}
              onPressOut={() => {
                setIsResendPressed(false);
                Animated.spring(bounceAnim, {
                  toValue: 1,
                  useNativeDriver: true,
                  friction: 3,
                }).start();
              }}
            >
              <Animated.View
                style={{
                  transform: [{ scale: bounceAnim }]
                }}
              >
                <Text style={[styles.resendButtonText, { color: canResend ? '#8000FF' : theme.colors.muted }]}>
                  {loading ? "Sending..." : canResend ? "Resend" : `Resend in ${resendCooldown}s`}
                </Text>
              </Animated.View>
            </TouchableOpacity>
          </GlassyBox>
        </View>
      </SafeAreaView>
    </View>
  )
});



OtpVerificationScreen.displayName = 'OtpVerificationScreen';

export default OtpVerificationScreen;
