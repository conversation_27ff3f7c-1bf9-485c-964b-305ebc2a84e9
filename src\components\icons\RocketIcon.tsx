import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface RocketIconProps {
  size?: number;
  color?: string;
}

const RocketIcon: React.FC<RocketIconProps> = ({ 
  size = 24, 
  color = '#007AFF' 
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M12 2C13.1 2 14 2.9 14 4V6L16 8V10C16 11.1 15.1 12 14 12H10C8.9 12 8 11.1 8 10V8L10 6V4C10 2.9 10.9 2 12 2Z"
        fill={color}
      />
      <Path
        d="M9 12V16L7 18V20C7 21.1 7.9 22 9 22H15C16.1 22 17 21.1 17 20V18L15 16V12H9Z"
        fill={color}
      />
      <Path
        d="M6 10C6 8.9 5.1 8 4 8S2 8.9 2 10V12C2 13.1 2.9 14 4 14S6 13.1 6 12V10Z"
        fill={color}
      />
      <Path
        d="M22 10C22 8.9 21.1 8 20 8S18 8.9 18 10V12C18 13.1 18.9 14 20 14S22 13.1 22 12V10Z"
        fill={color}
      />
      <Path
        d="M12 6C12.55 6 13 6.45 13 7S12.55 8 12 8S11 7.55 11 7S11.45 6 12 6Z"
        fill="#FFFFFF"
      />
    </Svg>
  );
};

export default RocketIcon;