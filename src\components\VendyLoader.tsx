import React, { useEffect, useRef } from 'react';
import { View, Animated, StyleSheet } from 'react-native';
import Svg, {
  Path,
  Defs,
  LinearGradient,
  Stop
} from 'react-native-svg';
import { useTheme } from './ThemeContext';

interface VendyLoaderProps {
  size?: number;
}

const AnimatedSvg = Animated.createAnimatedComponent(Svg);
const AnimatedPath = Animated.createAnimatedComponent(Path);

const VendyLoader: React.FC<VendyLoaderProps> = ({ size = 64 }) => {
  const { isDark } = useTheme();

  // Animation values for each letter
  const vAnim = useRef(new Animated.Value(0)).current;
  const eAnim = useRef(new Animated.Value(0)).current;
  const nAnim = useRef(new Animated.Value(0)).current;
  const dAnim = useRef(new Animated.Value(0)).current;
  const yAnim = useRef(new Animated.Value(0)).current;
  const rotationAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Create staggered animations for each letter
    const createLetterAnimation = (animValue: Animated.Value, delay: number = 0) => {
      return Animated.loop(
        Animated.sequence([
          Animated.delay(delay),
          Animated.timing(animValue, {
            toValue: 1,
            duration: 2000,
            useNativeDriver: false,
          }),
          Animated.timing(animValue, {
            toValue: 0,
            duration: 2000,
            useNativeDriver: false,
          }),
        ])
      );
    };

    // Rotation animation for the middle letter (E)
    const rotationLoop = Animated.loop(
      Animated.timing(rotationAnim, {
        toValue: 1,
        duration: 8000,
        useNativeDriver: true,
      })
    );

    // Start all animations with staggered delays
    createLetterAnimation(vAnim, 0).start();
    createLetterAnimation(eAnim, 200).start();
    createLetterAnimation(nAnim, 400).start();
    createLetterAnimation(dAnim, 600).start();
    createLetterAnimation(yAnim, 800).start();
    rotationLoop.start();

    return () => {
      // Cleanup animations
      vAnim.stopAnimation();
      eAnim.stopAnimation();
      nAnim.stopAnimation();
      dAnim.stopAnimation();
      yAnim.stopAnimation();
      rotationAnim.stopAnimation();
    };
  }, []);

  const rotation = rotationAnim.interpolate({
    inputRange: [0, 0.125, 0.25, 0.375, 0.5, 0.625, 0.75, 0.875, 1],
    outputRange: ['0deg', '270deg', '270deg', '540deg', '540deg', '810deg', '810deg', '1080deg', '1080deg'],
  });

  // Create dash animations for each letter
  const createDashProps = (animValue: Animated.Value) => {
    const dashArray = animValue.interpolate({
      inputRange: [0, 0.5, 1],
      outputRange: ['0 1 359 0', '0 359 1 0', '359 1 0 0'],
    });

    const dashOffset = animValue.interpolate({
      inputRange: [0, 1],
      outputRange: [365, 5],
    });

    return { strokeDasharray: dashArray, strokeDashoffset: dashOffset };
  };

  // Purple and black color scheme
  const colors = isDark ? {
    v: ['#C084FC', '#8B5CF6'], // Light Purple to Purple
    e: ['#A855F7', '#7C3AED'], // Purple to Dark Purple
    n: ['#9333EA', '#6B21A8'], // Dark Purple to Deep Purple
    d: ['#7C3AED', '#581C87'], // Dark Purple to Very Dark Purple
    y: ['#8B5CF6', '#6B21A8'], // Purple to Deep Purple
  } : {
    v: ['#6B21A8', '#581C87'], // Deep Purple to Very Dark Purple
    e: ['#7C3AED', '#5B21B6'], // Dark Purple to Very Dark Purple
    n: ['#8B5CF6', '#6B21A8'], // Purple to Deep Purple
    d: ['#9333EA', '#7C3AED'], // Dark Purple to Dark Purple
    y: ['#A855F7', '#8B5CF6'], // Purple to Purple
  };

  const vDashProps = createDashProps(vAnim);
  const eDashProps = createDashProps(eAnim);
  const nDashProps = createDashProps(nAnim);
  const dDashProps = createDashProps(dAnim);
  const yDashProps = createDashProps(yAnim);

  return (
    <View style={[styles.container, { width: size * 5.2, height: size }]}>
      {/* V */}
      <Svg width={size} height={size} viewBox="0 0 64 64">
        <Defs>
          <LinearGradient id="gradV" x1="0" y1="0" x2="0" y2="64">
            <Stop offset="0" stopColor={colors.v[0]} />
            <Stop offset="1" stopColor={colors.v[1]} />
          </LinearGradient>
        </Defs>
        <AnimatedPath
          d="M 12 4 L 20 4 L 32 48 L 44 4 L 52 4 L 36 60 L 28 60 Z"
          stroke="url(#gradV)"
          strokeWidth="3"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          {...vDashProps}
        />
      </Svg>

      {/* E */}
      <Svg width={size} height={size} viewBox="0 0 64 64">
        <Defs>
          <LinearGradient id="gradE" x1="0" y1="0" x2="0" y2="64">
            <Stop offset="0" stopColor={colors.e[0]} />
            <Stop offset="1" stopColor={colors.e[1]} />
          </LinearGradient>
        </Defs>
        <AnimatedPath
          d="M 8 4 L 8 60 L 56 60 L 56 52 L 16 52 L 16 36 L 48 36 L 48 28 L 16 28 L 16 12 L 56 12 L 56 4 Z"
          stroke="url(#gradE)"
          strokeWidth="3"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          {...eDashProps}
        />
      </Svg>

      {/* N */}
      <Svg width={size} height={size} viewBox="0 0 64 64">
        <Defs>
          <LinearGradient id="gradN" x1="0" y1="0" x2="0" y2="64">
            <Stop offset="0" stopColor={colors.n[0]} />
            <Stop offset="1" stopColor={colors.n[1]} />
          </LinearGradient>
        </Defs>
        <AnimatedPath
          d="M 8 4 L 16 4 L 16 36 L 48 4 L 56 4 L 56 60 L 48 60 L 48 28 L 16 60 L 8 60 Z"
          stroke="url(#gradN)"
          strokeWidth="3"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          {...nDashProps}
        />
      </Svg>

      {/* D */}
      <Svg width={size} height={size} viewBox="0 0 64 64">
        <Defs>
          <LinearGradient id="gradD" x1="0" y1="0" x2="0" y2="64">
            <Stop offset="0" stopColor={colors.d[0]} />
            <Stop offset="1" stopColor={colors.d[1]} />
          </LinearGradient>
        </Defs>
        <AnimatedPath
          d="M 8 4 L 28 4 C 44 4 56 16 56 32 C 56 48 44 60 28 60 L 8 60 Z M 16 12 L 16 52 L 28 52 C 40 52 48 44 48 32 C 48 20 40 12 28 12 Z"
          stroke="url(#gradD)"
          strokeWidth="3"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          {...dDashProps}
        />
      </Svg>

      {/* Y */}
      <Svg width={size} height={size} viewBox="0 0 64 64">
        <Defs>
          <LinearGradient id="gradY" x1="0" y1="0" x2="0" y2="64">
            <Stop offset="0" stopColor={colors.y[0]} />
            <Stop offset="1" stopColor={colors.y[1]} />
          </LinearGradient>
        </Defs>
        <AnimatedPath
          d="M 12 4 L 20 4 L 32 28 L 44 4 L 52 4 L 36 36 L 36 60 L 28 60 L 28 36 Z"
          stroke="url(#gradY)"
          strokeWidth="3"
          fill="none"
          strokeLinecap="round"
          strokeLinejoin="round"
          {...yDashProps}
        />
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  spacer: {
    width: 8,
  },
});

export default VendyLoader;
