import { Platform, Dimensions } from 'react-native'

// Device performance detection
export const getDevicePerformanceLevel = () => {
  const { width, height } = Dimensions.get('window')
  const screenSize = width * height
  
  // Basic performance classification based on screen size and platform
  // This is a simplified approach - in production, you'd use more sophisticated detection
  if (Platform.OS === 'ios') {
    // iOS devices generally have better performance
    if (screenSize > 2000000) return 'high' // iPhone Pro models, iPad
    if (screenSize > 1500000) return 'medium' // Standard iPhones
    return 'low' // Older iPhones
  } else {
    // Android performance varies widely
    if (screenSize > 2500000) return 'high' // High-end Android tablets/phones
    if (screenSize > 1800000) return 'medium' // Mid-range devices
    return 'low' // Budget devices
  }
}

// Performance configuration based on device capability
export const getPerformanceConfig = () => {
  const performanceLevel = getDevicePerformanceLevel()
  
  switch (performanceLevel) {
    case 'high':
      return {
        // High-end devices can handle full animations and effects
        animationsEnabled: true,
        waveSegments: 80,
        typingSpeed: 50,
        deletingSpeed: 25,
        maxConcurrentAnimations: 10,
        enableShadows: true,
        enableBlur: true,
        imageQuality: 'high',
        enableHaptics: true,
      }
    
    case 'medium':
      return {
        // Mid-range devices with reduced complexity
        animationsEnabled: true,
        waveSegments: 50,
        typingSpeed: 80,
        deletingSpeed: 40,
        maxConcurrentAnimations: 6,
        enableShadows: true,
        enableBlur: false,
        imageQuality: 'medium',
        enableHaptics: true,
      }
    
    case 'low':
    default:
      return {
        // Low-end devices with minimal animations
        animationsEnabled: true, // Keep basic animations for UX
        waveSegments: 30,
        typingSpeed: 120,
        deletingSpeed: 60,
        maxConcurrentAnimations: 3,
        enableShadows: false,
        enableBlur: false,
        imageQuality: 'low',
        enableHaptics: false,
      }
  }
}

// Global performance settings
export const PERFORMANCE_CONFIG = getPerformanceConfig()

// Memory management settings
export const MEMORY_CONFIG = {
  // Image cache settings
  maxImageCacheSize: PERFORMANCE_CONFIG.imageQuality === 'high' ? 100 : 50, // MB
  
  // Component cache settings
  maxComponentCacheSize: 50,
  
  // Animation cleanup settings
  animationCleanupDelay: 1000, // ms
  
  // Garbage collection hints
  enableGCHints: PERFORMANCE_CONFIG.animationsEnabled,
}

// Bundle optimization settings
export const BUNDLE_CONFIG = {
  // Code splitting thresholds
  chunkSizeThreshold: 100000, // bytes
  
  // Lazy loading settings
  enableLazyLoading: true,
  lazyLoadThreshold: 2, // screens ahead to preload
  
  // Asset optimization
  enableAssetOptimization: true,
  assetCompressionLevel: PERFORMANCE_CONFIG.imageQuality === 'high' ? 0.8 : 0.6,
}

// Network optimization settings
export const NETWORK_CONFIG = {
  // Request batching
  enableRequestBatching: true,
  batchTimeout: 100, // ms
  
  // Caching strategy
  cacheStrategy: 'stale-while-revalidate',
  cacheTimeout: 300000, // 5 minutes
  
  // Retry settings
  maxRetries: 3,
  retryDelay: 1000, // ms
  
  // Timeout settings
  requestTimeout: PERFORMANCE_CONFIG.imageQuality === 'high' ? 10000 : 5000, // ms
}



// Feature flags based on performance
export const FEATURE_FLAGS = {
  enableAdvancedAnimations: PERFORMANCE_CONFIG.animationsEnabled && PERFORMANCE_CONFIG.maxConcurrentAnimations > 5,
  enableParallax: PERFORMANCE_CONFIG.animationsEnabled,
  enableGestures: true,
  enableVibration: PERFORMANCE_CONFIG.enableHaptics,
  enableSoundEffects: PERFORMANCE_CONFIG.enableHaptics,
  enableVideoPlayback: PERFORMANCE_CONFIG.imageQuality === 'high',
  enableLiveUpdates: PERFORMANCE_CONFIG.maxConcurrentAnimations > 3,
}
