<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - <PERSON><PERSON><PERSON></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="/js/dashboard.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav {
            display: flex;
            gap: 2rem;
        }

        .nav a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: background 0.3s ease;
        }

        .nav a:hover, .nav a.active {
            background: rgba(255, 255, 255, 0.2);
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .page-title {
            font-size: 2rem;
            margin-bottom: 2rem;
            color: #333;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }

        .settings-card {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .settings-card h3 {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: #333;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #555;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 100px;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #667eea;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            transition: transform 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .coming-soon {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .coming-soon i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #667eea;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .nav {
                gap: 1rem;
            }

            .container {
                padding: 0 1rem;
            }

            .settings-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-cog"></i> Vendy Admin
            </div>
            <nav class="nav">
                <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/ota-updates"><i class="fas fa-download"></i> OTA Updates</a>
                <a href="/analytics"><i class="fas fa-chart-bar"></i> Analytics</a>
                <a href="/settings" class="active"><i class="fas fa-cog"></i> Settings</a>
            </nav>
            <div class="user-menu">
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </div>
    </header>

    <div class="container">
        <h1 class="page-title">
            <i class="fas fa-cog"></i> System Settings
        </h1>

        <div class="settings-grid">
            <div class="settings-card">
                <h3><i class="fas fa-server"></i> System Configuration</h3>
                <div class="coming-soon">
                    <i class="fas fa-tools"></i>
                    <h4>Configuration Coming Soon</h4>
                    <p>System configuration options will be available in a future update.</p>
                </div>
            </div>

            <div class="settings-card">
                <h3><i class="fas fa-shield-alt"></i> Security Settings</h3>
                <div class="coming-soon">
                    <i class="fas fa-lock"></i>
                    <h4>Security Settings Coming Soon</h4>
                    <p>Advanced security configuration options will be available soon.</p>
                </div>
            </div>

            <div class="settings-card">
                <h3><i class="fas fa-bell"></i> Notification Settings</h3>
                <div class="coming-soon">
                    <i class="fas fa-envelope"></i>
                    <h4>Notifications Coming Soon</h4>
                    <p>Email and push notification settings will be configurable soon.</p>
                </div>
            </div>

            <div class="settings-card">
                <h3><i class="fas fa-database"></i> Backup & Maintenance</h3>
                <div class="coming-soon">
                    <i class="fas fa-download"></i>
                    <h4>Backup Tools Coming Soon</h4>
                    <p>Automated backup and maintenance tools will be available soon.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadSettings();
        });

        async function loadSettings() {
            try {
                // Load current settings from API
                console.log('Settings page loaded');
            } catch (error) {
                console.error('Failed to load settings:', error);
            }
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                fetch('/logout', { method: 'POST' })
                    .then(() => {
                        window.location.href = '/login';
                    });
            }
        }
    </script>
</body>
</html>
