import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface ArrowRightIconProps {
  size?: number;
  color?: string;
}

const ArrowRightIcon: React.FC<ArrowRightIconProps> = ({ 
  size = 24, 
  color = '#FFFFFF' 
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M8.59 16.59L13.17 12L8.59 7.41L10 6L16 12L10 18L8.59 16.59Z"
        fill={color}
      />
    </Svg>
  );
};

export default ArrowRightIcon;