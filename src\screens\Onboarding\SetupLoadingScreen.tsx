import React, { useEffect, useRef, memo, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  StatusBar,
  Animated,
  Dimensions,
  ImageBackground,
} from 'react-native';
import { useTheme } from '../../components/ThemeContext';
import { useNavigation, useRoute } from '@react-navigation/native';
import type { NativeStackNavigationProp } from '@react-navigation/native-stack';
import type { RootStackParamList } from '../../types/navigation';
import logger from '../../services/productionLogger';
import crashReporting from '../../services/crashReportingService';

const { width, height } = Dimensions.get('window');

interface SetupLoadingScreenProps {
  message?: string;
}

type SetupLoadingNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const SetupLoadingScreen: React.FC<SetupLoadingScreenProps> = memo(({
  message: propMessage = "Hold on while we prepare your experience"
}) => {
  const { theme, isDark } = useTheme();
  const navigation = useNavigation<SetupLoadingNavigationProp>();
  const route = useRoute();

  // Get message from route params or use prop message
  const routeParams = route.params as { message?: string; next?: keyof RootStackParamList } | undefined;
  const message = routeParams?.message || propMessage;

  // Check if this is the post-PIN setup transition (no custom message)
  const isTransition = message === propMessage && !routeParams?.message;
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const textFadeAnim = useRef(new Animated.Value(0)).current;
  
  // Bouncing dots animation (only for setup screens)
  const dot1Anim = useRef(new Animated.Value(0)).current;
  const dot2Anim = useRef(new Animated.Value(0)).current;
  const dot3Anim = useRef(new Animated.Value(0)).current;
  
  // Breathing animation (for transitions)
  const breathingAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Log setup loading screen display
    logger.info('Setup loading screen displayed', {
      message,
      isTransition,
      hasRouteParams: !!route.params
    }, 'setup');

    // Add breadcrumb for setup loading
    crashReporting.addBreadcrumb({
      category: 'ui',
      message: 'Setup loading screen displayed',
      level: 'info',
      data: {
        isTransition,
        hasMessage: !!message,
      },
    });

    // Logo entrance animation
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 60,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();

    // Text fade in after logo (only if not transition)
    if (!isTransition) {
      setTimeout(() => {
        Animated.timing(textFadeAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }).start();
      }, 400);
    }

    let animationLoop: any;

    if (isTransition) {
      // Breathing animation for transitions
      animationLoop = Animated.loop(
        Animated.sequence([
          Animated.timing(breathingAnim, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(breathingAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
    } else {
      // Bouncing dots animation for setup screens
      const animateBouncingDots = () => {
        const bounceAnimation = (dot: Animated.Value, delay: number) => {
          return Animated.sequence([
            Animated.delay(delay),
            Animated.timing(dot, {
              toValue: -8,
              duration: 300,
              useNativeDriver: true,
            }),
            Animated.timing(dot, {
              toValue: 0,
              duration: 300,
              useNativeDriver: true,
            }),
          ]);
        };

        return Animated.loop(
          Animated.parallel([
            bounceAnimation(dot1Anim, 0),
            bounceAnimation(dot2Anim, 150),
            bounceAnimation(dot3Anim, 300),
          ])
        );
      };

      animationLoop = animateBouncingDots();
    }

    animationLoop.start();

    // Handle post-verification transition with proper type safety
    const routeParams = route.params as { next?: keyof RootStackParamList; userData?: any; message?: string } | undefined;
    if (routeParams?.next) {
      logger.info('Setup loading screen transitioning to next screen', { nextScreen: routeParams.next }, 'setup');

      setTimeout(() => {
        try {
          // Handle different screen types that need parameters
          if (routeParams.next === 'PinVerification' && routeParams.userData) {
            navigation.reset({
              index: 0,
              routes: [{ name: 'PinVerification', params: { user: routeParams.userData } }],
            });
          } else {
            navigation.reset({
              index: 0,
              routes: [{ name: routeParams.next! }],
            });
          }

          crashReporting.recordUserAction('setup_loading_transition', {
            nextScreen: routeParams.next,
            timestamp: new Date().toISOString(),
          });
        } catch (error) {
          logger.error('Failed to navigate from setup loading screen', error, 'setup');
          // Fallback navigation
          navigation.navigate('MainTabs');
        }
      }, 1500); // 1.5 seconds for UX
    }

    return () => {
      try {
        animationLoop.stop();
        logger.debug('Setup loading screen animations stopped', null, 'setup');
      } catch (error) {
        logger.error('Error stopping setup loading animations', error, 'setup');
      }
    };
  }, [isTransition, message, route.params]);



  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: 40,
    },
    logoContainer: {
      justifyContent: "center",
      alignItems: "center",
      marginBottom: 40,
    },
    logo: {
      width: 120,
      height: 120,
      resizeMode: "contain",
    },
    dotsContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
      marginTop: 20,
      gap: 6,
    },
    dot: {
      width: 6,
      height: 6,
      borderRadius: 3,
      backgroundColor: theme.colors.primary,
    },
    messageContainer: {
      alignItems: 'center',
    },
    message: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      textAlign: 'center',
      lineHeight: 26,
    },
    subtitle: {
      fontSize: 14,
      color: theme.colors.muted,
      textAlign: 'center',
      marginTop: 8,
      lineHeight: 20,
    },
    // Subtle decorative elements
    decorativeCircle1: {
      position: "absolute",
      top: height * 0.15,
      right: -50,
      width: 100,
      height: 100,
      borderRadius: 50,
      backgroundColor: theme.colors.primary,
      opacity: isDark ? 0.05 : 0.03,
    },
    decorativeCircle2: {
      position: "absolute",
      bottom: height * 0.2,
      left: -30,
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: theme.colors.accent,
      opacity: isDark ? 0.04 : 0.02,
    },
  }), [theme, isDark, height]);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle={isDark ? "light-content" : "dark-content"}
        backgroundColor="transparent"
        translucent={true}
      />
      <ImageBackground
        source={isDark ? require("../../../assets/images/bg.jpeg") : require("../../../assets/images/bg-white.jpeg")}
        style={StyleSheet.absoluteFillObject}
        resizeMode="cover"
      />

      {/* Subtle decorative elements */}
      <View style={styles.decorativeCircle1} />
      <View style={styles.decorativeCircle2} />

      {isTransition ? (
        /* Breathing Logo for transitions */
        <Animated.View
          style={[
            styles.logoContainer,
            {
              opacity: fadeAnim,
              transform: [
                { scale: scaleAnim },
                { scale: breathingAnim }
              ],
            },
          ]}
        >
          <Animated.Image
            source={require("../../../assets/icons/vendy.png")}
            style={styles.logo}
          />
        </Animated.View>
      ) : (
        /* Setup screens with bouncing dots and message */
        <>
          <Animated.View
            style={[
              styles.logoContainer,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }],
              },
            ]}
          >
            <Animated.Image
              source={require("../../../assets/icons/vendy.png")}
              style={styles.logo}
            />
            
            {/* Bouncing dots */}
            <View style={styles.dotsContainer}>
              <Animated.View 
                style={[
                  styles.dot, 
                  { transform: [{ translateY: dot1Anim }] }
                ]} 
              />
              <Animated.View 
                style={[
                  styles.dot, 
                  { transform: [{ translateY: dot2Anim }] }
                ]} 
              />
              <Animated.View 
                style={[
                  styles.dot, 
                  { transform: [{ translateY: dot3Anim }] }
                ]} 
              />
            </View>
          </Animated.View>

          {/* Message */}
          <Animated.View 
            style={[
              styles.messageContainer,
              { opacity: textFadeAnim }
            ]}
          >
            <Text style={styles.message}>{message}</Text>
            <Text style={styles.subtitle}>This will only take a moment</Text>
          </Animated.View>
        </>
      )}
    </SafeAreaView>
  );
});

SetupLoadingScreen.displayName = 'SetupLoadingScreen';

export default SetupLoadingScreen;