-- =====================================================
-- PLUGINNG SCHEMA EXTENSIONS
-- =====================================================
-- Database schema extensions to support PluginNG airtime API integration
-- Adds PluginNG-specific fields, indexes, and functions for optimal performance
-- 
-- Author: PayVendy Development Team
-- Version: 1.0.0
-- Date: 2025-07-11

-- =====================================================
-- PLUGINNG TRANSACTION LOG TABLE
-- =====================================================

-- Create dedicated table for PluginNG API request/response logging
CREATE TABLE IF NOT EXISTS pluginng_transaction_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    transaction_id UUID REFERENCES transactions(id) ON DELETE CASCADE,
    request_id VARCHAR(100) NOT NULL,
    request_type VARCHAR(50) NOT NULL, -- 'purchase', 'query', 'balance'
    request_payload JSONB NOT NULL,
    response_payload JSONB,
    response_code VARCHAR(10),
    response_message TEXT,
    http_status INTEGER,
    duration_ms INTEGER,
    retry_attempt INTEGER DEFAULT 1,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for PluginNG logs
CREATE INDEX IF NOT EXISTS idx_pluginng_logs_transaction_id 
ON pluginng_transaction_logs(transaction_id);

CREATE INDEX IF NOT EXISTS idx_pluginng_logs_request_id 
ON pluginng_transaction_logs(request_id);

CREATE INDEX IF NOT EXISTS idx_pluginng_logs_request_type_date 
ON pluginng_transaction_logs(request_type, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_pluginng_logs_response_code 
ON pluginng_transaction_logs(response_code, created_at DESC);

-- =====================================================
-- PERFORMANCE INDEXES FOR PLUGINNG
-- =====================================================

-- Index for PluginNG provider transactions
CREATE INDEX IF NOT EXISTS idx_transactions_provider_type_pluginng 
ON transactions(provider_type) 
WHERE provider_type = 'pluginng';

-- Composite index for user transactions by PluginNG provider
CREATE INDEX IF NOT EXISTS idx_transactions_user_pluginng_status 
ON transactions(user_id, provider_type, status, created_at DESC)
WHERE provider_type = 'pluginng';

-- Index for pending PluginNG transactions that need retry
CREATE INDEX IF NOT EXISTS idx_transactions_pluginng_pending_retry 
ON transactions(status, last_retry_at, retry_count, provider_type) 
WHERE (status = 'pending' OR status = 'failed') AND provider_type = 'pluginng';

-- =====================================================
-- HELPER FUNCTIONS FOR PLUGINNG
-- =====================================================

-- Function to get PluginNG transaction statistics
CREATE OR REPLACE FUNCTION get_pluginng_stats(
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '24 hours',
    end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE (
    total_transactions BIGINT,
    successful_transactions BIGINT,
    failed_transactions BIGINT,
    pending_transactions BIGINT,
    total_amount DECIMAL(15,2),
    total_commission DECIMAL(15,4),
    success_rate DECIMAL(5,2),
    average_amount DECIMAL(15,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_transactions,
        COUNT(*) FILTER (WHERE status = 'completed') as successful_transactions,
        COUNT(*) FILTER (WHERE status = 'failed') as failed_transactions,
        COUNT(*) FILTER (WHERE status = 'pending') as pending_transactions,
        COALESCE(SUM(amount) FILTER (WHERE status = 'completed'), 0) as total_amount,
        COALESCE(SUM(commission_amount) FILTER (WHERE status = 'completed'), 0) as total_commission,
        CASE 
            WHEN COUNT(*) > 0 THEN 
                ROUND((COUNT(*) FILTER (WHERE status = 'completed') * 100.0 / COUNT(*)), 2)
            ELSE 0 
        END as success_rate,
        CASE 
            WHEN COUNT(*) FILTER (WHERE status = 'completed') > 0 THEN 
                ROUND(AVG(amount) FILTER (WHERE status = 'completed'), 2)
            ELSE 0 
        END as average_amount
    FROM transactions 
    WHERE provider_type = 'pluginng' 
    AND created_at BETWEEN start_date AND end_date;
END;
$$ LANGUAGE plpgsql;

-- Function to get pending PluginNG transactions for retry
CREATE OR REPLACE FUNCTION get_pending_pluginng_transactions(
    max_retry_count INTEGER DEFAULT 3,
    retry_delay_minutes INTEGER DEFAULT 5
)
RETURNS TABLE (
    id UUID,
    reference VARCHAR(100),
    amount DECIMAL(15,2),
    recipient VARCHAR(255),
    retry_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    last_retry_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.id,
        t.reference,
        t.amount,
        t.recipient,
        t.retry_count,
        t.created_at,
        t.last_retry_at
    FROM transactions t
    WHERE t.provider_type = 'pluginng'
    AND t.status = 'pending'
    AND t.retry_count < max_retry_count
    AND (
        t.last_retry_at IS NULL 
        OR t.last_retry_at < NOW() - (retry_delay_minutes || ' minutes')::INTERVAL
    )
    ORDER BY t.created_at ASC
    LIMIT 100;
END;
$$ LANGUAGE plpgsql;

-- Function to update PluginNG transaction status with automatic timestamp
CREATE OR REPLACE FUNCTION update_pluginng_transaction_status(
    transaction_id UUID,
    new_status VARCHAR(20),
    provider_tx_id VARCHAR(100) DEFAULT NULL,
    commission_amt DECIMAL(15,4) DEFAULT NULL,
    failure_reason_val VARCHAR(100) DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    update_data JSONB := '{}';
BEGIN
    -- Build update data
    update_data := jsonb_build_object('status', new_status, 'updated_at', NOW());
    
    IF provider_tx_id IS NOT NULL THEN
        update_data := update_data || jsonb_build_object('provider_transaction_id', provider_tx_id);
    END IF;
    
    IF commission_amt IS NOT NULL THEN
        update_data := update_data || jsonb_build_object('commission_amount', commission_amt);
    END IF;
    
    IF failure_reason_val IS NOT NULL THEN
        update_data := update_data || jsonb_build_object('failure_reason', failure_reason_val);
    END IF;
    
    -- Set completion timestamp for completed transactions
    IF new_status = 'completed' THEN
        update_data := update_data || jsonb_build_object('completed_at', NOW());
    END IF;
    
    -- Update the transaction
    UPDATE transactions 
    SET 
        status = (update_data->>'status')::VARCHAR(20),
        updated_at = (update_data->>'updated_at')::TIMESTAMP WITH TIME ZONE,
        provider_transaction_id = COALESCE((update_data->>'provider_transaction_id')::VARCHAR(100), provider_transaction_id),
        commission_amount = COALESCE((update_data->>'commission_amount')::DECIMAL(15,4), commission_amount),
        failure_reason = COALESCE((update_data->>'failure_reason')::VARCHAR(100), failure_reason),
        completed_at = CASE 
            WHEN new_status = 'completed' THEN (update_data->>'completed_at')::TIMESTAMP WITH TIME ZONE
            ELSE completed_at 
        END
    WHERE id = transaction_id
    AND provider_type = 'pluginng';
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Function to compare provider performance
CREATE OR REPLACE FUNCTION compare_provider_performance(
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW() - INTERVAL '24 hours',
    end_date TIMESTAMP WITH TIME ZONE DEFAULT NOW()
)
RETURNS TABLE (
    provider_type VARCHAR(50),
    total_transactions BIGINT,
    successful_transactions BIGINT,
    success_rate DECIMAL(5,2),
    average_amount DECIMAL(15,2),
    total_volume DECIMAL(15,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.provider_type,
        COUNT(*) as total_transactions,
        COUNT(*) FILTER (WHERE t.status = 'completed') as successful_transactions,
        CASE 
            WHEN COUNT(*) > 0 THEN 
                ROUND((COUNT(*) FILTER (WHERE t.status = 'completed') * 100.0 / COUNT(*)), 2)
            ELSE 0 
        END as success_rate,
        CASE 
            WHEN COUNT(*) FILTER (WHERE t.status = 'completed') > 0 THEN 
                ROUND(AVG(t.amount) FILTER (WHERE t.status = 'completed'), 2)
            ELSE 0 
        END as average_amount,
        COALESCE(SUM(t.amount) FILTER (WHERE t.status = 'completed'), 0) as total_volume
    FROM transactions t
    WHERE t.provider_type IN ('vtpass', 'pluginng')
    AND t.created_at BETWEEN start_date AND end_date
    GROUP BY t.provider_type
    ORDER BY success_rate DESC, total_volume DESC;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON TABLE pluginng_transaction_logs IS 'Detailed logging of all PluginNG API interactions for debugging and audit purposes';
COMMENT ON FUNCTION get_pluginng_stats IS 'Returns comprehensive statistics for PluginNG transactions within a date range';
COMMENT ON FUNCTION get_pending_pluginng_transactions IS 'Returns pending PluginNG transactions that are eligible for retry based on retry count and delay';
COMMENT ON FUNCTION update_pluginng_transaction_status IS 'Helper function to update PluginNG transaction status with automatic timestamp and related fields';
COMMENT ON FUNCTION compare_provider_performance IS 'Compares performance metrics between VTpass and PluginNG providers';

-- =====================================================
-- UPDATE EXISTING CONSTRAINTS
-- =====================================================

-- Update provider_type constraint to include pluginng
DO $$ 
BEGIN
    -- Check if constraint exists and update it
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE table_name = 'transactions' 
        AND constraint_name LIKE '%provider_type%'
    ) THEN
        -- Drop existing constraint if it exists
        ALTER TABLE transactions DROP CONSTRAINT IF EXISTS transactions_provider_type_check;
    END IF;
    
    -- Add updated constraint
    ALTER TABLE transactions ADD CONSTRAINT transactions_provider_type_check 
    CHECK (provider_type IN ('vtpass', 'pluginng'));
    
EXCEPTION WHEN OTHERS THEN
    -- If constraint doesn't exist or other error, just add the new one
    ALTER TABLE transactions ADD CONSTRAINT transactions_provider_type_check 
    CHECK (provider_type IN ('vtpass', 'pluginng'));
END $$;
