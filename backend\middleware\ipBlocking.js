/**
 * Advanced IP Blocking Middleware
 * 
 * Automatically blocks IPs that exhibit malicious behavior
 * with configurable thresholds and automatic cleanup.
 */

const logger = require('../utils/logger');

// Configuration
const CONFIG = {
  // Thresholds for automatic blocking
  MAX_404_REQUESTS: 10,        // Max 404s before blocking
  MAX_SUSPICIOUS_REQUESTS: 5,   // Max suspicious requests before blocking
  TIME_WINDOW: 10 * 60 * 1000, // 10 minutes
  BLOCK_DURATION: 60 * 60 * 1000, // 1 hour block
  
  // Whitelist (never block these IPs)
  WHITELIST: new Set([
    '127.0.0.1',
    '::1',
    'localhost',
    '***************', // Server IP
    // Add your monitoring/health check IPs here
  ]),
  
  // Permanent blacklist (always block)
  PERMANENT_BLACKLIST: new Set([
    // Add known malicious IPs here
  ])
};

// Tracking data structures
const ipStats = new Map(); // IP -> { requests, suspicious, blocked, firstSeen, lastSeen }
const blockedIPs = new Map(); // IP -> { blockedAt, reason, requestCount }

/**
 * Get or create IP stats
 */
function getIPStats(ip) {
  if (!ipStats.has(ip)) {
    ipStats.set(ip, {
      requests: 0,
      suspicious: 0,
      notFound: 0,
      blocked: false,
      firstSeen: Date.now(),
      lastSeen: Date.now()
    });
  }
  return ipStats.get(ip);
}

/**
 * Check if IP should be blocked
 */
function shouldBlockIP(ip, stats) {
  const now = Date.now();
  const timeInWindow = now - stats.firstSeen;
  
  // Don't block if not enough time has passed
  if (timeInWindow < 60 * 1000) return false; // At least 1 minute of activity
  
  // Block if too many 404s
  if (stats.notFound >= CONFIG.MAX_404_REQUESTS) {
    return { reason: 'excessive_404_requests', count: stats.notFound };
  }
  
  // Block if too many suspicious requests
  if (stats.suspicious >= CONFIG.MAX_SUSPICIOUS_REQUESTS) {
    return { reason: 'excessive_suspicious_requests', count: stats.suspicious };
  }
  
  // Block if very high request rate (potential DDoS)
  const requestRate = stats.requests / (timeInWindow / 1000); // requests per second
  if (requestRate > 10) { // More than 10 requests per second
    return { reason: 'high_request_rate', rate: requestRate.toFixed(2) };
  }
  
  return false;
}

/**
 * Block an IP address
 */
function blockIP(ip, reason, additionalInfo = {}) {
  const now = Date.now();
  blockedIPs.set(ip, {
    blockedAt: now,
    reason,
    ...additionalInfo
  });
  
  const stats = getIPStats(ip);
  stats.blocked = true;
  
  logger.error('🚫 [IP_BLOCKING] IP address blocked', {
    ip,
    reason,
    ...additionalInfo,
    totalRequests: stats.requests,
    suspiciousRequests: stats.suspicious,
    notFoundRequests: stats.notFound,
    activityDuration: Math.round((now - stats.firstSeen) / 1000) + 's'
  });
}

/**
 * Check if IP is currently blocked
 */
function isIPBlocked(ip) {
  // Check permanent blacklist
  if (CONFIG.PERMANENT_BLACKLIST.has(ip)) {
    return { blocked: true, reason: 'permanent_blacklist' };
  }
  
  // Check temporary blocks
  if (blockedIPs.has(ip)) {
    const blockInfo = blockedIPs.get(ip);
    const now = Date.now();
    
    // Check if block has expired
    if (now - blockInfo.blockedAt > CONFIG.BLOCK_DURATION) {
      blockedIPs.delete(ip);
      const stats = getIPStats(ip);
      stats.blocked = false;
      
      logger.info('✅ [IP_BLOCKING] IP block expired', { ip });
      return { blocked: false };
    }
    
    return { blocked: true, reason: blockInfo.reason, blockedAt: blockInfo.blockedAt };
  }
  
  return { blocked: false };
}

/**
 * IP blocking middleware
 */
const ipBlockingMiddleware = (req, res, next) => {
  const ip = req.ip || req.connection.remoteAddress;
  
  // Skip whitelisted IPs
  if (CONFIG.WHITELIST.has(ip)) {
    return next();
  }
  
  // Check if IP is blocked
  const blockStatus = isIPBlocked(ip);
  if (blockStatus.blocked) {
    const remainingTime = Math.ceil((CONFIG.BLOCK_DURATION - (Date.now() - blockStatus.blockedAt)) / 60000);
    
    logger.warn('🚫 [IP_BLOCKING] Blocked IP attempted access', {
      ip,
      reason: blockStatus.reason,
      url: req.originalUrl,
      remainingBlockTime: remainingTime + ' minutes'
    });
    
    return res.status(429).json({
      error: 'Access temporarily restricted',
      retryAfter: remainingTime * 60
    });
  }
  
  // Update IP stats
  const stats = getIPStats(ip);
  stats.requests++;
  stats.lastSeen = Date.now();
  
  // Mark as suspicious if it matches certain patterns
  const url = req.originalUrl || req.url;
  const userAgent = req.get('User-Agent') || '';
  
  if (url.includes('/.git/') || 
      url.includes('/.env') || 
      url.includes('/admin') ||
      userAgent.toLowerCase().includes('scanner') ||
      userAgent.toLowerCase().includes('bot')) {
    stats.suspicious++;
  }
  
  // Track 404s by intercepting the response
  const originalSend = res.send;
  res.send = function(data) {
    if (res.statusCode === 404) {
      stats.notFound++;
    }
    originalSend.call(this, data);
  };
  
  // Check if IP should be blocked after this request
  const shouldBlock = shouldBlockIP(ip, stats);
  if (shouldBlock) {
    blockIP(ip, shouldBlock.reason, shouldBlock);
    
    // Don't block the current request, but future ones will be blocked
    // This prevents blocking legitimate users who just made a few mistakes
  }
  
  next();
};

/**
 * Cleanup old data periodically
 */
setInterval(() => {
  const now = Date.now();
  
  // Clean up old IP stats (older than 24 hours)
  for (const [ip, stats] of ipStats.entries()) {
    if (now - stats.lastSeen > 24 * 60 * 60 * 1000) {
      ipStats.delete(ip);
    }
  }
  
  // Clean up expired blocks
  for (const [ip, blockInfo] of blockedIPs.entries()) {
    if (now - blockInfo.blockedAt > CONFIG.BLOCK_DURATION) {
      blockedIPs.delete(ip);
    }
  }
  
  logger.info('🧹 [IP_BLOCKING] Cleanup completed', {
    activeIPs: ipStats.size,
    blockedIPs: blockedIPs.size
  });
}, 60 * 60 * 1000); // Run every hour

/**
 * Get current statistics (for admin dashboard)
 */
function getStats() {
  return {
    totalTrackedIPs: ipStats.size,
    currentlyBlocked: blockedIPs.size,
    topIPs: Array.from(ipStats.entries())
      .sort(([,a], [,b]) => b.requests - a.requests)
      .slice(0, 10)
      .map(([ip, stats]) => ({ ip, ...stats })),
    recentBlocks: Array.from(blockedIPs.entries())
      .map(([ip, info]) => ({ ip, ...info }))
      .sort((a, b) => b.blockedAt - a.blockedAt)
      .slice(0, 10)
  };
}

module.exports = {
  ipBlockingMiddleware,
  isIPBlocked,
  blockIP,
  getStats,
  CONFIG
};
