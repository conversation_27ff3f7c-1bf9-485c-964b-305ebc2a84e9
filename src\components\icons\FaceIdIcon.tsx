import React from 'react';
import Svg, { Path, Rect } from 'react-native-svg';

interface FaceIdIconProps {
  size?: number;
  color?: string;
}

const FaceIdIcon: React.FC<FaceIdIconProps> = ({ 
  size = 24, 
  color = '#007AFF' 
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      {/* Face outline */}
      <Path
        d="M12 2C13.1 2 14 2.9 14 4V6C14 7.1 13.1 8 12 8S10 7.1 10 6V4C10 2.9 10.9 2 12 2Z"
        fill={color}
      />
      <Path
        d="M12 16C13.1 16 14 16.9 14 18V20C14 21.1 13.1 22 12 22S10 21.1 10 20V18C10 16.9 10.9 16 12 16Z"
        fill={color}
      />
      <Path
        d="M4 10C2.9 10 2 10.9 2 12S2.9 14 4 14H6C7.1 14 8 13.1 8 12S7.1 10 6 10H4Z"
        fill={color}
      />
      <Path
        d="M18 10C16.9 10 16 10.9 16 12S16.9 14 18 14H20C21.1 14 22 13.1 22 12S21.1 10 20 10H18Z"
        fill={color}
      />
      
      {/* Eyes */}
      <Rect x="9" y="9" width="2" height="2" rx="1" fill={color} />
      <Rect x="13" y="9" width="2" height="2" rx="1" fill={color} />
      
      {/* Nose */}
      <Path
        d="M12 11V13C12 13.55 11.55 14 11 14H10C9.45 14 9 13.55 9 13S9.45 12 10 12H11V11C11 10.45 11.45 10 12 10S13 10.45 13 11Z"
        fill={color}
      />
      
      {/* Corner brackets */}
      <Path
        d="M6 4H4C2.9 4 2 4.9 2 6V8"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        fill="none"
      />
      <Path
        d="M18 4H20C21.1 4 22 4.9 22 6V8"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        fill="none"
      />
      <Path
        d="M6 20H4C2.9 20 2 19.1 2 18V16"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        fill="none"
      />
      <Path
        d="M18 20H20C21.1 20 22 19.1 22 18V16"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        fill="none"
      />
    </Svg>
  );
};

export default FaceIdIcon;