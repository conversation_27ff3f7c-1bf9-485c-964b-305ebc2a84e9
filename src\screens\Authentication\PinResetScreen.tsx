import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
  Alert,
  Animated,
  StatusBar,
  ImageBackground,
  BackHandler,
} from 'react-native';

import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../../types/navigation';
import { useTheme } from '../../components/ThemeContext';
import logger from '../../services/productionLogger';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import { apiService } from '../../services/apiService';
import Svg, { Path } from 'react-native-svg';
import AccountSuspensionModal from '../../components/AccountSuspensionModal';

type PinResetScreenNavigationProp = StackNavigationProp<RootStackParamList, 'PinReset'>;

const PinResetScreen: React.FC = () => {
  const navigation = useNavigation<PinResetScreenNavigationProp>();
  const route = useRoute();

  console.log('🔐 [PIN_RESET] Route params received:', {
    hasParams: !!route.params,
    params: route.params,
    paramsKeys: route.params ? Object.keys(route.params) : 'no params'
  });

  const { resetToken, userData } = (route.params as { resetToken: string; userData?: any }) || {};
  const { theme, isDark } = useTheme();

  const [step, setStep] = useState<'create' | 'confirm'>('create');
  const [pin, setPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showSuspensionModal, setShowSuspensionModal] = useState(false);
  const [suspensionData, setSuspensionData] = useState<{
    reason: string;
    minutesRemaining?: number;
    lockUntil?: string;
  }>({
    reason: ''
  });

  // Animation values
  const shakeAnimation = useRef(new Animated.Value(0)).current;

  // Input refs
  const inputRefs = useRef<TextInput[]>([]);

  console.log('🔐 [PIN_RESET] Screen initialized with:', {
    hasResetToken: !!resetToken,
    hasUserData: !!userData,
    userEmail: userData?.email,
    resetTokenLength: resetToken?.length,
    resetTokenValue: resetToken || 'MISSING'
  });

  // Early validation - if no reset token, show error and go back
  useEffect(() => {
    if (!resetToken) {
      console.error('🔐 [PIN_RESET] No reset token provided!');
      Alert.alert(
        'Invalid Reset Request',
        'Reset token is missing. Please restart the PIN reset process.',
        [
          {
            text: 'OK',
            onPress: () => {
              navigation.reset({
                index: 0,
                routes: [{ name: 'Startup' }],
              });
            }
          }
        ]
      );
    }
  }, [resetToken, navigation]);

  // Prevent back navigation during PIN reset (security measure)
  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        // Exit app directly during PIN reset for security
        BackHandler.exitApp();
        return true; // Prevent default back action
      };

      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () => subscription.remove();
    }, [])
  );

  const triggerHaptic = useCallback((type: 'success' | 'error' | 'warning') => {
    const hapticType = type === 'success' ? 'notificationSuccess' : 
                     type === 'error' ? 'notificationError' : 'notificationWarning';
    ReactNativeHapticFeedback.trigger(hapticType, {
      enableVibrateFallback: true,
      ignoreAndroidSystemSettings: false,
    });
  }, []);

  const shakeInputs = useCallback(() => {
    Animated.sequence([
      Animated.timing(shakeAnimation, { toValue: 10, duration: 100, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 100, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 10, duration: 100, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 0, duration: 100, useNativeDriver: true }),
    ]).start();
  }, [shakeAnimation]);



  const setErrorWithTimeout = useCallback((message: string) => {
    setError(message);
    triggerHaptic('error');
    shakeInputs();
    
    // Clear error after 3 seconds
    setTimeout(() => {
      setError('');
    }, 3000);
  }, [triggerHaptic, shakeInputs]);

  const handlePinChange = useCallback((value: string, index: number) => {
    if (!/^\d*$/.test(value)) return; // Only allow digits
    
    const currentPin = step === 'create' ? pin : confirmPin;
    const newPin = currentPin.substring(0, index) + value + currentPin.substring(index + 1);
    
    if (step === 'create') {
      setPin(newPin);
    } else {
      setConfirmPin(newPin);
    }

    // Clear error when user starts typing
    if (error) {
      setError('');
    }

    // Auto-focus next input
    if (value && index < 3) {
      inputRefs.current[index + 1]?.focus();
    }

    // Auto-advance to confirm step when create PIN is complete
    if (step === 'create' && newPin.length === 4) {
      setTimeout(() => {
        setStep('confirm');
        setTimeout(() => {
          inputRefs.current[0]?.focus();
        }, 100);
      }, 500);
    }

    // Auto-submit when confirm PIN is complete
    if (step === 'confirm' && newPin.length === 4) {
      setTimeout(() => {
        handleResetPin(newPin);
      }, 100);
    }
  }, [step, pin, confirmPin, error]);

  const handleKeyPress = useCallback((key: string, index: number) => {
    if (key === 'Backspace') {
      const currentPin = step === 'create' ? pin : confirmPin;
      if (!currentPin[index] && index > 0) {
        // Move to previous input if current is empty
        inputRefs.current[index - 1]?.focus();
      }
    }
  }, [step, pin, confirmPin]);

  const handleResetPin = useCallback(async (confirmPinValue: string = confirmPin) => {
    console.log('🔐 [PIN_RESET] Starting PIN reset process');
    
    if (pin !== confirmPinValue) {
      setErrorWithTimeout('PINs do not match. Please try again.');
      setStep('create');
      setPin('');
      setConfirmPin('');
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 100);
      return;
    }

    if (pin.length !== 4) {
      setErrorWithTimeout('PIN must be 4 digits');
      return;
    }

    setLoading(true);
    setError('');

    try {
      console.log('🔐 [PIN_RESET] Calling reset PIN API with token:', {
        hasResetToken: !!resetToken,
        resetTokenLength: resetToken?.length,
        resetTokenPreview: resetToken ? `${resetToken.substring(0, 10)}...` : 'null',
        pinLength: pin.length
      });

      if (!resetToken) {
        throw new Error('Reset token is missing. Please restart the reset process.');
      }

      const response = await apiService.resetPin(resetToken, pin);

      if (response.data?.status === 'success') {
        console.log('✅ [PIN_RESET] PIN reset completed successfully');
        triggerHaptic('success');

        // Navigate to loading screen with custom message, then to PIN verification
        navigation.replace('SetupLoading', {
          message: 'PIN reset successful! Securing your session...',
          next: 'PinVerification',
          userData: userData // Pass user data for PIN verification
        });
      } else {
        throw new Error(response.data?.message || response.message || 'Failed to reset PIN');
      }

    } catch (error: any) {
      console.log('❌ [PIN_RESET] Error:', error.message);
      logger.error('PIN reset failed', error, 'auth');

      // Check if it's an account lock error
      if (error.status === 423 || error.message?.includes('ACCOUNT_LOCKED') || error.message?.includes('temporarily locked')) {
        logger.security('ACCOUNT_LOCKED_PIN_RESET', {
          error: error.message
        });

        // Extract minutes remaining from error message if available
        const minutesMatch = error.message?.match(/(\d+)\s+minute/);
        const minutes = minutesMatch ? parseInt(minutesMatch[1]) : null;

        // Extract lock until timestamp if available
        let lockUntil = '';
        try {
          const errorData = typeof error.response?.data === 'object' ? error.response.data : {};
          lockUntil = errorData.lockUntil || '';
        } catch (e) {
          // Ignore parsing errors
        }

        // Show suspension modal instead of error message
        setSuspensionData({
          reason: 'Too many failed PIN attempts. Your account has been temporarily locked for security.',
          minutesRemaining: minutes || undefined,
          lockUntil: lockUntil || undefined
        });
        setShowSuspensionModal(true);

        // Don't reset form for lock errors, just show the modal
        return;
      } else if (error.message?.includes('cannot be the same')) {
        setErrorWithTimeout('New PIN cannot be the same as your current PIN. Please choose a different PIN.');
      } else {
        setErrorWithTimeout(error.message || 'Failed to reset PIN. Please try again.');
      }

      setStep('create');
      setPin('');
      setConfirmPin('');
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 100);
    } finally {
      setLoading(false);
    }
  }, [pin, confirmPin, resetToken, userData, navigation, triggerHaptic, setErrorWithTimeout]);

  // Back navigation removed for security during PIN reset

  const currentPin = step === 'create' ? pin : confirmPin;

  return (
    <View style={styles.container}>
      <StatusBar barStyle={isDark ? "light-content" : "dark-content"} backgroundColor="transparent" translucent={true} />
      <ImageBackground
        source={isDark ? require("../../../assets/images/bg.jpeg") : require("../../../assets/images/bg-white.jpeg")}
        style={StyleSheet.absoluteFillObject}
        resizeMode="cover"
      />

      {/* Header - No back button for security */}
      <SafeAreaView style={styles.header}>
        {/* Back navigation disabled during PIN reset for security */}
      </SafeAreaView>

      {/* Content */}
      <SafeAreaView style={styles.content}>
        <Text style={[styles.title, { color: theme.colors.text }]}>
          {step === 'create' ? 'Reset Your PIN' : 'Confirm New PIN'}
        </Text>
        <Text style={[styles.subtitle, { color: theme.colors.muted }]}>
          {step === 'create' 
            ? 'Create a new 4-digit PIN to replace your current one'
            : 'Re-enter your new PIN to confirm the reset'
          }
        </Text>

        {/* PIN Input */}
        <Animated.View
          style={[
            styles.pinContainer,
            { transform: [{ translateX: shakeAnimation }] }
          ]}
        >
          {[0, 1, 2, 3].map((index) => (
            <TextInput
              key={index}
              ref={(ref) => {
                if (ref) inputRefs.current[index] = ref
              }}
              style={[
                styles.pinInput,
                {
                  backgroundColor: 'transparent',
                  borderBottomColor: currentPin[index] ? '#8000FF' : theme.colors.border,
                  color: theme.colors.text,
                }
              ]}
              value={currentPin[index] || ''}
              onChangeText={(value) => handlePinChange(value, index)}
              onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
              keyboardType="numeric"
              maxLength={1}
              autoFocus={index === 0}
              editable={!loading}
              secureTextEntry
            />
          ))}
        </Animated.View>

        {error && (
          <Text style={[styles.errorText, { color: '#EF4444' }]}>{error}</Text>
        )}

        <Text style={[styles.instructionText, { color: theme.colors.muted }]}>
          {step === 'create'
            ? 'Choose a new PIN different from your current one'
            : 'Ensure both PINs match exactly'
          }
        </Text>


      </SafeAreaView>

      {/* Account Suspension Modal */}
      <AccountSuspensionModal
        visible={showSuspensionModal}
        onClose={() => setShowSuspensionModal(false)}
        reason={suspensionData.reason}
        minutesRemaining={suspensionData.minutesRemaining}
        lockUntil={suspensionData.lockUntil}
      />
    </View>
  );
};

const BackArrowIcon = ({ color, size }: { color: string, size: number }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path
      d="M20 11.5H9.41L14.46 6.46C14.85 6.07 14.85 5.44 14.46 5.05C14.27 4.86 14.02 4.76 13.76 4.76C13.5 4.76 13.25 4.86 13.05 5.05L6.46 11.64C6.07 12.03 6.07 12.66 6.46 13.05L13.05 19.64C13.44 20.03 14.07 20.03 14.46 19.64C14.85 19.25 14.85 18.62 14.46 18.23L9.41 13.18H20C20.55 13.18 21 12.73 21 12.18C21 11.63 20.55 11.18 20 11.18V11.5Z"
      fill={color}
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 32,
    justifyContent: 'center',
    paddingBottom: 100,
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 12,
    letterSpacing: 0.3,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 22,
  },
  pinContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
    marginBottom: 20,
  },
  pinInput: {
    width: 56,
    height: 56,
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    borderBottomWidth: 2,
  },
  errorText: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
    marginBottom: 20,
  },
  instructionText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 20,
  },

});

export default PinResetScreen;
