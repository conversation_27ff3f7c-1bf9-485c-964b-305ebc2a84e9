import notifee, { AndroidImportance } from '@notifee/react-native';
import { Platform } from 'react-native';
// import FlashMessage from 'react-native-flash-message'; // Uncomment if you want to use flash messages
import apiService from './apiService';
import logger from './productionLogger';
// Import Firebase services
import { firebaseInitService } from './firebaseInitService';
import messaging from '@react-native-firebase/messaging';
import { getApp } from '@react-native-firebase/app';
import deviceInfoService from './deviceInfoService';

// Helper function to get messaging instance with proper app
function getMessaging() {
  try {
    const app = getApp();
    return messaging();
  } catch (error) {
    // Fallback to default messaging if app is not available
    return messaging();
  }
}

export async function requestUserPermission() {
  try {
    // Ensure Firebase is initialized before requesting permission
    const firebaseReady = await firebaseInitService.initialize();

    if (!firebaseReady) {
      logger.error('Cannot request permission - Firebase not initialized');
      return false;
    }

    const messagingInstance = getMessaging();
    const authStatus = await messagingInstance.requestPermission();
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;
    return enabled;
  } catch (error) {
    logger.error('Failed to request notification permission:', error instanceof Error ? error.message : String(error));
    return false;
  }
}

export async function getFcmToken() {
  try {
    // Ensure Firebase is initialized before getting token
    const firebaseReady = await firebaseInitService.initialize();

    if (!firebaseReady) {
      throw new Error('Firebase initialization failed');
    }

    const messagingInstance = getMessaging();
    const token = await messagingInstance.getToken();
    return token;
  } catch (error) {
    logger.error('❌ [NOTIFICATIONS] Failed to get FCM token:', error);
    throw error;
  }
}

// Notification categories matching backend
export const NOTIFICATION_CATEGORIES = {
  TRANSACTION: 'transaction',
  SECURITY: 'security',
  ACCOUNT: 'account',
  MARKETING: 'marketing',
  SYSTEM: 'system'
} as const;

export const SECURITY_LEVELS = {
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low'
} as const;

export async function displayNotification(title: string, body: string, data?: any) {
  const category = data?.category || NOTIFICATION_CATEGORIES.SYSTEM;
  const securityLevel = data?.securityLevel || SECURITY_LEVELS.LOW;

  // Choose appropriate channel and settings based on category
  const channelId = category === NOTIFICATION_CATEGORIES.TRANSACTION ? 'transactions' : 'default';
  const importance = securityLevel === SECURITY_LEVELS.HIGH ? AndroidImportance.HIGH : AndroidImportance.DEFAULT;

  await notifee.displayNotification({
    title,
    body,
    data: data || {},
    android: {
      channelId,
      importance,
      // High security notifications are persistent
      ongoing: securityLevel === SECURITY_LEVELS.HIGH,
      // Add security icon for transaction/security notifications
      smallIcon: category === NOTIFICATION_CATEGORIES.TRANSACTION || category === NOTIFICATION_CATEGORIES.SECURITY
        ? 'ic_security'
        : 'ic_notification',
      color: category === NOTIFICATION_CATEGORIES.TRANSACTION ? '#FF6B35' : '#007AFF',
      // Require authentication for high security notifications
      actions: securityLevel === SECURITY_LEVELS.HIGH ? [
        {
          title: 'View Details',
          pressAction: { id: 'view_details', launchActivity: 'default' },
        }
      ] : undefined
    },
    ios: {
      sound: securityLevel === SECURITY_LEVELS.HIGH ? 'critical.wav' : 'default',
      categoryId: category,
      interruptionLevel: securityLevel === SECURITY_LEVELS.HIGH ? 'critical' : 'active'
    }
  });
}

export async function createDefaultChannel() {
  if (Platform.OS === 'android') {
    // Create default channel
    await notifee.createChannel({
      id: 'default',
      name: 'General Notifications',
      importance: AndroidImportance.DEFAULT,
      description: 'General app notifications'
    });

    // Create high-security transaction channel
    await notifee.createChannel({
      id: 'transactions',
      name: 'Transactions',
      importance: AndroidImportance.HIGH,
      description: 'Transaction alerts and confirmations',
      sound: 'default',
      vibration: true,
      lights: true,
      lightColor: '#FF6B35'
    });

    // Create security alerts channel
    await notifee.createChannel({
      id: 'security',
      name: 'Security Alerts',
      importance: AndroidImportance.HIGH,
      description: 'Critical security notifications',
      sound: 'default',
      vibration: true,
      lights: true,
      lightColor: '#FF0000'
    });
  }
}

export async function setupNotificationListeners() {
  try {
    // Ensure Firebase is initialized before setting up listeners
    const firebaseReady = await firebaseInitService.initialize();

    if (!firebaseReady) {
      logger.error('Cannot setup notification listeners - Firebase not initialized');
      return;
    }

    const messagingInstance = getMessaging();

    // Foreground message handler with enhanced security
    messagingInstance.onMessage(async remoteMessage => {
      const { title, body } = remoteMessage.notification || {};
      const data = remoteMessage.data || {};

      if (title && body) {
        // Log notification receipt for audit trail
        logger.info('📱 [NOTIFICATIONS] Received foreground notification', {
          category: data.category || 'unknown',
          securityLevel: data.securityLevel || 'low',
          timestamp: data.timestamp
        });

        await displayNotification(title, body, data);

        // For high security notifications, also show in-app alert
        if (data.securityLevel === SECURITY_LEVELS.HIGH) {
          // FlashMessage.show({
          //   message: title,
          //   description: body,
          //   type: 'warning',
          //   duration: 5000
          // });
        }
      }
    });

    // Background/quit state handler
    messagingInstance.setBackgroundMessageHandler(async remoteMessage => {
      const { title, body } = remoteMessage.notification || {};
      if (title && body) {
        await displayNotification(title, body);
      }
    });

    logger.info('✅ [NOTIFICATIONS] Notification listeners setup completed');
  } catch (error) {
    logger.error('❌ [NOTIFICATIONS] Failed to setup notification listeners:', error instanceof Error ? error.message : String(error));
  }
}

// Call this in your App.tsx or entry point
export async function initializeNotifications() {
  try {
    logger.info('🔔 [NOTIFICATIONS] Starting notification initialization...');

    // Ensure Firebase is initialized first using centralized initialization
    const firebaseReady = await firebaseInitService.initialize();

    if (!firebaseReady) {
      throw new Error('Firebase initialization failed');
    }

    await createDefaultChannel();
    await requestUserPermission();
    await setupNotificationListeners();

    logger.info('✅ [NOTIFICATIONS] Notification initialization completed');
  } catch (error) {
    logger.error('❌ [NOTIFICATIONS] Notification initialization failed:', error);
    throw error;
  }
}

/**
 * Register the FCM token with the backend after login/signup or on token refresh
 */
export async function registerFcmTokenWithBackend(userId: string) {
  try {
    logger.info('🔔 [NOTIFICATIONS] Attempting to register FCM token for user:', userId);

    // Ensure Firebase is initialized using centralized initialization
    const firebaseReady = await firebaseInitService.initialize();

    if (!firebaseReady) {
      throw new Error('Firebase not initialized');
    }

    const fcmToken = await getFcmToken();
    if (!fcmToken) throw new Error('No FCM token available');

    // Get device information for registration
    logger.info('🔔 [NOTIFICATIONS] Collecting device information for FCM registration...');
    const deviceInfo = await deviceInfoService.getFcmDeviceInfo();

    // Log detailed device info for debugging
    logger.info('🔔 [NOTIFICATIONS] Device info collected for FCM registration', {
      deviceId: deviceInfo.deviceId,
      platform: deviceInfo.platform,
      osVersion: deviceInfo.osVersion,
      deviceModel: deviceInfo.deviceModel,
      deviceBrand: deviceInfo.deviceBrand,
      appVersion: deviceInfo.appVersion,
      buildNumber: deviceInfo.buildNumber,
      isEmulator: deviceInfo.isEmulator,
      timezone: deviceInfo.timezone,
      hasUnknownFields: Object.values(deviceInfo).includes('unknown'),
    });

    logger.info('🔔 [NOTIFICATIONS] Got FCM token and device info, registering with backend...', {
      tokenLength: fcmToken.length,
      userId: userId,
      devicePlatform: deviceInfo.platform,
      deviceModel: deviceInfo.deviceModel,
      appVersion: deviceInfo.appVersion,
    });

    // Use authenticated request (don't skip auth)
    const response = await apiService.post('/notification/register-fcm-token', {
      userId,
      fcmToken,
      deviceInfo,
    });

    logger.info('✅ [NOTIFICATIONS] FCM token registered with backend', {
      userId: userId,
      response: response.status || 'success',
      deviceRegistered: true,
    });
  } catch (error) {
    logger.error('❌ [NOTIFICATIONS] Failed to register FCM token:', {
      userId: userId,
      error: error instanceof Error ? error.message : String(error),
      errorDetails: error instanceof Error ? {
        name: error.name,
        stack: error.stack
      } : error,
      // Include response details if it's an API error
      ...(error && typeof error === 'object' && 'response' in error ? {
        status: (error as any).response?.status,
        statusText: (error as any).response?.statusText,
        responseData: (error as any).response?.data
      } : {})
    });
  }
}

/**
 * Listen for FCM token refresh and re-register with backend
 */
export function listenForFcmTokenRefresh(userId: string) {
  const messaging = require('@react-native-firebase/messaging').default;
  messaging().onTokenRefresh(async (newToken: string) => {
    try {
      logger.info('🔄 [NOTIFICATIONS] FCM token refreshed, re-registering...', {
        userId: userId,
        tokenLength: newToken.length
      });

      // Get device information for registration
      logger.info('🔄 [NOTIFICATIONS] Collecting device info for token refresh...');
      const deviceInfo = await deviceInfoService.getFcmDeviceInfo();

      logger.info('🔄 [NOTIFICATIONS] Device info for token refresh', {
        deviceId: deviceInfo.deviceId,
        platform: deviceInfo.platform,
        deviceModel: deviceInfo.deviceModel,
        hasUnknownFields: Object.values(deviceInfo).includes('unknown'),
      });

      // Use authenticated request (don't skip auth)
      await apiService.post('/notification/register-fcm-token', {
        userId,
        fcmToken: newToken,
        deviceInfo,
      });

      logger.info('✅ [NOTIFICATIONS] FCM token refreshed and registered', {
        userId: userId,
        deviceUpdated: true,
      });
    } catch (error) {
      logger.error('❌ [NOTIFICATIONS] Failed to register refreshed FCM token:', {
        userId: userId,
        error: error instanceof Error ? error.message : String(error),
        errorDetails: error instanceof Error ? {
          name: error.name,
          stack: error.stack
        } : error
      });
    }
  });
}
