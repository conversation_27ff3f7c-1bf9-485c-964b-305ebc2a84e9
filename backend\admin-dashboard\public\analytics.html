<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics - <PERSON><PERSON><PERSON></title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="/js/dashboard.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .nav {
            display: flex;
            gap: 2rem;
        }

        .nav a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: background 0.3s ease;
        }

        .nav a:hover, .nav a.active {
            background: rgba(255, 255, 255, 0.2);
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .page-title {
            font-size: 2rem;
            margin-bottom: 2rem;
            color: #333;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .chart-container {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .chart-title {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            color: #333;
        }

        .coming-soon {
            text-align: center;
            padding: 4rem 2rem;
            color: #666;
        }

        .coming-soon i {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #667eea;
        }

        .coming-soon h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .nav {
                gap: 1rem;
            }

            .container {
                padding: 0 1rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-chart-line"></i> Vendy Admin
            </div>
            <nav class="nav">
                <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="/ota-updates"><i class="fas fa-download"></i> OTA Updates</a>
                <a href="/analytics" class="active"><i class="fas fa-chart-bar"></i> Analytics</a>
                <a href="/settings"><i class="fas fa-cog"></i> Settings</a>
            </nav>
            <div class="user-menu">
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </div>
    </header>

    <div class="container">
        <h1 class="page-title">
            <i class="fas fa-chart-bar"></i> Analytics Dashboard
        </h1>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon" style="color: #10b981;">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-value" id="totalUsers">-</div>
                <div class="stat-label">Total Users</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="color: #3b82f6;">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <div class="stat-value" id="activeDevices">-</div>
                <div class="stat-label">Active Devices</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="color: #f59e0b;">
                    <i class="fas fa-download"></i>
                </div>
                <div class="stat-value" id="totalDownloads">-</div>
                <div class="stat-label">App Downloads</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon" style="color: #ef4444;">
                    <i class="fas fa-sync-alt"></i>
                </div>
                <div class="stat-value" id="updateRate">-</div>
                <div class="stat-label">Update Success Rate</div>
            </div>
        </div>

        <div class="chart-container">
            <h3 class="chart-title">User Growth Over Time</h3>
            <div class="coming-soon">
                <i class="fas fa-chart-line"></i>
                <h3>Analytics Coming Soon</h3>
                <p>Advanced analytics and reporting features are currently in development.</p>
            </div>
        </div>

        <div class="chart-container">
            <h3 class="chart-title">App Update Statistics</h3>
            <div class="coming-soon">
                <i class="fas fa-pie-chart"></i>
                <h3>Update Analytics Coming Soon</h3>
                <p>Detailed update deployment and success metrics will be available soon.</p>
            </div>
        </div>
    </div>

    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadAnalytics();
        });

        async function loadAnalytics() {
            try {
                // Placeholder data - replace with actual API calls
                document.getElementById('totalUsers').textContent = '1,234';
                document.getElementById('activeDevices').textContent = '987';
                document.getElementById('totalDownloads').textContent = '5,678';
                document.getElementById('updateRate').textContent = '98.5%';
            } catch (error) {
                console.error('Failed to load analytics:', error);
            }
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                fetch('/logout', { method: 'POST' })
                    .then(() => {
                        window.location.href = '/login';
                    });
            }
        }
    </script>
</body>
</html>
