import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface SpeedIconProps {
  size?: number;
  color?: string;
}

const SpeedIcon: React.FC<SpeedIconProps> = ({ 
  size = 24, 
  color = '#007AFF' 
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M20.38 8.57L19.13 9.82C20.6 11.54 20.6 14.19 19.13 15.91L20.38 17.16C22.39 14.74 22.39 11.19 20.38 8.57Z"
        fill={color}
      />
      <Path
        d="M17.96 10.99L16.71 12.24C17.32 12.85 17.32 13.83 16.71 14.44L17.96 15.69C19.17 14.48 19.17 12.2 17.96 10.99Z"
        fill={color}
      />
      <Path
        d="M9.76 12.24L8.51 10.99C7.3 12.2 7.3 14.48 8.51 15.69L9.76 14.44C9.15 13.83 9.15 12.85 9.76 12.24Z"
        fill={color}
      />
      <Path
        d="M7.09 9.82L5.84 8.57C3.83 11.19 3.83 14.74 5.84 17.16L7.09 15.91C5.62 14.19 5.62 11.54 7.09 9.82Z"
        fill={color}
      />
      <Path
        d="M12 10C13.1 10 14 10.9 14 12S13.1 14 12 14S10 13.1 10 12S10.9 10 12 10Z"
        fill={color}
      />
    </Svg>
  );
};

export default SpeedIcon;