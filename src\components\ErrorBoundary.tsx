import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { useTheme } from './ThemeContext';
import { logger } from '../services/productionLogger';
import crashReporting from '../services/crashReportingService';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundaryClass extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log to crash reporting service (async, but don't block rendering)
    this.logErrorToService(error, errorInfo).catch((logError) => {
      // If logging fails, at least log to console in development
      if (__DEV__) {
        console.error('Failed to log error to service:', logError);
      }
    });

    // Call custom error handler
    this.props.onError?.(error, errorInfo);
  }

  logErrorToService = async (error: Error, errorInfo: ErrorInfo) => {
    // Log the error with detailed context
    logger.error('🔥 [ErrorBoundary] Caught error:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
    });

    // Add breadcrumb for the error boundary catch
    crashReporting.addBreadcrumb({
      category: 'error_boundary',
      message: 'Error caught by ErrorBoundary',
      level: 'error',
      data: {
        errorMessage: error.message,
        hasComponentStack: !!errorInfo.componentStack,
      },
    });

    // Record the error with crash reporting service
    try {
      await crashReporting.recordError(error, errorInfo.componentStack || undefined);
      logger.info('Error successfully reported to crash service');
    } catch (reportError) {
      logger.error('Failed to report error to crash service:', reportError);

      // Fallback: record as non-fatal error
      try {
        await crashReporting.recordNonFatalError('ErrorBoundary catch failed to report', {
          originalError: error.message,
          reportError: reportError instanceof Error ? reportError.message : String(reportError),
        });
      } catch (fallbackError) {
        logger.error('Complete crash reporting failure:', fallbackError);
      }
    }
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return <ErrorFallback error={this.state.error} onRetry={this.handleRetry} />;
    }

    return this.props.children;
  }
}

// Error Fallback Component
const ErrorFallback: React.FC<{
  error: Error | null;
  onRetry: () => void;
}> = ({ error, onRetry }) => {
  const { theme } = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    title: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: 16,
      textAlign: 'center',
    },
    message: {
      fontSize: 16,
      color: theme.colors.muted,
      textAlign: 'center',
      marginBottom: 24,
      lineHeight: 24,
    },
    errorDetails: {
      backgroundColor: theme.colors.card,
      borderRadius: 8,
      padding: 16,
      marginBottom: 24,
      maxHeight: 200,
      width: '100%',
    },
    errorText: {
      fontSize: 12,
      color: theme.colors.muted,
      fontFamily: 'monospace',
    },
    retryButton: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 32,
      paddingVertical: 12,
      borderRadius: 8,
      marginBottom: 16,
    },
    retryButtonText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
    },
    reportButton: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      paddingHorizontal: 32,
      paddingVertical: 12,
      borderRadius: 8,
    },
    reportButtonText: {
      color: theme.colors.text,
      fontSize: 16,
      fontWeight: '600',
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.title}>Oops! Something went wrong</Text>
      <Text style={styles.message}>
        We're sorry for the inconvenience. The app encountered an unexpected error.
      </Text>
      
      {__DEV__ && error && (
        <ScrollView style={styles.errorDetails}>
          <Text style={styles.errorText}>
            {error.message}
            {'\n\n'}
            {error.stack}
          </Text>
        </ScrollView>
      )}

      <TouchableOpacity style={styles.retryButton} onPress={onRetry}>
        <Text style={styles.retryButtonText}>Try Again</Text>
      </TouchableOpacity>

      <TouchableOpacity style={styles.reportButton} onPress={async () => {
        logger.info('Report error tapped');

        // Record user action
        crashReporting.recordUserAction('error_report_requested', {
          errorMessage: error?.message,
          timestamp: new Date().toISOString(),
        });

        // Add breadcrumb for user reporting the error
        crashReporting.addBreadcrumb({
          category: 'user',
          message: 'User requested error report',
          level: 'info',
          data: { source: 'error_boundary_fallback' },
        });

        // In a real app, you might:
        // - Open email client with pre-filled error details
        // - Navigate to feedback screen
        // - Show a modal with error reporting options
        // For now, we'll just ensure the error is recorded
        if (error) {
          try {
            await crashReporting.recordNonFatalError('User reported error from ErrorBoundary', {
              errorMessage: error.message,
              userInitiated: true,
            });
          } catch (reportError) {
            logger.error('Failed to record user-reported error:', reportError);
          }
        }
      }}>
        <Text style={styles.reportButtonText}>Report Issue</Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
};

// HOC wrapper for functional components
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode,
  onError?: (error: Error, errorInfo: ErrorInfo) => void
) => {
  return (props: P) => (
    <ErrorBoundaryClass fallback={fallback} onError={onError}>
      <Component {...props} />
    </ErrorBoundaryClass>
  );
};

export default ErrorBoundaryClass;