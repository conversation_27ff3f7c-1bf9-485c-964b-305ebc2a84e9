import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
  Alert,
  Vibration,
  Animated,
  StatusBar,
  Image,
  ImageBackground,
  BackHandler,
} from 'react-native';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../../types/navigation';
import { setupService } from '../../services/setupService';
import { useTheme } from '../../components/ThemeContext';
import {
  handleAuthError,
  verifyPinSecurely,
  storePinSecurely,
  validatePinFormat
} from '../../utils/authUtils';
import SetupLoadingScreen from '../Onboarding/SetupLoadingScreen';
import GlassyBox from '../../components/GlassyBox';
import logger from '../../services/productionLogger';
import BiometricSetupModal from '../../components/BiometricSetupModal';
import VendyLoader from '../../components/VendyLoader';
import { BlurView } from '@react-native-community/blur';
import { apiService } from '../../services/apiService';
import AccountSuspensionModal from '../../components/AccountSuspensionModal';

type PinSetupScreenNavigationProp = StackNavigationProp<RootStackParamList, 'PinSetup'>;



const PinSetupScreen: React.FC = () => {
  const navigation = useNavigation<PinSetupScreenNavigationProp>();
  const route = useRoute();
  const { userData } = route.params as { userData?: any };
  const { theme, isDark } = useTheme();

  console.log('🔐 [PIN_SETUP] Screen initialized for PIN setup only');

  const [mode, setMode] = useState<'setup' | 'verify' | 'loading'>('loading');
  const [step, setStep] = useState<'create' | 'confirm'>('create');
  const [pin, setPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [imageError, setImageError] = useState(false);
  const [mismatchedIndexes, setMismatchedIndexes] = useState<number[]>([]);
  const [hasConfirmMismatch, setHasConfirmMismatch] = useState(false);
  const [showBiometricModal, setShowBiometricModal] = useState(false);
  const [showSuspensionModal, setShowSuspensionModal] = useState(false);
  const [suspensionData, setSuspensionData] = useState<{
    reason: string;
    minutesRemaining?: number;
    lockUntil?: string;
  }>({
    reason: ''
  });

  // Request deduplication for PIN verification mode
  const [isRequestInProgress, setIsRequestInProgress] = useState(false);
  const requestIdRef = useRef<string | null>(null);

  // Animation values
  const fadeAnimation = useRef(new Animated.Value(1)).current;
  const shakeAnimation = useRef(new Animated.Value(0)).current;

  // Input refs
  const inputRefs = useRef<TextInput[]>([]);

  useEffect(() => {
    // Check if user needs PIN setup or verification
    checkPinFlowType();
  }, []);

  // Disable back navigation during PIN setup (security measure)
  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        // Only prevent back navigation for new users in setup mode
        // Allow back for confirmation step within PIN setup
        if (mode === 'setup' && step === 'create') {
          // Show confirmation dialog for first-time PIN creation
          Alert.alert(
            'Exit Setup?',
            'You need to create a PIN to secure your account. Are you sure you want to exit?',
            [
              {
                text: 'Continue Setup',
                style: 'cancel',
              },
              {
                text: 'Exit App',
                style: 'destructive',
                onPress: () => BackHandler.exitApp(),
              },
            ]
          );
          return true; // Prevent default back action
        } else if (mode === 'setup' && step === 'confirm') {
          // Allow going back to create step
          setStep('create');
          setConfirmPin('');
          setTimeout(() => {
            inputRefs.current[0]?.focus();
          }, 100);
          return true; // Prevent default back action
        }
        
        // For verify mode, allow normal back navigation
        return false;
      };

      const subscription = BackHandler.addEventListener('hardwareBackPress', onBackPress);
      return () => subscription.remove();
    }, [mode, step])
  );

  useEffect(() => {
    // Auto-focus first input when mode is determined
    if (mode !== 'loading') {
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 100);
    }
  }, [mode]);



  const checkPinFlowType = async () => {
    try {
      console.log('🔐 [PIN_SETUP] checkPinFlowType called');

      const flowType = await setupService.getPinFlowType();
      console.log('🔐 [PIN_SETUP] Flow type received:', flowType);

      if (flowType === 'complete') {
        // Setup is complete, go to main app
        console.log('🔐 [PIN_SETUP] Flow type is complete, navigating to MainTabs');
        navigation.navigate('MainTabs');
        return;
      }

      console.log('🔐 [PIN_SETUP] Setting mode to:', flowType);
      setMode(flowType);
    } catch (error) {
      console.log('🔐 [PIN_SETUP] Error checking PIN flow type:', error);
      logger.error('Error checking PIN flow type', error, 'setup');
      // Default to setup if we can't determine
      console.log('🔐 [PIN_SETUP] Defaulting to setup mode due to error');
      setMode('setup');
    }
  };

  const handlePinChange = (value: string, index: number) => {
    const currentPin = mode === 'verify' ? pin : (step === 'create' ? pin : confirmPin);
    const newPin = currentPin.substring(0, index) + value + currentPin.substring(index + 1);
    
    // Subtle haptic feedback for each keystroke
    if (value) {
      ReactNativeHapticFeedback.trigger('impactLight', {
        enableVibrateFallback: true,
        ignoreAndroidSystemSettings: false,
      });
    }
    
    if (mode === 'verify') {
      setPin(newPin);
    } else if (step === 'create') {
      setPin(newPin);
      setError(''); // Clear any previous errors immediately when typing
      setHasConfirmMismatch(false); // Reset confirm mismatch state
    } else {
      setConfirmPin(newPin);
      setError(''); // Clear any previous errors immediately when typing
      
      // Real-time PIN mismatch detection
      if (value && pin[index] && value !== pin[index]) {
        // Add this index to mismatched indexes
        setMismatchedIndexes(prev => {
          if (!prev.includes(index)) {
            return [...prev, index];
          }
          return prev;
        });
        setHasConfirmMismatch(true);
        
        // Medium haptic for mismatch
        ReactNativeHapticFeedback.trigger('impactMedium', {
          enableVibrateFallback: true,
          ignoreAndroidSystemSettings: false,
        });
      } else if (value && pin[index] && value === pin[index]) {
        // Remove this index from mismatched indexes if it now matches
        setMismatchedIndexes(prev => prev.filter(i => i !== index));
        
        // Check if all mismatches are cleared
        if (mismatchedIndexes.length === 1 && mismatchedIndexes.includes(index)) {
          setHasConfirmMismatch(false);
        }
      }
    }

    // Auto-focus next input
    if (value && index < 3) {
      inputRefs.current[index + 1]?.focus();
    }

    // Auto-submit when PIN is complete
    if (newPin.length === 4) {
      if (mode === 'verify') {
        // Submit PIN for verification
        setTimeout(() => {
          handleSubmit(newPin);
        }, 300);
      } else if (step === 'create') {
        // Move to confirm step
        setTimeout(() => {
          setStep('confirm');
          setTimeout(() => {
            inputRefs.current[0]?.focus();
          }, 100);
        }, 300);
      } else {
        // Submit PIN for setup
        setTimeout(() => {
          handleSubmit(newPin);
        }, 300);
      }
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace') {
      const currentPin = mode === 'verify' ? pin : (step === 'create' ? pin : confirmPin);
      
      if (currentPin[index]) {
        // Clear current input
        const newPin = currentPin.substring(0, index) + currentPin.substring(index + 1);
        
        if (mode === 'verify') {
          setPin(newPin);
        } else if (step === 'create') {
          setPin(newPin);
        } else {
          setConfirmPin(newPin);
          // Clear mismatch state for this index when deleting
          setMismatchedIndexes(prev => prev.filter(i => i !== index));
        }
      } else if (index > 0) {
        // Move to previous input if current is empty and clear it
        const prevIndex = index - 1;
        const newPin = currentPin.substring(0, prevIndex) + currentPin.substring(prevIndex + 1);
        
        if (mode === 'verify') {
          setPin(newPin);
        } else if (step === 'create') {
          setPin(newPin);
        } else {
          setConfirmPin(newPin);
          // Clear mismatch state for previous index
          setMismatchedIndexes(prev => prev.filter(i => i !== prevIndex));
        }
        
        inputRefs.current[prevIndex]?.focus();
      }
    }
  };

  const shakeInputs = () => {
    Vibration.vibrate(400);
    Animated.sequence([
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnimation, { toValue: 0, duration: 50, useNativeDriver: true }),
    ]).start();
  };

  const handleSubmit = async (pinToSubmit: string) => {
    if (mode === 'verify') {
      // PIN verification mode
      await handlePinVerification(pinToSubmit);
    } else {
      // PIN setup mode
      await handlePinSetup(pinToSubmit);
    }
  };

  const handlePinVerification = async (pinToSubmit: string) => {
    if (loading || isRequestInProgress) return;

    // Generate unique request ID to prevent duplicate requests
    const currentRequestId = Date.now().toString() + Math.random().toString(36).substring(2, 11);
    requestIdRef.current = currentRequestId;

    setLoading(true);
    setIsRequestInProgress(true);
    setError('');

    try {
      // First validate PIN format
      const validation = validatePinFormat(pinToSubmit);
      if (!validation.isValid) {
        setError(validation.error!);
        shakeInputs();
        setPin('');
        setTimeout(() => {
          inputRefs.current[0]?.focus();
        }, 100);
        return;
      }

      // Use backend PIN verification (consistent with PIN verification screen)
      const result = await setupService.verifyPin({ pin: pinToSubmit });

      // Check if this is still the current request (prevent race conditions)
      if (requestIdRef.current !== currentRequestId) {
        logger.info('Ignoring outdated PIN verification response', { requestId: currentRequestId }, 'auth');
        return;
      }
      
      if (result && result.pinVerified) {
        // Success haptic feedback
        ReactNativeHapticFeedback.trigger('notificationSuccess', {
          enableVibrateFallback: true,
          ignoreAndroidSystemSettings: false,
        });

        // Navigate to SetupLoadingScreen with custom message
        logger.info('Navigating to SetupLoadingScreen', null, 'auth');
        navigation.navigate('SetupLoading', {
          message: "Securing your session",
          next: 'MainTabs'
        });
      } else {
        // This shouldn't happen as the API throws on failure, but handle just in case
        logger.warn('PIN verification failed - invalid result', { result }, 'auth');
        setError('Invalid PIN. Please try again.');
        shakeInputs();
        setPin('');
        setTimeout(() => {
          inputRefs.current[0]?.focus();
        }, 100);

        // Strong haptic for error
        ReactNativeHapticFeedback.trigger('notificationError', {
          enableVibrateFallback: true,
          ignoreAndroidSystemSettings: false,
        });
      }
      
    } catch (error: any) {
      logger.error('PIN verification error', error, 'auth');

      // Check if it's an account lock error
      if (error.status === 423 || error.message?.includes('ACCOUNT_LOCKED') || error.message?.includes('temporarily locked')) {
        logger.security('ACCOUNT_LOCKED_PIN_SETUP_VERIFICATION', {
          error: error.message
        });

        // Extract minutes remaining from error message if available
        const minutesMatch = error.message?.match(/(\d+)\s+minute/);
        const minutes = minutesMatch ? parseInt(minutesMatch[1]) : null;

        // Extract lock until timestamp if available
        let lockUntil = '';
        try {
          const errorData = typeof error.response?.data === 'object' ? error.response.data : {};
          lockUntil = errorData.lockUntil || '';
        } catch (e) {
          // Ignore parsing errors
        }

        // Show suspension modal instead of error message
        setSuspensionData({
          reason: 'Too many failed PIN attempts. Your account has been temporarily locked for security.',
          minutesRemaining: minutes || undefined,
          lockUntil: lockUntil || undefined
        });
        setShowSuspensionModal(true);

        // Don't reset form for lock errors
        return;
      }

      // Check if it's an authentication error
      const authErrorHandled = await handleAuthError(error, navigation);
      if (authErrorHandled) {
        return;
      }

      setError(error.message || 'Unable to verify PIN. Please try again.');
      shakeInputs();
      setPin('');
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 100);
    } finally {
      setLoading(false);
      setIsRequestInProgress(false);
      requestIdRef.current = null;
    }
  };

  const handlePinSetup = async (pinToSubmit: string) => {
    console.log('🔐 [PIN_SETUP] Starting PIN setup process');
    console.log('🔐 [PIN_SETUP] Original PIN:', pin);
    console.log('🔐 [PIN_SETUP] Confirm PIN:', pinToSubmit);
    console.log('🔐 [PIN_SETUP] PINs match:', pin === pinToSubmit);

    if (pin !== pinToSubmit) {
      console.log('❌ [PIN_SETUP] PINs do not match');
      setError('PINs do not match. Please try again.');

      // Strong haptic for final error
      ReactNativeHapticFeedback.trigger('notificationError', {
        enableVibrateFallback: true,
        ignoreAndroidSystemSettings: false,
      });

      shakeInputs();

      // Reset state after showing error
      setTimeout(() => {
        setStep('create');
        setPin('');
        setConfirmPin('');
        setMismatchedIndexes([]);
        setHasConfirmMismatch(false);
        setError(''); // Clear the error message
        setTimeout(() => {
          inputRefs.current[0]?.focus();
        }, 100);
      }, 2000); // Show error for 2 seconds

      return;
    }

    console.log('🔐 [PIN_SETUP] PINs match, validating format...');
    // Validate PIN format before proceeding
    const validation = validatePinFormat(pin);
    if (!validation.isValid) {
      console.log('❌ [PIN_SETUP] PIN validation failed:', validation.error);
      setError(validation.error!);
      shakeInputs();
      setStep('create');
      setPin('');
      setConfirmPin('');
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 100);
      return;
    }

    console.log('✅ [PIN_SETUP] PIN validation passed, proceeding with setup...');

    setLoading(true);
    setError('');

    try {
      console.log('🔐 [PIN_SETUP] Starting normal PIN setup flow');

      // Store PIN securely first
      const pinStored = await storePinSecurely(pin);
      if (!pinStored) {
        throw new Error('Failed to store PIN securely');
      }

      // Then call backend setup service
      await setupService.setupPin({
        pin: pin,
        confirmPin: pinToSubmit,
      });

      // Success haptic feedback
      ReactNativeHapticFeedback.trigger('notificationSuccess', {
        enableVibrateFallback: true,
        ignoreAndroidSystemSettings: false,
      });

      // Show biometric setup modal
      setTimeout(() => {
        setShowBiometricModal(true);
      }, 500);
    } catch (error: any) {
      logger.error('PIN setup error', error, 'setup');

      // Check if PIN is already set
      if (error.message === 'PIN_ALREADY_SET') {
        // Switch to verification mode
        setMode('verify');
        setStep('create');
        setPin('');
        setConfirmPin('');
        setError('');
        return;
      }

      // Check if it's an account lock error
      if (error.status === 423 || error.message?.includes('ACCOUNT_LOCKED') || error.message?.includes('temporarily locked')) {
        logger.security('ACCOUNT_LOCKED_PIN_SETUP', {
          error: error.message
        });

        // Extract minutes remaining from error message if available
        const minutesMatch = error.message?.match(/(\d+)\s+minute/);
        const minutes = minutesMatch ? parseInt(minutesMatch[1]) : null;

        // Extract lock until timestamp if available
        let lockUntil = '';
        try {
          const errorData = typeof error.response?.data === 'object' ? error.response.data : {};
          lockUntil = errorData.lockUntil || '';
        } catch (e) {
          // Ignore parsing errors
        }

        // Show suspension modal instead of error message
        setSuspensionData({
          reason: 'Too many failed PIN attempts. Your account has been temporarily locked for security.',
          minutesRemaining: minutes || undefined,
          lockUntil: lockUntil || undefined
        });
        setShowSuspensionModal(true);

        // Don't reset form for lock errors
        return;
      }

      // Check if it's an authentication error
      const authErrorHandled = await handleAuthError(error, navigation);
      if (authErrorHandled) {
        return;
      }

      setError(error.message || 'Failed to set PIN. Please try again.');
      shakeInputs();
      setStep('create');
      setPin('');
      setConfirmPin('');
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 100);
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    if (mode === 'setup' && step === 'create') {
      // For new users in setup mode, show exit confirmation
      Alert.alert(
        'Exit Setup?',
        'You need to create a PIN to secure your account. Are you sure you want to exit?',
        [
          {
            text: 'Continue Setup',
            style: 'cancel',
          },
          {
            text: 'Exit App',
            style: 'destructive',
            onPress: () => BackHandler.exitApp(),
          },
        ]
      );
    } else {
      // For verify mode, allow normal back navigation
      navigation.goBack();
    }
  };

  const handleBiometricModalComplete = (_success: boolean) => {
    setShowBiometricModal(false);

    // Navigate to the actual PIN verification screen after biometric setup
    setTimeout(() => {
      logger.info('PIN and biometric setup complete, navigating to PIN verification screen', {
        biometricEnabled: _success,
        userData: userData ? 'present' : 'missing'
      }, 'setup');

      // Navigate to the dedicated PinVerificationScreen
      navigation.replace('PinVerification', { user: userData });
    }, 500);
  };



  const renderPinInput = () => {
    const currentPin = mode === 'verify' ? pin : (step === 'create' ? pin : confirmPin);
    
    return (
      <Animated.View style={[styles.pinInputContainer, { transform: [{ translateX: shakeAnimation }] }]}>
        {[0, 1, 2, 3].map((index) => {
          const isFilled = !!currentPin[index];
          const isMismatched = (step === 'confirm' && mismatchedIndexes.includes(index)) || 
                              (error && step === 'confirm');
          
          return (
            <View key={index} style={styles.pinInputWrapper}>

              <View
                style={[
                  styles.pinContainer,
                  isFilled && !isMismatched && styles.pinContainerFilled,
                  isMismatched && styles.pinContainerError,
                ]}
              >
                <TextInput
                  ref={(ref) => {
                    if (ref) {
                      inputRefs.current[index] = ref;
                    }
                  }}
                  style={[
                    styles.pinInput,
                    isFilled && !isMismatched && styles.pinInputFilled,
                    isMismatched && styles.pinInputError,
                  ]}
                  value={currentPin[index] || ''}
                  onChangeText={(value) => handlePinChange(value, index)}
                  onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
                  keyboardType="numeric"
                  maxLength={1}
                  secureTextEntry
                  selectTextOnFocus
                  editable={!loading}
                />
              </View>
            </View>
          );
        })}
      </Animated.View>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: 'transparent',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 24,
      paddingTop: 60,
      paddingBottom: 32,
    },
    backButton: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
      justifyContent: 'center',
      alignItems: 'center',
      shadowColor: theme.colors.text,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 3,
    },
    backButtonText: {
      fontSize: 22,
      color: theme.colors.text,
      fontWeight: '300',
    },
    stepIndicator: {
      alignItems: 'center',
    },
    stepText: {
      fontSize: 13,
      fontWeight: '500',
      color: theme.colors.muted,
      textTransform: 'uppercase',
      letterSpacing: 1,
    },
    content: {
      flex: 1,
      paddingHorizontal: 24,
      paddingTop: 16,
    },
    titleContainer: {
      alignItems: 'center',
      marginBottom: 40,
      marginTop: 8,
      paddingHorizontal: 16,
    },
    profileContainer: {
      marginBottom: 32,
      alignItems: 'center',
      marginTop: 20,
    },
    profileImage: {
      width: 100,
      height: 100,
      borderRadius: 50,
      borderWidth: 4,
      borderColor: '#007AFF',
    },
    defaultProfileContainer: {
      width: 100,
      height: 100,
      borderRadius: 50,
      backgroundColor: '#007AFF',
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 4,
      borderColor: '#007AFF',
    },
    defaultProfileText: {
      fontSize: 40,
      fontWeight: 'bold',
      color: 'white',
    },
    title: {
      fontSize: 28,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
      textAlign: 'center',
      letterSpacing: -0.3,
      lineHeight: 34,
    },
    subtitle: {
      fontSize: 16,
      fontWeight: '400',
      color: theme.colors.muted,
      textAlign: 'center',
      lineHeight: 24,
      paddingHorizontal: 8,
      letterSpacing: 0.1,
    },
    pinSection: {
      alignItems: 'center',
      marginBottom: 40,
      paddingHorizontal: 16,
    },
    pinInputContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      gap: 18,
      marginBottom: 24,
      paddingVertical: 8,
    },
    pinInputWrapper: {
      position: 'relative',
    },

    pinContainer: {
      width: 60,
      height: 60,
      borderRadius: 16,
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.05)',
      borderWidth: 1,
      borderColor: isDark ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.08)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    pinContainerFilled: {
      backgroundColor: isDark ? 'rgba(99, 102, 241, 0.15)' : 'rgba(99, 102, 241, 0.1)',
      borderColor: isDark ? 'rgba(99, 102, 241, 0.3)' : 'rgba(99, 102, 241, 0.25)',
    },
    pinContainerError: {
      backgroundColor: 'rgba(239, 68, 68, 0.1)',
      borderColor: 'rgba(239, 68, 68, 0.3)',
    },
    pinInput: {
      width: '100%',
      height: '100%',
      backgroundColor: 'transparent',
      fontSize: 24,
      fontWeight: '600',
      textAlign: 'center',
      color: theme.colors.text,
      borderWidth: 0,
      letterSpacing: 1,
    },
    pinInputFilled: {
      color: isDark ? '#FFFFFF' : '#1F2937',
      fontWeight: '700',
    },
    pinInputError: {
      color: '#EF4444',
      fontWeight: '700',
    },
    errorText: {
      color: '#FF6B6B',
      fontSize: 15,
      fontWeight: '500',
      textAlign: 'center',
      marginTop: 16,
      paddingHorizontal: 24,
      lineHeight: 22,
      letterSpacing: 0.2,
    },
    instructionText: {
      fontSize: 13,
      fontWeight: '400',
      color: theme.colors.muted,
      textAlign: 'center',
      marginTop: 20,
      paddingHorizontal: 32,
      lineHeight: 18,
      letterSpacing: 0.1,
    },
    instructionTextWarning: {
      color: '#FF8A80',
      fontWeight: '500',
    },
    loadingOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000,
    },
    dimOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: isDark ? 'rgba(0, 0, 0, 0.4)' : 'rgba(0, 0, 0, 0.3)',
    },
    biometricOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 999,
    },
  });

  if (mode === 'loading') {
    return <SetupLoadingScreen message="Setting up your security" />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar 
        barStyle={isDark ? "light-content" : "dark-content"} 
        backgroundColor="transparent"
        translucent={true}
      />
      
      <ImageBackground
        source={isDark ? require("../../../assets/images/bg.jpeg") : require("../../../assets/images/bg-white.jpeg")}
        style={StyleSheet.absoluteFillObject}
        resizeMode="cover"
      />
      
      <View style={styles.header}>
        {/* Only show back button for verify mode */}
        {mode === 'verify' ? (
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
        ) : null}
        
        {mode !== 'verify' && (
          <View style={styles.stepIndicator}>
            <Text style={styles.stepText}>
              {step === 'create' ? 'Create PIN' : 'Confirm PIN'}
            </Text>
          </View>
        )}
      </View>

      <Animated.View style={[styles.content, { opacity: fadeAnimation }]}>
        <View style={styles.titleContainer}>
          {/* User Profile Picture - only show in verify mode */}
          {mode === 'verify' && (
            <View style={styles.profileContainer}>
              {userData?.avatar && !imageError ? (
                <Image
                  source={{ uri: userData.avatar }}
                  style={styles.profileImage}
                  onError={() => {
                    logger.info('Failed to load avatar image', null, 'ui');
                    setImageError(true);
                  }}
                />
              ) : (
                <View style={styles.defaultProfileContainer}>
                  <Text style={styles.defaultProfileText}>
                    {userData?.firstName?.charAt(0)?.toUpperCase() || 'U'}
                  </Text>
                </View>
              )}
            </View>
          )}

          <Text style={styles.title}>
            {mode === 'verify'
              ? `Welcome back, ${userData?.firstName || 'User'}`
              : (step === 'create' ? 'Secure Your Account' : 'Confirm Security PIN')
            }
          </Text>
          <Text style={styles.subtitle}>
            {mode === 'verify'
              ? 'Enter your 4-digit PIN to access your account'
              : (step === 'create'
                ? 'Create a 4-digit PIN to protect your financial data and transactions'
                : 'Re-enter your PIN to confirm and complete setup'
              )
            }
          </Text>
        </View>

        <View style={styles.pinSection}>
          {renderPinInput()}

          {error ? (
            <Text style={styles.errorText}>{error}</Text>
          ) : (
            <Text style={styles.instructionText}>
              {mode === 'verify'
                ? 'Your PIN protects access to your financial data'
                : (step === 'create'
                  ? 'Avoid using obvious patterns like 1234 or repeated digits'
                  : 'Ensure both PINs match exactly'
                )
              }
            </Text>
          )}
        </View>
      </Animated.View>

      {loading && (
        <View style={styles.loadingOverlay}>
          <BlurView
            style={StyleSheet.absoluteFillObject}
            blurType={isDark ? "dark" : "light"}
            blurAmount={15}
            reducedTransparencyFallbackColor={isDark ? "rgba(0, 0, 0, 0.8)" : "rgba(255, 255, 255, 0.8)"}
          />
          <View style={styles.dimOverlay} />
          <VendyLoader size={48} />
        </View>
      )}

      {/* Biometric Modal Blur Overlay */}
      {showBiometricModal && (
        <View style={styles.biometricOverlay}>
          <BlurView
            style={StyleSheet.absoluteFillObject}
            blurType={isDark ? "dark" : "light"}
            blurAmount={20}
            reducedTransparencyFallbackColor={isDark ? "rgba(0, 0, 0, 0.9)" : "rgba(255, 255, 255, 0.9)"}
          />
          <View style={[styles.dimOverlay, { backgroundColor: isDark ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.2)' }]} />
        </View>
      )}

      {/* Biometric Setup Modal */}
      <BiometricSetupModal
        visible={showBiometricModal}
        onComplete={handleBiometricModalComplete}
        userData={userData}
      />

      {/* Account Suspension Modal */}
      <AccountSuspensionModal
        visible={showSuspensionModal}
        onClose={() => setShowSuspensionModal(false)}
        reason={suspensionData.reason}
        minutesRemaining={suspensionData.minutesRemaining}
        lockUntil={suspensionData.lockUntil}
      />
    </SafeAreaView>
  );
};

export default PinSetupScreen;
