/**
 * React Native Stallion Configuration (RN Lab)
 *
 * Configuration for OTA (Over-The-Air) updates using React Native Stallion by RN Lab
 * Dashboard: https://dashboard.rnlab.io
 * Docs: https://stallion.rnlab.io/docs
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

module.exports = {
  // ============================================================================
  // STALLION CREDENTIALS
  // ============================================================================

  /**
   * App Token from Stallion Dashboard
   * Get this from: https://dashboard.rnlab.io
   * IMPORTANT: Replace with your actual app token
   */
  appToken: process.env.STALLION_APP_TOKEN || 'your-app-token-here',

  /**
   * Project ID from Stallion Dashboard
   * Get this from: https://dashboard.rnlab.io
   * IMPORTANT: Replace with your actual project ID
   */
  projectId: process.env.STALLION_PROJECT_ID || 'your-project-id-here',

  // ============================================================================
  // APP INFORMATION
  // ============================================================================

  /**
   * App version - Should match your app's version in package.json
   */
  appVersion: '1.0.0',

  /**
   * Build number - Incremented with each build
   */
  buildNumber: 1,

  // ============================================================================
  // UPDATE BEHAVIOR
  // ============================================================================

  /**
   * Automatically check for updates on app start
   */
  checkOnStart: true,

  /**
   * Check for updates when app comes to foreground
   */
  checkOnResume: true,

  /**
   * Automatically download and install updates
   */
  autoUpdate: true,

  // ============================================================================
  // DEVELOPMENT
  // ============================================================================

  /**
   * Enable debug logging
   */
  debug: __DEV__,
};
