import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  SafeAreaView,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  ImageBackground,
  Animated,
  ActivityIndicator,
  Vibration,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import type { NativeStackScreenProps } from '@react-navigation/native-stack';
import type { RootStackParamList } from '../../types/navigation';
import { useTheme } from '../../components/ThemeContext';
import ArrowRightIcon from '../../components/icons/ArrowRightIcon';
import GlassyBox from '../../components/GlassyBox';
import { autoSetupUserAvatar } from '../../utils/avatarSetup';
import logger from '../../services/productionLogger';

type Props = NativeStackScreenProps<RootStackParamList, 'NameSetup'>;

const NameSetupScreen = ({ navigation, route }: Props) => {
  const { theme, isDark } = useTheme();
  const [firstName, setFirstName] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // Animation refs
  const labelAnim = useRef(new Animated.Value(0)).current;
  const shakeAnim = useRef(new Animated.Value(0)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;
  const buttonScaleAnim = useRef(new Animated.Value(1)).current;

  const { userData } = route.params || { userData: {} };

  const isValidName = firstName.trim().length >= 2;
  const characterCount = firstName.length;
  const maxCharacters = 50;

  // Enhanced animations
  useEffect(() => {
    Animated.timing(labelAnim, {
      toValue: isFocused || firstName ? 1 : 0,
      duration: 200,
      useNativeDriver: false,
    }).start();
  }, [isFocused, firstName]);

  useEffect(() => {
    Animated.timing(progressAnim, {
      toValue: characterCount / maxCharacters,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [characterCount, maxCharacters]);

  useEffect(() => {
    Animated.timing(buttonScaleAnim, {
      toValue: isValidName ? 1 : 0.95,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [isValidName]);

  // Shake animation for errors
  const triggerShakeAnimation = () => {
    Animated.sequence([
      Animated.timing(shakeAnim, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnim, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnim, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(shakeAnim, { toValue: 0, duration: 50, useNativeDriver: true }),
    ]).start();
  };

  // Validate name in real-time
  const validateName = (name: string) => {
    if (name.length === 0) {
      setShowError(false);
      setErrorMessage('');
      return true;
    }

    if (name.length < 2) {
      setShowError(true);
      setErrorMessage('First name must be at least 2 characters');
      return false;
    }

    if (name.length > maxCharacters) {
      setShowError(true);
      setErrorMessage(`First name cannot exceed ${maxCharacters} characters`);
      return false;
    }

    if (!/^[a-zA-Z\s'-]+$/.test(name)) {
      setShowError(true);
      setErrorMessage('Please enter a valid name using only letters, spaces, hyphens, and apostrophes');
      return false;
    }

    setShowError(false);
    setErrorMessage('');
    return true;
  };

  const handleNameChange = (text: string) => {
    setFirstName(text);
    validateName(text);
  };

  const handleContinue = async () => {
    if (!validateName(firstName.trim())) {
      triggerShakeAnimation();
      if (Platform.OS === 'ios') {
        Vibration.vibrate([0, 100]);
      } else {
        Vibration.vibrate(100);
      }
      return;
    }

    setIsLoading(true);

    try {
      const { setupService } = await import('../../services/setupService');

      await setupService.setupProfile({
        firstName: firstName.trim(),
      });

      logger.info('Profile saved successfully', { category: 'auth', context: 'name-setup' });

      // Automatically generate and set avatar based on name and gender detection
      try {
        logger.info('Setting up avatar based on name', { firstName: firstName.trim() }, 'name-setup');

        const avatarResult = await autoSetupUserAvatar({
          firstName: firstName.trim(),
          email: userData?.email,
          skipIfExists: false // Always generate new avatar for name setup
        });

        if (avatarResult.success) {
          logger.info('Avatar automatically set', {
            avatarPath: avatarResult.avatarPath,
            detectedGender: avatarResult.detectedGender,
            confidence: avatarResult.confidence
          }, 'name-setup');
        } else {
          logger.warn('Avatar setup failed, user can set it later', {
            error: avatarResult.error
          }, 'name-setup');
        }
      } catch (avatarError) {
        // Don't block the flow if avatar setup fails
        logger.error('Avatar setup error, continuing with flow', avatarError, 'name-setup');
      }

      // Success haptic feedback
      if (Platform.OS === 'ios') {
        Vibration.vibrate([0, 50]);
      }

      const updatedUserData = {
        ...userData,
        firstName: firstName.trim(),
        displayName: firstName.trim(),
      };

      navigation.navigate('PinSetup', { userData: updatedUserData });
    } catch (error: any) {
      logger.error('Error saving profile', { category: 'auth', context: 'name-setup', error });

      setShowError(true);
      setErrorMessage(error.message || 'Failed to save profile. Please try again.');
      triggerShakeAnimation();

      // Error haptic feedback
      if (Platform.OS === 'ios') {
        Vibration.vibrate([0, 100, 50, 100]);
      } else {
        Vibration.vibrate([100, 50, 100]);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const labelStyle = {
    position: 'absolute' as 'absolute',
    left: 0,
    color: showError
      ? '#FF6B6B'
      : isFocused
        ? (isDark ? '#C084FC' : '#6B21A8')
        : (isDark ? '#E5E7EB' : '#4B5563'),
    fontWeight: "500" as const,
    fontSize: labelAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [16, 12],
    }),
    top: labelAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [12, -18],
    }),
  };

  const inputContainerTransform = {
    transform: [
      {
        translateX: shakeAnim,
      },
    ],
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      paddingTop: 60,
      paddingHorizontal: 20,
      paddingBottom: 20,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },

    content: {
      flex: 1,
      paddingHorizontal: 24,
      paddingTop: 40,
      justifyContent: 'flex-start',
    },
    welcomeContainer: {
      alignItems: 'flex-start',
      marginBottom: 40,
    },
    title: {
      fontSize: 28,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 8,
      letterSpacing: -0.5,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.muted,
      lineHeight: 22,
      fontWeight: '400',
    },
    inputSection: {
      marginTop: 32,
    },
    inputWrapper: {
      position: 'relative',
      paddingBottom: 4,
      borderBottomWidth: isFocused || showError ? 0 : 2,
      borderColor: showError
        ? '#FF6B6B'
        : isDark ? '#4B5563' : '#9CA3AF',
    },
    inputGradientLine: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      height: 2,
    },
    input: {
      height: 40,
      fontSize: 16,
      color: showError ? '#FF6B6B' : theme.colors.text,
      paddingVertical: 4,
    },
    inputHelperContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: 8,
      minHeight: 20,
    },
    errorText: {
      fontSize: 12,
      color: '#FF6B6B',
      fontWeight: '500',
      flex: 1,
    },
    characterCount: {
      fontSize: 12,
      color: characterCount > maxCharacters * 0.8
        ? (characterCount >= maxCharacters ? '#FF6B6B' : '#FF9500')
        : theme.colors.muted,
      fontWeight: '500',
      marginLeft: 8,
    },
    securityNote: {
      marginTop: 24,
      paddingHorizontal: 16,
      paddingVertical: 12,
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.03)',
      borderRadius: 8,
      borderWidth: 1,
      borderColor: isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.08)',
    },
    securityText: {
      fontSize: 12,
      color: theme.colors.muted,
      lineHeight: 16,
      textAlign: 'center',
      fontWeight: '400',
    },
    buttonContainer: {
      paddingHorizontal: 24,
      paddingBottom: 20,
      paddingTop: 20,
    },
    continueButton: {
      borderRadius: 16,
      opacity: isValidName ? 1 : 0.6,
      overflow: 'hidden',
      minHeight: 56,
    },
    glassyButtonContainer: {
      borderRadius: 16,
      overflow: 'hidden',
      minHeight: 56,
    },
    buttonContent: {
      paddingVertical: 16,
      paddingHorizontal: 24,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      gap: 8,
      minHeight: 56,
      backgroundColor: isValidName
        ? (isDark ? 'rgba(99, 102, 241, 0.08)' : 'rgba(99, 102, 241, 0.06)')
        : 'transparent',
      borderWidth: 1,
      borderColor: isValidName
        ? (isDark ? 'rgba(99, 102, 241, 0.3)' : 'rgba(99, 102, 241, 0.25)')
        : (isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.15)'),
    },
    continueText: {
      color: isValidName
        ? (isDark ? '#8B5CF6' : '#6366F1')
        : theme.colors.muted,
      fontSize: 16,
      fontWeight: '600',
      letterSpacing: 0.2,
    },
    loadingContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    loadingText: {
      color: isDark ? '#8B5CF6' : '#6366F1',
      fontSize: 16,
      fontWeight: '600',
      letterSpacing: 0.2,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor="transparent"
        translucent={true}
      />
      <ImageBackground
        source={
          isDark
            ? require('../../../assets/images/bg.jpeg')
            : require('../../../assets/images/bg-light.jpeg')
        }
        style={StyleSheet.absoluteFillObject}
        resizeMode="cover"
      />

      <View style={styles.header} />



      <KeyboardAvoidingView style={{ flex: 1 }} behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
        <View style={styles.content}>
          <View style={styles.welcomeContainer}>
            <Text style={styles.title}>Personal Information</Text>
            <Text style={styles.subtitle}>Please provide your legal first name as it appears on your identification</Text>
          </View>

          <View style={styles.inputSection}>
            <Animated.View style={[styles.inputWrapper, inputContainerTransform]}>
              <Animated.Text style={labelStyle}>First Name</Animated.Text>
              <TextInput
                style={styles.input}
                value={firstName}
                onChangeText={handleNameChange}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
                placeholder=""
                autoCapitalize="words"
                autoCorrect={false}
                maxLength={maxCharacters}
                returnKeyType="done"
                onSubmitEditing={handleContinue}
              />
              {(isFocused || showError) && (
                <LinearGradient
                  colors={showError
                    ? ['#FF6B6B', '#FF8E8E']
                    : isDark
                      ? ['#C084FC', '#FFFFFF']
                      : ['#6B21A8', '#000000']
                  }
                  style={styles.inputGradientLine}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                />
              )}
            </Animated.View>

            {/* Helper Text and Character Count */}
            <View style={styles.inputHelperContainer}>
              {showError ? (
                <Text style={styles.errorText}>{errorMessage}</Text>
              ) : (
                <View />
              )}
              <Text style={styles.characterCount}>
                {characterCount}/{maxCharacters}
              </Text>
            </View>

            {/* Security Note */}
            {!firstName && !isFocused && (
              <View style={styles.securityNote}>
                <Text style={styles.securityText}>
                  Your personal information is encrypted and securely stored in compliance with financial regulations
                </Text>
              </View>
            )}
          </View>
        </View>

        <View style={styles.buttonContainer}>
          <Animated.View style={{ transform: [{ scale: buttonScaleAnim }] }}>
            <GlassyBox
              intensity="medium"
              glow={false}
              style={styles.glassyButtonContainer}
            >
              <TouchableOpacity
                style={styles.continueButton}
                onPress={handleContinue}
                disabled={!isValidName || isLoading}
                activeOpacity={0.8}
              >
                <View style={styles.buttonContent}>
                  {isLoading ? (
                    <View style={styles.loadingContainer}>
                      <ActivityIndicator size="small" color={isDark ? '#8B5CF6' : '#6366F1'} />
                      <Text style={styles.loadingText}>Verifying...</Text>
                    </View>
                  ) : (
                    <>
                      <Text style={styles.continueText}>Continue</Text>
                      <ArrowRightIcon
                        size={16}
                        color={isValidName
                          ? (isDark ? '#8B5CF6' : '#6366F1')
                          : theme.colors.muted
                        }
                      />
                    </>
                  )}
                </View>
              </TouchableOpacity>
            </GlassyBox>
          </Animated.View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default NameSetupScreen;
