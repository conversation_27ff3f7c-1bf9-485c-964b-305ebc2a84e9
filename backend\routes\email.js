const express = require('express');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const emailService = require('../services/brevoEmailService');
const userService = require('../services/userService');
const authService = require('../services/authService');
const logger = require('../utils/logger');

const router = express.Router();

// Rate limiting for email OTP requests
const emailOTPLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 3, // Limit each email to 3 OTP requests per windowMs
  message: {
    status: 'error',
    message: 'Too many OTP requests. Please wait 5 minutes before requesting another code.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => req.body.email || req.ip
});

// Rate limiting for general email sending
const emailLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 email requests per windowMs
  message: {
    status: 'error',
    message: 'Too many email requests. Please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * @route   POST /api/v1/email/send-otp
 * @desc    Send OTP to email address
 * @access  Public
 */
router.post('/send-otp',
  emailOTPLimiter,
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email address'),
    body('purpose')
      .optional()
      .isIn(['verification', 'reset', 'login'])
      .withMessage('Invalid purpose. Must be verification, reset, or login')
  ],
  async (req, res) => {
    try {
      // PERFORMANCE OPTIMIZATION: Reduce console logging in production
      if (process.env.NODE_ENV !== 'production') {
        console.log('📧 [EMAIL-ROUTE] Send OTP request received');
        console.log('📧 Request body:', JSON.stringify(req.body, null, 2));
        console.log('📧 Client IP:', req.ip);
      }

      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.log('❌ [EMAIL-ROUTE] Validation errors:', errors.array());
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { email, purpose = 'verification' } = req.body;
      const clientIP = req.ip || req.connection.remoteAddress;

      if (process.env.NODE_ENV !== 'production') {
        console.log('📧 [EMAIL-ROUTE] Processing email OTP request...');
        console.log('📧 Email:', email);
        console.log('📧 Purpose:', purpose);
        console.log('📧 Client IP:', clientIP);
      }

      // PERFORMANCE OPTIMIZATION: Run user lookup and OTP generation in parallel where possible
      let user = await userService.findByEmail(email);

      if (!user && purpose === 'verification') {
        // For verification, create a new user record if it doesn't exist
        console.log('👤 [EMAIL-ROUTE] User not found, creating new user...');
        user = await userService.createUserWithEmail(email);
        console.log('✅ [EMAIL-ROUTE] New user created:', user.id);

      } else if (!user) {
        console.log('❌ [EMAIL-ROUTE] User not found for email:', email);
        return res.status(404).json({
          status: 'error',
          message: 'No account found with this email address'
        });
      }

      // PERFORMANCE OPTIMIZATION: Generate OTP and prepare for email sending in parallel
      console.log('🔢 [EMAIL-ROUTE] Generating OTP...');
      const [otp] = await Promise.all([
        userService.createEmailVerificationToken(user.id),
        // Pre-warm any other async operations if needed
      ]);
      console.log('✅ [EMAIL-ROUTE] OTP generated:', otp);

      // Send OTP via email
      console.log('📨 [EMAIL-ROUTE] Sending OTP via email...');
      console.log('📧 Target email:', email);
      console.log('🔢 OTP code:', otp);

      const emailResult = await emailService.sendOTP(email, otp, purpose);

      console.log('📨 [EMAIL-ROUTE] Email Result:', JSON.stringify(emailResult, null, 2));

      if (!emailResult.success) {
        console.log('❌ [EMAIL-ROUTE] Email sending failed:', emailResult.error);
        logger.error(`Failed to send OTP to ${email}: ${emailResult.error}`);
        return res.status(500).json({
          status: 'error',
          message: 'Failed to send verification code. Please try again.'
        });
      }

      logger.info(`OTP sent successfully to ${email} from ${clientIP}`);

      res.status(200).json({
        status: 'success',
        message: 'Verification code sent to your email address',
        data: {
          email: email,
          messageId: emailResult.messageId,
          expiresIn: '5 minutes'
        }
      });

    } catch (error) {
      console.log('💥 [EMAIL-ROUTE] Error in send-otp:', error);
      logger.error('Send email OTP error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/v1/email/verify-otp
 * @desc    Verify email OTP and complete email verification
 * @access  Public
 */
router.post('/verify-otp',
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email address'),
    body('otp')
      .isLength({ min: 6, max: 6 })
      .isNumeric()
      .withMessage('OTP must be a 6-digit number')
  ],
  async (req, res) => {
    try {
      console.log('🔐 [EMAIL-ROUTE] Verify OTP request received');
      console.log('🔐 Request body:', JSON.stringify(req.body, null, 2));

      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.log('❌ [EMAIL-ROUTE] Validation errors:', errors.array());
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { email, otp } = req.body;
      const clientIP = req.ip || req.connection.remoteAddress;

      console.log('🔐 [EMAIL-ROUTE] Processing email OTP verification...');
      console.log('📧 Email:', email);
      console.log('🔢 OTP:', otp);
      console.log('📍 Client IP:', clientIP);

      // Find user by email
      const user = await userService.findByEmail(email);
      if (!user) {
        console.log('❌ [EMAIL-ROUTE] User not found for email:', email);
        return res.status(404).json({
          status: 'error',
          message: 'No account found with this email address. Please sign up again.'
        });
      }

      console.log('👤 [EMAIL-ROUTE] User found:', user.id);

      // Verify OTP
      console.log('🔍 [EMAIL-ROUTE] Verifying OTP...');
      const isValidOTP = await userService.verifyEmailOTP(user.id, otp);

      if (!isValidOTP) {
        console.log('❌ [EMAIL-ROUTE] Invalid or expired OTP');

        // Increment failed attempts
        await userService.incrementLoginAttempts(user.id);

        logger.warn(`Invalid email OTP attempt for ${email} from ${clientIP}`);

        return res.status(400).json({
          status: 'error',
          message: 'Invalid or expired verification code'
        });
      }

      // CRITICAL: Double-check user still exists after OTP verification
      // This handles the case where user was deleted during the verification process
      const userStillExists = await userService.findById(user.id);
      if (!userStillExists) {
        console.log('❌ [EMAIL-ROUTE] User was deleted during verification process:', user.id);
        return res.status(404).json({
          status: 'error',
          message: 'Account no longer exists. Please sign up again.'
        });
      }

      console.log('✅ [EMAIL-ROUTE] OTP verified successfully');

      // PERFORMANCE OPTIMIZATION: Run all post-verification operations in parallel
      const [tokens] = await Promise.all([
        // Generate JWT tokens (most critical - do first)
        authService.generateTokens(user.id, {
          ipAddress: clientIP,
          userAgent: req.headers['user-agent']
        }),
        // Run other operations in parallel (less critical)
        userService.markEmailAsVerified(user.id),
        userService.addIpAddress(user.id, clientIP),
        userService.resetLoginAttempts(user.id)
      ]);

      logger.info(`Successful email OTP verification for ${email} from ${clientIP}`);

      res.status(200).json({
        status: 'success',
        message: 'Email verified successfully',
        data: {
          user: {
            id: user.id,
            email: user.email,
            isEmailVerified: true,
            balance: user.balance,
            createdAt: user.createdAt
          },
          tokens
        }
      });

    } catch (error) {
      console.log('💥 [EMAIL-ROUTE] Error in verify-otp:', error);
      logger.error('Verify email OTP error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   GET /api/v1/email/delivery-status/:messageId
 * @desc    Check email delivery status
 * @access  Private (Admin only)
 */
router.get('/delivery-status/:messageId',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const { messageId } = req.params;

      console.log('📊 [EMAIL-ROUTE] Checking delivery status for:', messageId);

      const status = await emailService.checkDeliveryStatus(messageId);

      res.status(200).json({
        status: 'success',
        data: status
      });

    } catch (error) {
      logger.error('Check email delivery status error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

/**
 * @route   POST /api/v1/email/test
 * @desc    Test email configuration (Admin only)
 * @access  Private (Admin only)
 */
router.post('/test',
  emailLimiter,
  authService.protect,
  authService.restrictTo('admin'),
  [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('Please provide a valid email address'),
    body('subject')
      .optional()
      .isLength({ min: 1, max: 200 })
      .withMessage('Subject must be between 1 and 200 characters')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { email, subject = 'Vendy Email Test' } = req.body;

      console.log('🧪 [EMAIL-ROUTE] Testing email configuration...');
      console.log('📧 Test email:', email);

      const htmlContent = `
        <h2>🧪 Vendy Email Test</h2>
        <p>This is a test email from your Vendy backend.</p>
        <p><strong>Configuration Status:</strong> ✅ Working</p>
        <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
        <p>If you received this email, your Brevo integration is working correctly!</p>
      `;

      const result = await emailService.sendEmail(email, subject, htmlContent);

      logger.info(`Email test sent to ${email} by admin ${req.user.id}`);

      res.status(200).json({
        status: 'success',
        message: 'Test email sent successfully',
        data: result
      });

    } catch (error) {
      logger.error('Email test error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Internal server error'
      });
    }
  }
);

module.exports = router;
