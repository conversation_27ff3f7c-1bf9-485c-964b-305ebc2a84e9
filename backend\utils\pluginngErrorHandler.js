/**
 * PluginNG Error Handler Utility
 * 
 * Comprehensive error handling and mapping for PluginNG API responses
 * with standardized error codes and user-friendly messages.
 * 
 * Features:
 * - PluginNG-specific error code mapping
 * - Standardized error categories
 * - User-friendly error messages
 * - Detailed error logging
 * - Retry logic recommendations
 * - Error severity classification
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const logger = require('./logger');

/**
 * Standard error categories for consistent handling across providers
 */
const ERROR_CATEGORIES = {
  AUTHENTICATION: 'authentication_error',
  VALIDATION: 'validation_error',
  INSUFFICIENT_BALANCE: 'insufficient_balance',
  NETWORK_ERROR: 'network_error',
  SERVER_ERROR: 'server_error',
  RATE_LIMIT: 'rate_limit_error',
  INVALID_PHONE: 'invalid_phone',
  TRANSACTION_FAILED: 'transaction_failed',
  TIMEOUT: 'timeout_error',
  UNKNOWN: 'unknown_error'
};

/**
 * Error severity levels
 */
const ERROR_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

/**
 * PluginNG-specific error codes and their mappings
 * Note: These will need to be updated based on actual PluginNG API documentation
 */
const PLUGINNG_ERROR_CODES = {
  // Authentication errors
  'INVALID_TOKEN': {
    category: ERROR_CATEGORIES.AUTHENTICATION,
    severity: ERROR_SEVERITY.HIGH,
    message: 'Invalid API token. Please check your credentials.',
    userMessage: 'Authentication failed. Please try again.',
    retryable: false
  },
  'TOKEN_EXPIRED': {
    category: ERROR_CATEGORIES.AUTHENTICATION,
    severity: ERROR_SEVERITY.HIGH,
    message: 'API token has expired.',
    userMessage: 'Session expired. Please try again.',
    retryable: false
  },
  'UNAUTHORIZED': {
    category: ERROR_CATEGORIES.AUTHENTICATION,
    severity: ERROR_SEVERITY.HIGH,
    message: 'Unauthorized access to PluginNG API.',
    userMessage: 'Access denied. Please contact support.',
    retryable: false
  },

  // Validation errors
  'INVALID_PHONE': {
    category: ERROR_CATEGORIES.INVALID_PHONE,
    severity: ERROR_SEVERITY.MEDIUM,
    message: 'Invalid phone number format.',
    userMessage: 'Please enter a valid phone number.',
    retryable: false
  },
  'INVALID_AMOUNT': {
    category: ERROR_CATEGORIES.VALIDATION,
    severity: ERROR_SEVERITY.MEDIUM,
    message: 'Invalid transaction amount.',
    userMessage: 'Please enter a valid amount.',
    retryable: false
  },
  'INVALID_NETWORK': {
    category: ERROR_CATEGORIES.VALIDATION,
    severity: ERROR_SEVERITY.MEDIUM,
    message: 'Unsupported network or subcategory.',
    userMessage: 'Network not supported. Please try a different network.',
    retryable: false
  },

  // Balance errors
  'INSUFFICIENT_BALANCE': {
    category: ERROR_CATEGORIES.INSUFFICIENT_BALANCE,
    severity: ERROR_SEVERITY.MEDIUM,
    message: 'Insufficient balance in PluginNG account.',
    userMessage: 'Transaction failed due to insufficient funds.',
    retryable: false
  },
  'LOW_BALANCE': {
    category: ERROR_CATEGORIES.INSUFFICIENT_BALANCE,
    severity: ERROR_SEVERITY.MEDIUM,
    message: 'Account balance is low.',
    userMessage: 'Transaction may fail due to low balance.',
    retryable: true
  },

  // Network/Server errors
  'NETWORK_ERROR': {
    category: ERROR_CATEGORIES.NETWORK_ERROR,
    severity: ERROR_SEVERITY.HIGH,
    message: 'Network connectivity issue with PluginNG.',
    userMessage: 'Network error. Please try again.',
    retryable: true
  },
  'SERVER_ERROR': {
    category: ERROR_CATEGORIES.SERVER_ERROR,
    severity: ERROR_SEVERITY.HIGH,
    message: 'PluginNG server error.',
    userMessage: 'Service temporarily unavailable. Please try again.',
    retryable: true
  },
  'TIMEOUT': {
    category: ERROR_CATEGORIES.TIMEOUT,
    severity: ERROR_SEVERITY.MEDIUM,
    message: 'Request timeout to PluginNG API.',
    userMessage: 'Request timed out. Please try again.',
    retryable: true
  },

  // Transaction errors
  'TRANSACTION_FAILED': {
    category: ERROR_CATEGORIES.TRANSACTION_FAILED,
    severity: ERROR_SEVERITY.MEDIUM,
    message: 'Transaction processing failed.',
    userMessage: 'Transaction failed. Please try again.',
    retryable: true
  },
  'DUPLICATE_REFERENCE': {
    category: ERROR_CATEGORIES.VALIDATION,
    severity: ERROR_SEVERITY.MEDIUM,
    message: 'Duplicate transaction reference.',
    userMessage: 'Duplicate transaction detected.',
    retryable: false
  },

  // Rate limiting
  'RATE_LIMIT_EXCEEDED': {
    category: ERROR_CATEGORIES.RATE_LIMIT,
    severity: ERROR_SEVERITY.MEDIUM,
    message: 'Rate limit exceeded for PluginNG API.',
    userMessage: 'Too many requests. Please wait and try again.',
    retryable: true
  }
};

/**
 * PluginNG Error Handler Class
 */
class PluginNGErrorHandler {
  /**
   * Handle and categorize PluginNG API errors
   *
   * @param {Error|Object} error - Error object or PluginNG response
   * @param {string} context - Context where error occurred
   * @returns {Object} Standardized error object
   */
  static handleError(error, context = 'unknown') {
    const requestId = error.requestId || 'unknown';
    
    logger.error('🚨 [PLUGINNG_ERROR_HANDLER] Processing error:', {
      requestId,
      context,
      error: error.message || error,
      stack: error.stack
    });

    // Handle different error types
    let standardizedError;

    if (error.response) {
      // HTTP response error
      standardizedError = this.handleHttpError(error, context);
    } else if (error.code) {
      // PluginNG API error with code
      standardizedError = this.handleApiError(error, context);
    } else if (error.message) {
      // Generic error with message
      standardizedError = this.handleGenericError(error, context);
    } else {
      // Unknown error format
      standardizedError = this.handleUnknownError(error, context);
    }

    // Add common fields
    standardizedError.requestId = requestId;
    standardizedError.context = context;
    standardizedError.timestamp = new Date().toISOString();
    standardizedError.provider = 'pluginng';

    logger.error('📋 [PLUGINNG_ERROR_HANDLER] Standardized error:', standardizedError);

    return standardizedError;
  }

  /**
   * Handle HTTP response errors
   */
  static handleHttpError(error, context) {
    const status = error.response?.status;
    const data = error.response?.data;
    const message = data?.message || error.message;

    let errorCode = 'UNKNOWN';
    
    // Map HTTP status codes to error categories
    switch (status) {
      case 400:
        errorCode = 'INVALID_REQUEST';
        break;
      case 401:
        errorCode = 'UNAUTHORIZED';
        break;
      case 403:
        errorCode = 'INVALID_TOKEN';
        break;
      case 429:
        errorCode = 'RATE_LIMIT_EXCEEDED';
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        errorCode = 'SERVER_ERROR';
        break;
      default:
        errorCode = 'NETWORK_ERROR';
    }

    const errorInfo = PLUGINNG_ERROR_CODES[errorCode] || PLUGINNG_ERROR_CODES['NETWORK_ERROR'];

    return {
      code: errorCode,
      category: errorInfo.category,
      severity: errorInfo.severity,
      message: message || errorInfo.message,
      userMessage: errorInfo.userMessage,
      retryable: errorInfo.retryable,
      httpStatus: status,
      originalError: {
        status,
        data,
        message: error.message
      }
    };
  }

  /**
   * Handle PluginNG API errors with specific codes
   */
  static handleApiError(error, context) {
    const errorCode = error.code || 'UNKNOWN';
    const errorInfo = PLUGINNG_ERROR_CODES[errorCode] || PLUGINNG_ERROR_CODES['TRANSACTION_FAILED'];

    return {
      code: errorCode,
      category: errorInfo.category,
      severity: errorInfo.severity,
      message: error.message || errorInfo.message,
      userMessage: errorInfo.userMessage,
      retryable: errorInfo.retryable,
      originalError: error
    };
  }

  /**
   * Handle generic errors
   */
  static handleGenericError(error, context) {
    const message = error.message.toLowerCase();
    let errorCode = 'UNKNOWN';

    // Try to categorize based on message content
    if (message.includes('timeout')) {
      errorCode = 'TIMEOUT';
    } else if (message.includes('network') || message.includes('connection')) {
      errorCode = 'NETWORK_ERROR';
    } else if (message.includes('phone') || message.includes('number')) {
      errorCode = 'INVALID_PHONE';
    } else if (message.includes('amount')) {
      errorCode = 'INVALID_AMOUNT';
    } else if (message.includes('balance')) {
      errorCode = 'INSUFFICIENT_BALANCE';
    } else if (message.includes('token') || message.includes('auth')) {
      errorCode = 'INVALID_TOKEN';
    }

    const errorInfo = PLUGINNG_ERROR_CODES[errorCode] || {
      category: ERROR_CATEGORIES.UNKNOWN,
      severity: ERROR_SEVERITY.MEDIUM,
      message: error.message,
      userMessage: 'An error occurred. Please try again.',
      retryable: true
    };

    return {
      code: errorCode,
      category: errorInfo.category,
      severity: errorInfo.severity,
      message: error.message,
      userMessage: errorInfo.userMessage,
      retryable: errorInfo.retryable,
      originalError: error
    };
  }

  /**
   * Handle unknown error formats
   */
  static handleUnknownError(error, context) {
    return {
      code: 'UNKNOWN',
      category: ERROR_CATEGORIES.UNKNOWN,
      severity: ERROR_SEVERITY.MEDIUM,
      message: 'Unknown error occurred',
      userMessage: 'An unexpected error occurred. Please try again.',
      retryable: true,
      originalError: error
    };
  }

  /**
   * Check if error is retryable
   */
  static isRetryable(error) {
    const standardizedError = this.handleError(error);
    return standardizedError.retryable;
  }

  /**
   * Get retry delay based on error type
   */
  static getRetryDelay(error, attempt = 1) {
    const standardizedError = this.handleError(error);
    
    if (!standardizedError.retryable) {
      return 0;
    }

    // Base delay with exponential backoff
    let baseDelay = 1000; // 1 second

    // Adjust delay based on error category
    switch (standardizedError.category) {
      case ERROR_CATEGORIES.RATE_LIMIT:
        baseDelay = 5000; // 5 seconds for rate limits
        break;
      case ERROR_CATEGORIES.SERVER_ERROR:
        baseDelay = 2000; // 2 seconds for server errors
        break;
      case ERROR_CATEGORIES.NETWORK_ERROR:
        baseDelay = 1500; // 1.5 seconds for network errors
        break;
    }

    // Exponential backoff with jitter
    const delay = baseDelay * Math.pow(2, attempt - 1);
    const jitter = Math.random() * 1000; // Add up to 1 second jitter
    
    return Math.min(delay + jitter, 30000); // Cap at 30 seconds
  }
}

module.exports = {
  PluginNGErrorHandler,
  ERROR_CATEGORIES,
  ERROR_SEVERITY,
  PLUGINNG_ERROR_CODES
};
