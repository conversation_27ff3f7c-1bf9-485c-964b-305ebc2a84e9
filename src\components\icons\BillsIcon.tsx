import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface BillsIconProps {
  size?: number;
  color?: string;
}

const BillsIcon: React.FC<BillsIconProps> = ({ 
  size = 24, 
  color = '#000000' 
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M9 11H15V13H9V11ZM9 7H15V9H9V7ZM5 21L12 18L19 21V3H5V21ZM7 5H17V17.82L12 15.97L7 17.82V5Z"
        fill={color}
      />
    </Svg>
  );
};

export default BillsIcon;