/**
 * VTpass Error Handler Utility
 * 
 * Comprehensive error handling system for VTpass API integration with detailed
 * error categorization, user-friendly messages, and automated recovery strategies.
 * 
 * Features:
 * - VTpass-specific error code mapping
 * - User-friendly error messages
 * - Automated retry strategies
 * - Error categorization and logging
 * - Recovery recommendations
 * - Error analytics and monitoring
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const logger = require('./logger');

/**
 * VTpass Error Categories
 * Categorizes errors for better handling and user experience
 */
const ERROR_CATEGORIES = {
  AUTHENTICATION: 'authentication',
  VALIDATION: 'validation',
  INSUFFICIENT_FUNDS: 'insufficient_funds',
  SERVICE_UNAVAILABLE: 'service_unavailable',
  NETWORK: 'network',
  RATE_LIMIT: 'rate_limit',
  PROVIDER: 'provider',
  SYSTEM: 'system',
  UNKNOWN: 'unknown'
};

/**
 * VTpass Error Codes and Mappings
 * Maps VTpass API response codes to user-friendly messages and categories
 */
const VTPASS_ERROR_CODES = {
  // Success codes
  '000': {
    category: 'success',
    message: 'Transaction successful',
    userMessage: 'Your transaction was completed successfully',
    retryable: false,
    severity: 'info'
  },

  // Authentication errors
  '001': {
    category: ERROR_CATEGORIES.AUTHENTICATION,
    message: 'Invalid API credentials',
    userMessage: 'Service temporarily unavailable. Please try again later.',
    retryable: false,
    severity: 'error',
    action: 'check_credentials'
  },

  '002': {
    category: ERROR_CATEGORIES.AUTHENTICATION,
    message: 'API key not found or inactive',
    userMessage: 'Service temporarily unavailable. Please contact support.',
    retryable: false,
    severity: 'error',
    action: 'check_api_key'
  },

  // Validation errors
  '003': {
    category: ERROR_CATEGORIES.VALIDATION,
    message: 'Invalid request format',
    userMessage: 'Invalid request. Please check your input and try again.',
    retryable: false,
    severity: 'warning'
  },

  '004': {
    category: ERROR_CATEGORIES.VALIDATION,
    message: 'Missing required parameters',
    userMessage: 'Missing required information. Please complete all fields.',
    retryable: false,
    severity: 'warning'
  },

  '005': {
    category: ERROR_CATEGORIES.VALIDATION,
    message: 'Invalid phone number format',
    userMessage: 'Please enter a valid phone number.',
    retryable: false,
    severity: 'warning'
  },

  '006': {
    category: ERROR_CATEGORIES.VALIDATION,
    message: 'Invalid amount',
    userMessage: 'Please enter a valid amount.',
    retryable: false,
    severity: 'warning'
  },

  // Service errors
  '007': {
    category: ERROR_CATEGORIES.SERVICE_UNAVAILABLE,
    message: 'Service temporarily unavailable',
    userMessage: 'Service is temporarily unavailable. Please try again in a few minutes.',
    retryable: true,
    severity: 'warning',
    retryDelay: 300000 // 5 minutes
  },

  '008': {
    category: ERROR_CATEGORIES.SERVICE_UNAVAILABLE,
    message: 'Network provider unavailable',
    userMessage: 'MTN service is temporarily unavailable. Please try again later.',
    retryable: true,
    severity: 'warning',
    retryDelay: 600000 // 10 minutes
  },

  // Balance/funding errors
  '009': {
    category: ERROR_CATEGORIES.INSUFFICIENT_FUNDS,
    message: 'Insufficient wallet balance',
    userMessage: 'Insufficient balance. Please fund your wallet and try again.',
    retryable: false,
    severity: 'warning',
    action: 'fund_wallet'
  },

  '010': {
    category: ERROR_CATEGORIES.INSUFFICIENT_FUNDS,
    message: 'Transaction amount exceeds limit',
    userMessage: 'Transaction amount exceeds your limit. Please try a smaller amount.',
    retryable: false,
    severity: 'warning'
  },

  // Rate limiting
  '011': {
    category: ERROR_CATEGORIES.RATE_LIMIT,
    message: 'Rate limit exceeded',
    userMessage: 'Too many requests. Please wait a moment and try again.',
    retryable: true,
    severity: 'warning',
    retryDelay: 60000 // 1 minute
  },

  // Provider-specific errors
  '012': {
    category: ERROR_CATEGORIES.PROVIDER,
    message: 'Transaction failed at provider',
    userMessage: 'Transaction failed. Your account has not been charged.',
    retryable: true,
    severity: 'error',
    retryDelay: 180000 // 3 minutes
  },

  '013': {
    category: ERROR_CATEGORIES.PROVIDER,
    message: 'Provider timeout',
    userMessage: 'Transaction is being processed. You will receive a confirmation shortly.',
    retryable: true,
    severity: 'warning',
    retryDelay: 300000 // 5 minutes
  },

  // System errors
  '014': {
    category: ERROR_CATEGORIES.SYSTEM,
    message: 'Database error',
    userMessage: 'System error occurred. Please try again later.',
    retryable: true,
    severity: 'error',
    retryDelay: 120000 // 2 minutes
  },

  '015': {
    category: ERROR_CATEGORIES.SYSTEM,
    message: 'Internal server error',
    userMessage: 'System temporarily unavailable. Please try again later.',
    retryable: true,
    severity: 'error',
    retryDelay: 300000 // 5 minutes
  }
};

/**
 * HTTP Status Code Mappings
 * Maps HTTP status codes to error categories and messages
 */
const HTTP_ERROR_MAPPINGS = {
  400: {
    category: ERROR_CATEGORIES.VALIDATION,
    message: 'Bad request',
    userMessage: 'Invalid request. Please check your input.',
    retryable: false
  },
  401: {
    category: ERROR_CATEGORIES.AUTHENTICATION,
    message: 'Unauthorized',
    userMessage: 'Authentication failed. Please try again.',
    retryable: false
  },
  403: {
    category: ERROR_CATEGORIES.AUTHENTICATION,
    message: 'Forbidden',
    userMessage: 'Access denied. Please contact support.',
    retryable: false
  },
  404: {
    category: ERROR_CATEGORIES.SERVICE_UNAVAILABLE,
    message: 'Service not found',
    userMessage: 'Service temporarily unavailable.',
    retryable: true,
    retryDelay: 300000
  },
  429: {
    category: ERROR_CATEGORIES.RATE_LIMIT,
    message: 'Too many requests',
    userMessage: 'Too many requests. Please wait and try again.',
    retryable: true,
    retryDelay: 60000
  },
  500: {
    category: ERROR_CATEGORIES.SYSTEM,
    message: 'Internal server error',
    userMessage: 'System error. Please try again later.',
    retryable: true,
    retryDelay: 300000
  },
  502: {
    category: ERROR_CATEGORIES.SERVICE_UNAVAILABLE,
    message: 'Bad gateway',
    userMessage: 'Service temporarily unavailable.',
    retryable: true,
    retryDelay: 180000
  },
  503: {
    category: ERROR_CATEGORIES.SERVICE_UNAVAILABLE,
    message: 'Service unavailable',
    userMessage: 'Service temporarily unavailable.',
    retryable: true,
    retryDelay: 300000
  },
  504: {
    category: ERROR_CATEGORIES.NETWORK,
    message: 'Gateway timeout',
    userMessage: 'Request timeout. Please try again.',
    retryable: true,
    retryDelay: 120000
  }
};

/**
 * VTpass Error Handler Class
 * Handles all VTpass-related errors with comprehensive logging and user feedback
 */
class VTpassErrorHandler {
  constructor() {
    this.errorStats = new Map();
    this.recentErrors = [];
    this.maxRecentErrors = 100;
  }

  /**
   * Handle VTpass API error
   * 
   * @param {Error} error - Error object
   * @param {Object} context - Additional context information
   * @returns {Object} Processed error information
   */
  handleError(error, context = {}) {
    const errorInfo = this.analyzeError(error, context);
    this.logError(errorInfo, context);
    this.updateErrorStats(errorInfo);
    this.addToRecentErrors(errorInfo, context);

    return {
      success: false,
      error: {
        code: errorInfo.code,
        category: errorInfo.category,
        message: errorInfo.userMessage,
        retryable: errorInfo.retryable,
        retryDelay: errorInfo.retryDelay,
        severity: errorInfo.severity,
        action: errorInfo.action,
        timestamp: new Date().toISOString(),
        requestId: context.requestId
      },
      debug: process.env.NODE_ENV === 'development' ? {
        originalError: error.message,
        stack: error.stack,
        context
      } : undefined
    };
  }

  /**
   * Analyze error and determine appropriate response
   * 
   * @param {Error} error - Error object
   * @param {Object} context - Error context
   * @returns {Object} Error analysis result
   */
  analyzeError(error, context) {
    // Check for VTpass API response error
    if (error.response?.data?.code) {
      const vtpassCode = error.response.data.code;
      const errorMapping = VTPASS_ERROR_CODES[vtpassCode];
      
      if (errorMapping) {
        return {
          code: vtpassCode,
          originalMessage: error.response.data.response_description || error.message,
          ...errorMapping
        };
      }
    }

    // Check for HTTP status code errors
    if (error.response?.status) {
      const httpStatus = error.response.status;
      const errorMapping = HTTP_ERROR_MAPPINGS[httpStatus];
      
      if (errorMapping) {
        return {
          code: `HTTP_${httpStatus}`,
          originalMessage: error.message,
          ...errorMapping
        };
      }
    }

    // Check for network errors
    if (error.code) {
      switch (error.code) {
        case 'ECONNABORTED':
          return {
            code: 'TIMEOUT',
            category: ERROR_CATEGORIES.NETWORK,
            message: 'Request timeout',
            userMessage: 'Request timed out. Please try again.',
            retryable: true,
            retryDelay: 30000,
            severity: 'warning'
          };
        case 'ENOTFOUND':
        case 'ECONNREFUSED':
          return {
            code: 'NETWORK_ERROR',
            category: ERROR_CATEGORIES.NETWORK,
            message: 'Network connection failed',
            userMessage: 'Network error. Please check your connection and try again.',
            retryable: true,
            retryDelay: 60000,
            severity: 'error'
          };
      }
    }

    // Default unknown error
    return {
      code: 'UNKNOWN_ERROR',
      category: ERROR_CATEGORIES.UNKNOWN,
      message: 'Unknown error occurred',
      userMessage: 'An unexpected error occurred. Please try again later.',
      originalMessage: error.message,
      retryable: true,
      retryDelay: 300000,
      severity: 'error'
    };
  }

  /**
   * Log error with appropriate level and context
   * 
   * @param {Object} errorInfo - Processed error information
   * @param {Object} context - Error context
   */
  logError(errorInfo, context) {
    const logData = {
      errorCode: errorInfo.code,
      category: errorInfo.category,
      message: errorInfo.message,
      userMessage: errorInfo.userMessage,
      retryable: errorInfo.retryable,
      severity: errorInfo.severity,
      context: {
        requestId: context.requestId,
        userId: context.userId,
        transactionId: context.transactionId,
        amount: context.amount,
        phone: context.phone?.replace(/(\d{4})(\d{3})(\d{4})/, '$1***$3'), // Mask phone
        ip: context.ip,
        userAgent: context.userAgent
      }
    };

    switch (errorInfo.severity) {
      case 'error':
        logger.error('❌ [VTPASS_ERROR] Critical error:', logData);
        break;
      case 'warning':
        logger.warn('⚠️ [VTPASS_ERROR] Warning:', logData);
        break;
      default:
        logger.info('ℹ️ [VTPASS_ERROR] Info:', logData);
    }
  }

  /**
   * Update error statistics for monitoring
   * 
   * @param {Object} errorInfo - Error information
   */
  updateErrorStats(errorInfo) {
    const key = `${errorInfo.category}:${errorInfo.code}`;
    const stats = this.errorStats.get(key) || {
      count: 0,
      firstOccurrence: Date.now(),
      lastOccurrence: Date.now()
    };

    stats.count++;
    stats.lastOccurrence = Date.now();
    this.errorStats.set(key, stats);
  }

  /**
   * Add error to recent errors list for analysis
   * 
   * @param {Object} errorInfo - Error information
   * @param {Object} context - Error context
   */
  addToRecentErrors(errorInfo, context) {
    this.recentErrors.unshift({
      ...errorInfo,
      context,
      timestamp: Date.now()
    });

    // Keep only recent errors
    if (this.recentErrors.length > this.maxRecentErrors) {
      this.recentErrors = this.recentErrors.slice(0, this.maxRecentErrors);
    }
  }

  /**
   * Get error statistics
   * 
   * @returns {Object} Error statistics
   */
  getErrorStats() {
    const stats = {};
    for (const [key, value] of this.errorStats.entries()) {
      stats[key] = {
        ...value,
        rate: value.count / ((Date.now() - value.firstOccurrence) / 3600000) // errors per hour
      };
    }
    return stats;
  }

  /**
   * Check if error should trigger retry
   * 
   * @param {Object} errorInfo - Error information
   * @param {number} attemptCount - Current attempt count
   * @returns {boolean} Whether to retry
   */
  shouldRetry(errorInfo, attemptCount = 1) {
    if (!errorInfo.retryable) return false;
    if (attemptCount >= 3) return false; // Max 3 retries
    
    // Don't retry authentication errors
    if (errorInfo.category === ERROR_CATEGORIES.AUTHENTICATION) return false;
    
    // Don't retry validation errors
    if (errorInfo.category === ERROR_CATEGORIES.VALIDATION) return false;
    
    return true;
  }

  /**
   * Get retry delay for error
   * 
   * @param {Object} errorInfo - Error information
   * @param {number} attemptCount - Current attempt count
   * @returns {number} Delay in milliseconds
   */
  getRetryDelay(errorInfo, attemptCount = 1) {
    const baseDelay = errorInfo.retryDelay || 30000; // Default 30 seconds
    return Math.min(baseDelay * Math.pow(2, attemptCount - 1), 300000); // Max 5 minutes
  }
}

// Create singleton instance
const vtpassErrorHandler = new VTpassErrorHandler();

module.exports = {
  VTpassErrorHandler,
  vtpassErrorHandler,
  ERROR_CATEGORIES,
  VTPASS_ERROR_CODES,
  HTTP_ERROR_MAPPINGS
};
