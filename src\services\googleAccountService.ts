import AsyncStorage from '@react-native-async-storage/async-storage';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import productionLogger from './productionLogger';

interface GoogleAccount {
  id: string;
  name: string;
  email: string;
  photo?: string;
  isCurrent?: boolean;
}

class GoogleAccountService {
  private readonly ACCOUNTS_STORAGE_KEY = 'google_accounts';

  /**
   * Cache a Google account for future use
   */
  async cacheAccount(account: GoogleAccount): Promise<void> {
    try {
      // Mark this account as current
      account.isCurrent = true;
      
      // Get existing accounts
      const accounts = await this.getCachedAccounts();
      
      // Update current status for all accounts
      const updatedAccounts = accounts.map(existingAccount => ({
        ...existingAccount,
        isCurrent: existingAccount.id === account.id
      }));
      
      // Add the new account if it doesn't exist
      const accountExists = updatedAccounts.some(existingAccount => existingAccount.id === account.id);
      if (!accountExists) {
        updatedAccounts.push({ ...account, isCurrent: true });
      }
      
      // Save updated accounts
      await AsyncStorage.setItem(this.ACCOUNTS_STORAGE_KEY, JSON.stringify(updatedAccounts));
      productionLogger.info('Google account cached successfully', {
        service: 'GoogleAccountService',
        method: 'cacheAccount',
        email: account.email,
        accountId: account.id
      });
    } catch (error) {
      productionLogger.error('Error caching Google account', {
        service: 'GoogleAccountService',
        method: 'cacheAccount',
        error: error.message || error
      });
    }
  }

  /**
   * Get all cached Google accounts
   */
  async getCachedAccounts(): Promise<GoogleAccount[]> {
    try {
      const accountsJson = await AsyncStorage.getItem(this.ACCOUNTS_STORAGE_KEY);
      if (accountsJson) {
        return JSON.parse(accountsJson);
      }
      return [];
    } catch (error) {
      productionLogger.error('Error getting cached Google accounts', {
        service: 'GoogleAccountService',
        method: 'getCachedAccounts',
        error: error.message || error
      });
      return [];
    }
  }

  /**
   * Clear all cached accounts
   */
  async clearCache(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.ACCOUNTS_STORAGE_KEY);
      productionLogger.info('Google accounts cache cleared', {
        service: 'GoogleAccountService',
        method: 'clearCache'
      });
    } catch (error) {
      productionLogger.error('Error clearing Google accounts cache', {
        service: 'GoogleAccountService',
        method: 'clearCache',
        error: error.message || error
      });
    }
  }

  /**
   * Get available Google accounts for sign-in
   * This combines cached accounts with the current Google account
   */
  async getAvailableAccounts(): Promise<GoogleAccount[]> {
    try {
      // Get cached accounts
      const cachedAccounts = await this.getCachedAccounts();
      
      // Try to get current Google account
      try {
        const currentUser = await GoogleSignin.getCurrentUser();
        if (currentUser && currentUser.user) {
          // Check if this account is already in the cache
          const accountExists = cachedAccounts.some(account => account.id === currentUser.user.id);
          
          if (!accountExists) {
            // Add current account to the list
            cachedAccounts.push({
              id: currentUser.user.id,
              name: currentUser.user.name || '',
              email: currentUser.user.email,
              photo: currentUser.user.photo || undefined,
              isCurrent: true
            });
          }
        }
      } catch (error) {
        productionLogger.debug('No current Google user found', {
          service: 'GoogleAccountService',
          method: 'getAvailableAccounts',
          context: 'getCurrentUser'
        });
      }
      
      // Get accounts directly from Google Play Services
      try {
        // This will trigger the Google account picker directly
        // We'll use this in the modal when "Add account" is clicked
        productionLogger.debug('Attempting to get Google Play Services accounts', {
          service: 'GoogleAccountService',
          method: 'getAvailableAccounts',
          context: 'googlePlayServicesAccounts'
        });
        
        // We don't actually call this here, as it would trigger the native picker
        // Instead, we'll rely on cached accounts and the "Add account" option
      } catch (error) {
        productionLogger.debug('Could not get Google Play Services accounts', {
          service: 'GoogleAccountService',
          method: 'getAvailableAccounts',
          context: 'googlePlayServicesAccounts',
          error: error.message || error
        });
      }
      
      return cachedAccounts;
    } catch (error) {
      productionLogger.error('Error getting available Google accounts', {
        service: 'GoogleAccountService',
        method: 'getAvailableAccounts',
        error: error.message || error
      });
      return [];
    }
  }

  /**
   * Format account for display in the UI
   */
  formatAccountForDisplay(account: GoogleAccount): GoogleAccount {
    return {
      ...account,
      name: account.name || account.email.split('@')[0]
    };
  }

  /**
   * Try to sign in silently with the current Google account
   */
  async signInSilently(): Promise<any> {
    try {
      return await GoogleSignin.signInSilently();
    } catch (error) {
      productionLogger.debug('Silent sign-in failed', {
        service: 'GoogleAccountService',
        method: 'signInSilently',
        error: error.message || error
      });
      return null;
    }
  }

  /**
   * Sign in with a specific account by accountId
   * Attempts silent sign-in first, falls back to regular sign-in with account hint
   */
  async signInWithAccount(accountId: string): Promise<any> {
    try {
      productionLogger.info('Attempting to sign in with account', {
        service: 'GoogleAccountService',
        method: 'signInWithAccount',
        accountId
      });
      
      // Get the account details from cache
      const cachedAccounts = await this.getCachedAccounts();
      const targetAccount = cachedAccounts.find(account => account.id === accountId);
      
      if (!targetAccount) {
        throw new Error(`Account with ID ${accountId} not found in cache`);
      }
      
      productionLogger.debug('Target account identified', {
        service: 'GoogleAccountService',
        method: 'signInWithAccount',
        accountId,
        email: targetAccount.email
      });
      
      // First, try silent sign-in
      try {
        productionLogger.debug('Attempting silent sign-in', {
          service: 'GoogleAccountService',
          method: 'signInWithAccount',
          context: 'silentSignIn',
          accountId
        });
        const silentResult = await GoogleSignin.signInSilently();
        
        // Check if the silent sign-in returned the correct account
        if (silentResult && silentResult.user && silentResult.user.id === accountId) {
          productionLogger.info('Silent sign-in successful with correct account', {
            service: 'GoogleAccountService',
            method: 'signInWithAccount',
            context: 'silentSignIn',
            accountId,
            email: silentResult.user.email
          });
          
          // Update the cached account list to mark this account as current
          await this.updateCurrentAccount(accountId);
          
          return {
            success: true,
            user: silentResult.user,
            method: 'silent',
            googleUser: {
              id: silentResult.user.id,
              name: silentResult.user.name || '',
              email: silentResult.user.email,
              photo: silentResult.user.photo || undefined,
              isCurrent: true,
            }
          };
        } else {
          productionLogger.debug('Silent sign-in returned different account, proceeding with regular sign-in', {
            service: 'GoogleAccountService',
            method: 'signInWithAccount',
            context: 'silentSignIn',
            requestedAccountId: accountId,
            returnedAccountId: silentResult?.user?.id
          });
        }
      } catch (silentError) {
        productionLogger.debug('Silent sign-in failed, proceeding with regular sign-in', {
          service: 'GoogleAccountService',
          method: 'signInWithAccount',
          context: 'silentSignIn',
          accountId,
          error: silentError.message || silentError
        });
      }
      
      // Fall back to regular sign-in with account hint
      productionLogger.debug('Attempting regular sign-in with account hint', {
        service: 'GoogleAccountService',
        method: 'signInWithAccount',
        context: 'regularSignIn',
        accountId
      });
      try {
        // Sign in with account hint (email)
        const signInResult = await GoogleSignin.signIn();
        
        // Verify we got the correct account
        if (signInResult && signInResult.user) {
          if (signInResult.user.id === accountId) {
            productionLogger.info('Regular sign-in successful with correct account', {
              service: 'GoogleAccountService',
              method: 'signInWithAccount',
              context: 'regularSignIn',
              accountId,
              email: signInResult.user.email
            });
            
            // Update the cached account list to mark this account as current
            await this.updateCurrentAccount(accountId);
            
            return {
              success: true,
              user: signInResult.user,
              method: 'regular',
              googleUser: {
                id: signInResult.user.id,
                name: signInResult.user.name || '',
                email: signInResult.user.email,
                photo: signInResult.user.photo || undefined,
                isCurrent: true,
              }
            };
          } else {
            productionLogger.warn('Sign-in returned different account than requested', {
              service: 'GoogleAccountService',
              method: 'signInWithAccount',
              context: 'regularSignIn',
              requestedAccountId: accountId,
              returnedAccountId: signInResult.user.id,
              returnedEmail: signInResult.user.email
            });
            // Still cache this account but note the discrepancy
            await this.cacheAccount({
              id: signInResult.user.id,
              name: signInResult.user.name || '',
              email: signInResult.user.email,
              photo: signInResult.user.photo || undefined,
              isCurrent: true,
            });
            
            return {
              success: true,
              user: signInResult.user,
              method: 'regular',
              warning: 'Different account than requested was signed in',
              googleUser: {
                id: signInResult.user.id,
                name: signInResult.user.name || '',
                email: signInResult.user.email,
                photo: signInResult.user.photo || undefined,
                isCurrent: true,
              }
            };
          }
        } else {
          throw new Error('Sign-in succeeded but no user data received');
        }
      } catch (regularSignInError) {
        productionLogger.error('Regular sign-in also failed', {
          service: 'GoogleAccountService',
          method: 'signInWithAccount',
          context: 'regularSignIn',
          accountId,
          error: regularSignInError.message || regularSignInError
        });
        throw regularSignInError;
      }
    } catch (error) {
      productionLogger.error('Failed to sign in with account', {
        service: 'GoogleAccountService',
        method: 'signInWithAccount',
        accountId,
        error: error.message || error
      });
      throw error;
    }
  }

  /**
   * Update the current account flag in cached accounts
   */
  private async updateCurrentAccount(accountId: string): Promise<void> {
    try {
      const accounts = await this.getCachedAccounts();
      
      // Update current status for all accounts
      const updatedAccounts = accounts.map(account => ({
        ...account,
        isCurrent: account.id === accountId
      }));
      
      // Save updated accounts
      await AsyncStorage.setItem(this.ACCOUNTS_STORAGE_KEY, JSON.stringify(updatedAccounts));
      productionLogger.info('Updated current account flag', {
        service: 'GoogleAccountService',
        method: 'updateCurrentAccount',
        accountId
      });
    } catch (error) {
      productionLogger.error('Error updating current account', {
        service: 'GoogleAccountService',
        method: 'updateCurrentAccount',
        accountId,
        error: error.message || error
      });
    }
  }
}

export default new GoogleAccountService();