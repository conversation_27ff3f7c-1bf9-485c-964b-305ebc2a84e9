import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
} from 'react-native';
import Modal from 'react-native-modal';
import { useTheme } from './ThemeContext';
import { userService } from '../services/userService';
import LocalAvatarImage from './LocalAvatarImage';
import logger from '../services/productionLogger';

interface AvatarSelectionModalProps {
  visible: boolean;
  onClose: () => void;
  onAvatarSelected: (avatarUrl: string) => void;
  userData?: {
    firstName?: string;
    lastName?: string;
    email?: string;
    name?: string;
  };
}

const AvatarSelectionModal: React.FC<AvatarSelectionModalProps> = ({
  visible,
  onClose,
  onAvatarSelected,
  userData
}) => {
  const { theme, isDark } = useTheme();
  const [avatarOptions, setAvatarOptions] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedAvatar, setSelectedAvatar] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (visible && userData) {
      loadAvatarOptions();
    }
  }, [visible, userData]);

  const loadAvatarOptions = async () => {
    try {
      setLoading(true);
      logger.info('Loading avatar options', null, 'avatar_modal');

      const options = await userService.getAvatarOptions(userData || {});
      setAvatarOptions(options);

      logger.info('Avatar options loaded', { count: options.length }, 'avatar_modal');
    } catch (error) {
      logger.error('Failed to load avatar options', error, 'avatar_modal');
      Alert.alert('Error', 'Failed to load avatar options. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarSelect = (avatarUrl: string) => {
    setSelectedAvatar(avatarUrl);
  };

  const handleSaveAvatar = async () => {
    if (!selectedAvatar) return;

    try {
      setSaving(true);
      logger.info('Saving selected avatar', { avatarUrl: selectedAvatar }, 'avatar_modal');

      await userService.setUserAvatar(selectedAvatar);
      onAvatarSelected(selectedAvatar);
      onClose();

      logger.info('Avatar saved successfully', null, 'avatar_modal');
    } catch (error) {
      logger.error('Failed to save avatar', error, 'avatar_modal');
      Alert.alert('Error', 'Failed to save avatar. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const styles = StyleSheet.create({
    modalContent: {
      backgroundColor: theme.colors.background,
      borderRadius: 20,
      padding: 20,
      maxHeight: '80%',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 20,
    },
    title: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.text,
    },
    closeButton: {
      padding: 8,
    },
    closeButtonText: {
      fontSize: 16,
      color: theme.colors.muted,
    },
    loadingContainer: {
      height: 200,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      marginTop: 10,
      color: theme.colors.muted,
      fontSize: 14,
    },
    avatarGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
      marginBottom: 20,
    },
    avatarOption: {
      width: '31%',
      aspectRatio: 1,
      marginBottom: 12,
      borderRadius: 12,
      overflow: 'hidden',
      borderWidth: 2,
      borderColor: 'transparent',
      marginHorizontal: '1%',
    },
    avatarOptionSelected: {
      borderColor: theme.colors.primary,
      borderWidth: 3,
    },
    avatarImage: {
      width: '100%',
      height: '100%',
      backgroundColor: theme.colors.surface,
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 20,
    },
    button: {
      flex: 1,
      paddingVertical: 12,
      borderRadius: 8,
      alignItems: 'center',
      marginHorizontal: 5,
    },
    cancelButton: {
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    saveButton: {
      backgroundColor: theme.colors.primary,
    },
    buttonText: {
      fontSize: 16,
      fontWeight: '600',
    },
    cancelButtonText: {
      color: theme.colors.text,
    },
    saveButtonText: {
      color: '#FFFFFF',
    },
    disabledButton: {
      opacity: 0.5,
    },
  });

  return (
    <Modal
      isVisible={visible}
      onBackdropPress={onClose}
      style={{ margin: 20 }}
      backdropOpacity={0.5}
      animationIn="slideInUp"
      animationOut="slideOutDown"
    >
      <View style={styles.modalContent}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Choose Your Avatar</Text>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>Creating personalized avatars...</Text>
          </View>
        ) : (
          <ScrollView showsVerticalScrollIndicator={false}>
            <View style={styles.avatarGrid}>
              {avatarOptions.map((avatarUrl, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.avatarOption,
                    selectedAvatar === avatarUrl && styles.avatarOptionSelected,
                  ]}
                  onPress={() => handleAvatarSelect(avatarUrl)}
                  activeOpacity={0.8}
                >
                  <LocalAvatarImage
                    avatarPath={avatarUrl}
                    style={styles.avatarImage}
                    resizeMode="cover"
                  />
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        )}

        {/* Buttons */}
        {!loading && (
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={onClose}
              disabled={saving}
            >
              <Text style={[styles.buttonText, styles.cancelButtonText]}>
                Cancel
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.button,
                styles.saveButton,
                (!selectedAvatar || saving) && styles.disabledButton,
              ]}
              onPress={handleSaveAvatar}
              disabled={!selectedAvatar || saving}
            >
              {saving ? (
                <ActivityIndicator size="small" color="#FFFFFF" />
              ) : (
                <Text style={[styles.buttonText, styles.saveButtonText]}>
                  Save Avatar
                </Text>
              )}
            </TouchableOpacity>
          </View>
        )}
      </View>
    </Modal>
  );
};

export default AvatarSelectionModal;
