import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface NoPasswordIconProps {
  size?: number;
  color?: string;
}

const NoPasswordIcon: React.FC<NoPasswordIconProps> = ({ 
  size = 24, 
  color = '#007AFF' 
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M12 17C10.89 17 10 16.1 10 15C10 13.89 10.89 13 12 13C13.11 13 14 13.89 14 15C14 16.1 13.11 17 12 17ZM18 8H17V6C17 3.24 14.76 1 12 1S7 3.24 7 6V8H6C4.9 8 4 8.9 4 10V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V10C20 8.9 19.1 8 18 8ZM8.9 6C8.9 4.29 10.29 2.9 12 2.9S15.1 4.29 15.1 6V8H8.9V6Z"
        fill={color}
      />
      <Path
        d="M2.81 2.81L1.39 4.22L6.17 9H6C4.9 9 4 9.9 4 11V21C4 22.1 4.9 23 6 23H18C18.35 23 18.67 22.92 18.95 22.78L20.78 24.61L22.19 23.2L2.81 2.81Z"
        fill={color}
        opacity="0.3"
      />
    </Svg>
  );
};

export default NoPasswordIcon;