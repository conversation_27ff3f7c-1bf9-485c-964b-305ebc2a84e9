/**
 * Security Blacklist Middleware
 * 
 * Blocks common attack patterns and malicious requests
 * to protect against reconnaissance and exploitation attempts.
 */

const logger = require('../utils/logger');

// Common attack patterns to block
const BLOCKED_PATTERNS = [
  // Git repository access attempts
  /^\/\.git/,
  /\.git\/config$/,
  /\.git\/HEAD$/,
  /\.git\/index$/,
  
  // Environment and config files
  /\.env$/,
  /\.env\.local$/,
  /\.env\.production$/,
  /config\.php$/,
  /wp-config\.php$/,
  
  // Admin panels and common CMS paths
  /^\/admin$/,
  /^\/administrator$/,
  /^\/wp-admin/,
  /^\/phpmyadmin/,
  /^\/phpMyAdmin/,
  
  // Common vulnerability scanners
  /^\/\.well-known\/security\.txt$/,
  /^\/robots\.txt$/,
  /^\/sitemap\.xml$/,
  
  // Database files
  /\.sql$/,
  /\.db$/,
  /\.sqlite$/,
  
  // Backup files
  /\.bak$/,
  /\.backup$/,
  /\.old$/,
  /~$/,
  
  // Source code files that shouldn't be accessible
  /\.php\.bak$/,
  /\.php~$/,
  /\.js\.map$/,
  
  // Common exploit attempts
  /\.\.\//,  // Directory traversal
  /%2e%2e%2f/i,  // URL encoded directory traversal
  /\/etc\/passwd/,
  /\/proc\/version/,
  
  // Shell and script files
  /\.sh$/,
  /\.bat$/,
  /\.cmd$/,
];

// Suspicious user agents (common in automated attacks)
// Note: We exclude curl and python-requests for legitimate API testing
const SUSPICIOUS_USER_AGENTS = [
  /scanner/i,
  /nikto/i,
  /sqlmap/i,
  /nmap/i,
  /masscan/i,
  /zap/i,
  /burp/i,
  /acunetix/i,
  /nessus/i,
  /openvas/i,
  // Only block obvious malicious bots, not legitimate ones
  /malicious/i,
  /exploit/i,
  /hack/i,
];

// IPs to monitor (you can add known malicious IPs here)
const MONITORED_IPS = new Set();

// Legitimate user agents that should never be blocked
const LEGITIMATE_USER_AGENTS = [
  /vendy-android/i,
  /vendy-ios/i,
  /okhttp/i,  // Android HTTP client
  /cfnetwork/i,  // iOS HTTP client
  /postman/i,  // API testing
  /insomnia/i,  // API testing
];

// Rate limiting for suspicious requests
const suspiciousRequestCounts = new Map();
const SUSPICIOUS_REQUEST_LIMIT = 5;
const SUSPICIOUS_REQUEST_WINDOW = 5 * 60 * 1000; // 5 minutes

/**
 * Security blacklist middleware
 */
const securityBlacklist = (req, res, next) => {
  const url = req.originalUrl || req.url;
  const userAgent = req.get('User-Agent') || '';
  const ip = req.ip || req.connection.remoteAddress;
  
  // Check for blocked URL patterns
  const isBlockedUrl = BLOCKED_PATTERNS.some(pattern => pattern.test(url));
  
  // Check for suspicious user agents (but allow legitimate ones)
  const isLegitimateUserAgent = LEGITIMATE_USER_AGENTS.some(pattern => pattern.test(userAgent));
  const isSuspiciousUserAgent = !isLegitimateUserAgent && SUSPICIOUS_USER_AGENTS.some(pattern => pattern.test(userAgent));
  
  if (isBlockedUrl || isSuspiciousUserAgent) {
    // Log the security event
    logger.warn('🚨 [SECURITY] Blocked suspicious request', {
      ip,
      url,
      userAgent,
      reason: isBlockedUrl ? 'blocked_url_pattern' : 'suspicious_user_agent',
      timestamp: new Date().toISOString()
    });
    
    // Track suspicious requests from this IP
    const requestKey = `${ip}:suspicious`;
    const now = Date.now();
    
    if (!suspiciousRequestCounts.has(requestKey)) {
      suspiciousRequestCounts.set(requestKey, { count: 1, firstRequest: now });
    } else {
      const data = suspiciousRequestCounts.get(requestKey);
      data.count++;
      
      // If too many suspicious requests in the time window, log as potential attack
      if (data.count >= SUSPICIOUS_REQUEST_LIMIT && 
          (now - data.firstRequest) <= SUSPICIOUS_REQUEST_WINDOW) {
        logger.error('🚨 [SECURITY] Potential attack detected', {
          ip,
          suspiciousRequestCount: data.count,
          timeWindow: Math.round((now - data.firstRequest) / 1000) + 's',
          latestUrl: url,
          userAgent
        });
        
        // Add to monitored IPs
        MONITORED_IPS.add(ip);
      }
    }
    
    // Return 404 to not reveal that we're actively blocking
    return res.status(404).json({
      error: 'Not Found'
    });
  }
  
  // Log monitored IPs for additional scrutiny
  if (MONITORED_IPS.has(ip)) {
    logger.info('👁️ [SECURITY] Request from monitored IP', {
      ip,
      url,
      userAgent,
      method: req.method
    });
  }
  
  next();
};

// Clean up old request counts periodically
setInterval(() => {
  const now = Date.now();
  for (const [key, data] of suspiciousRequestCounts.entries()) {
    if (now - data.firstRequest > SUSPICIOUS_REQUEST_WINDOW) {
      suspiciousRequestCounts.delete(key);
    }
  }
}, 60 * 1000); // Clean up every minute

module.exports = {
  securityBlacklist,
  BLOCKED_PATTERNS,
  SUSPICIOUS_USER_AGENTS,
  MONITORED_IPS
};
