const express = require('express');
const rateLimit = require('express-rate-limit');
const userService = require('../services/userService');
const authService = require('../services/authService');
const logger = require('../utils/logger');

const router = express.Router();

// Rate limiting for user endpoints
const userLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 user requests per windowMs
  message: {
    status: 'error',
    message: 'Too many requests. Please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Apply rate limiting to all user routes
router.use(userLimiter);

// All user routes require authentication
router.use(authService.protect);

/**
 * @route   GET /api/v1/user/profile
 * @desc    Get current user's profile data
 * @access  Private
 */
router.get('/profile', async (req, res) => {
  const startTime = Date.now();
  
  try {
    console.log('👤 [USER] Profile request received');
    console.log('👤 [USER] User ID:', req.user?.id);
    
    if (!req.user || !req.user.id) {
      console.log('❌ [USER] No authenticated user found');
      return res.status(401).json({
        status: 'error',
        message: 'Authentication required'
      });
    }
    
    // Add performance tracking
    const userFetchStart = Date.now();
    const user = await userService.findById(req.user.id);
    const userFetchTime = Date.now() - userFetchStart;
    console.log(`⏱️ [USER] User fetch took: ${userFetchTime}ms`);
    
    if (!user) {
      return res.status(404).json({
        status: 'error',
        message: 'User not found'
      });
    }

    // Prepare optimized response data with all needed fields
    const profileData = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phoneNumber: user.phoneNumber,
      picture: user.picture || user.avatar, // Use avatar if picture is not set
      avatar: user.avatar || user.picture, // Use picture if avatar is not set
      isEmailVerified: user.isEmailVerified,
      isPhoneVerified: user.isPhoneVerified,
      balance: user.balance || 0,
      authProvider: user.authProvider,
      createdAt: user.createdAt,
      // Add any other profile fields you need
    };

    const totalTime = Date.now() - startTime;
    console.log(`⏱️ [USER] Total profile request took: ${totalTime}ms`);
    
    console.log('👤 [USER] Profile response:', {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      hasPicture: !!user.picture,
      performanceMs: totalTime
    });

    // Add performance headers for debugging
    res.set('X-Response-Time', `${totalTime}ms`);
    res.set('X-User-Fetch-Time', `${userFetchTime}ms`);

    res.status(200).json({
      status: 'success',
      data: {
        user: profileData
      }
    });

  } catch (error) {
    const totalTime = Date.now() - startTime;
    console.error(`❌ [USER] Get profile error (${totalTime}ms):`, error);
    logger.error('Get user profile error:', error);
    
    res.status(500).json({
      status: 'error',
      message: 'Internal server error'
    });
  }
});

/**
 * @route   PUT /api/v1/user/profile
 * @desc    Update current user's profile data
 * @access  Private
 */
router.put('/profile', async (req, res) => {
  try {
    console.log('👤 [USER] Profile update request received');
    console.log('👤 [USER] User ID:', req.user?.id);
    console.log('👤 [USER] Update data:', req.body);
    
    if (!req.user || !req.user.id) {
      return res.status(401).json({
        status: 'error',
        message: 'Authentication required'
      });
    }
    
    const userId = req.user.id;
    const updateData = req.body;
    
    // Validate and sanitize update data
    const allowedFields = ['firstName', 'lastName', 'picture', 'avatar', 'dateOfBirth'];
    const sanitizedData = {};

    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        sanitizedData[field] = updateData[field];
      }
    }

    // If avatar is provided, also set it as picture for compatibility
    if (updateData.avatar) {
      sanitizedData.picture = updateData.avatar;
    }

    // If picture is provided, also set it as avatar for compatibility
    if (updateData.picture) {
      sanitizedData.avatar = updateData.picture;
    }
    
    if (Object.keys(sanitizedData).length === 0) {
      return res.status(400).json({
        status: 'error',
        message: 'No valid fields to update'
      });
    }
    
    // Add timestamp
    sanitizedData.updated_at = new Date().toISOString();
    
    // Update user
    const updatedUser = await userService.updateUser(userId, sanitizedData);
    
    console.log('✅ [USER] Profile updated successfully for user:', userId);
    logger.info(`Profile updated for user ${userId}`);

    res.status(200).json({
      status: 'success',
      message: 'Profile updated successfully',
      data: {
        user: {
          id: updatedUser.id,
          firstName: updatedUser.firstName,
          lastName: updatedUser.lastName,
          email: updatedUser.email,
          phoneNumber: updatedUser.phoneNumber,
          picture: updatedUser.picture || updatedUser.avatar,
          avatar: updatedUser.avatar || updatedUser.picture,
          isEmailVerified: updatedUser.isEmailVerified,
          isPhoneVerified: updatedUser.isPhoneVerified,
          balance: updatedUser.balance || 0,
        }
      }
    });

  } catch (error) {
    console.error('❌ [USER] Update profile error:', error);
    logger.error('Update user profile error:', error);
    
    res.status(500).json({
      status: 'error',
      message: 'Internal server error'
    });
  }
});

/**
 * @route   GET /api/v1/user/balance
 * @desc    Get current user's balance
 * @access  Private
 */
router.get('/balance', async (req, res) => {
  try {
    console.log('💰 [USER] Balance request received');
    console.log('💰 [USER] User ID:', req.user?.id);
    
    if (!req.user || !req.user.id) {
      return res.status(401).json({
        status: 'error',
        message: 'Authentication required'
      });
    }
    
    const user = await userService.findById(req.user.id);
    
    if (!user) {
      return res.status(404).json({
        status: 'error',
        message: 'User not found'
      });
    }

    console.log('💰 [USER] Balance response for user:', req.user.id);

    res.status(200).json({
      status: 'success',
      data: {
        balance: user.balance || 0,
        currency: 'NGN'
      }
    });

  } catch (error) {
    console.error('��� [USER] Get balance error:', error);
    logger.error('Get user balance error:', error);
    
    res.status(500).json({
      status: 'error',
      message: 'Internal server error'
    });
  }
});

module.exports = router;