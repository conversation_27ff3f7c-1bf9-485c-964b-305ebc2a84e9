/**
 * Transaction Status Controller
 * 
 * Secure controller for querying and managing transaction status with comprehensive
 * authentication, validation, and real-time status updates from VTpass API.
 * 
 * Features:
 * - Transaction status querying from VTpass
 * - Real-time status synchronization
 * - Comprehensive transaction history
 * - Status update notifications
 * - Audit logging and monitoring
 * - Retry mechanism for failed transactions
 * - Transaction analytics and reporting
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const { param, query, validationResult } = require('express-validator');
const vtpassService = require('../services/vtpassService');
const { vtpassErrorHandler } = require('../utils/vtpassErrorHandler');
const { getSupabase } = require('../config/database');
const logger = require('../utils/logger');
const notificationService = require('../services/notificationService');

/**
 * Transaction Controller Class
 * Handles all transaction status and management operations
 */
class TransactionController {
  constructor() {
    this.supabase = getSupabase();
  }

  /**
   * Validation rules for transaction status query
   */
  static getStatusQueryValidationRules() {
    return [
      param('transactionId')
        .notEmpty()
        .withMessage('Transaction ID is required')
        .isUUID()
        .withMessage('Transaction ID must be a valid UUID')
    ];
  }

  /**
   * Validation rules for reference-based query
   */
  static getReferenceQueryValidationRules() {
    return [
      param('reference')
        .notEmpty()
        .withMessage('Reference is required')
        .isLength({ min: 10, max: 100 })
        .withMessage('Reference must be between 10 and 100 characters')
        .matches(/^[a-zA-Z0-9_-]+$/)
        .withMessage('Reference contains invalid characters')
    ];
  }

  /**
   * Query transaction status by transaction ID
   * 
   * @route GET /api/v1/transactions/:transactionId/status
   * @access Private (User can only query their own transactions)
   */
  async getTransactionStatus(req, res) {
    const requestId = req.headers['x-request-id'] || `status_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      logger.info('🔍 [TRANSACTION_CONTROLLER] Status query initiated:', {
        requestId,
        transactionId: req.params.transactionId,
        userId: req.user.id,
        ip: req.ip
      });

      // Validate request inputs
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        logger.warn('⚠️ [TRANSACTION_CONTROLLER] Validation failed:', {
          requestId,
          errors: errors.array()
        });

        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
          requestId
        });
      }

      const { transactionId } = req.params;
      const userId = req.user.id;

      // Get transaction from database
      const transaction = await this.getTransactionById(transactionId, userId);
      if (!transaction) {
        logger.warn('🚫 [TRANSACTION_CONTROLLER] Transaction not found:', {
          requestId,
          transactionId,
          userId
        });

        return res.status(404).json({
          success: false,
          message: 'Transaction not found',
          code: 'TRANSACTION_NOT_FOUND',
          requestId
        });
      }

      // Check if transaction needs status update from VTpass
      const needsUpdate = this.shouldUpdateStatus(transaction);
      let updatedTransaction = transaction;

      if (needsUpdate && transaction.reference) {
        logger.info('🔄 [TRANSACTION_CONTROLLER] Updating status from VTpass:', {
          requestId,
          transactionId,
          reference: transaction.reference,
          currentStatus: transaction.status
        });

        try {
          // Query VTpass for latest status
          const vtpassResult = await vtpassService.queryTransactionStatus(transaction.reference);
          
          if (vtpassResult.success && vtpassResult.status) {
            // Update transaction status based on VTpass response
            updatedTransaction = await this.updateTransactionFromVTpass(
              transactionId,
              vtpassResult,
              transaction
            );

            // Send notification if status changed to completed or failed
            if (transaction.status !== updatedTransaction.status) {
              await this.sendStatusUpdateNotification(userId, updatedTransaction);
            }
          }
        } catch (vtpassError) {
          logger.warn('⚠️ [TRANSACTION_CONTROLLER] VTpass status query failed:', {
            requestId,
            transactionId,
            error: vtpassError.message
          });
          // Continue with existing transaction data
        }
      }

      logger.info('✅ [TRANSACTION_CONTROLLER] Status query completed:', {
        requestId,
        transactionId,
        status: updatedTransaction.status,
        updated: needsUpdate
      });

      return res.status(200).json({
        success: true,
        data: {
          transaction: this.formatTransactionResponse(updatedTransaction),
          lastUpdated: new Date().toISOString(),
          requestId
        }
      });

    } catch (error) {
      const errorResponse = vtpassErrorHandler.handleError(error, {
        requestId,
        transactionId: req.params.transactionId,
        userId: req.user?.id,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      logger.error('❌ [TRANSACTION_CONTROLLER] Status query failed:', {
        requestId,
        error: error.message,
        stack: error.stack
      });

      return res.status(500).json({
        ...errorResponse,
        requestId
      });
    }
  }

  /**
   * Query transaction status by reference
   * 
   * @route GET /api/v1/transactions/reference/:reference/status
   * @access Private
   */
  async getTransactionStatusByReference(req, res) {
    const requestId = req.headers['x-request-id'] || `ref_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      logger.info('🔍 [TRANSACTION_CONTROLLER] Reference status query:', {
        requestId,
        reference: req.params.reference,
        userId: req.user.id
      });

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
          requestId
        });
      }

      const { reference } = req.params;
      const userId = req.user.id;

      // Get transaction by reference
      const transaction = await this.getTransactionByReference(reference, userId);
      if (!transaction) {
        return res.status(404).json({
          success: false,
          message: 'Transaction not found',
          code: 'TRANSACTION_NOT_FOUND',
          requestId
        });
      }

      // Query VTpass for latest status
      const vtpassResult = await vtpassService.queryTransactionStatus(reference);
      
      // Update transaction with latest status
      const updatedTransaction = await this.updateTransactionFromVTpass(
        transaction.id,
        vtpassResult,
        transaction
      );

      return res.status(200).json({
        success: true,
        data: {
          transaction: this.formatTransactionResponse(updatedTransaction),
          vtpassResponse: vtpassResult,
          lastUpdated: new Date().toISOString(),
          requestId
        }
      });

    } catch (error) {
      const errorResponse = vtpassErrorHandler.handleError(error, {
        requestId,
        reference: req.params.reference,
        userId: req.user?.id,
        ip: req.ip
      });

      return res.status(500).json({
        ...errorResponse,
        requestId
      });
    }
  }

  /**
   * Get transaction by ID with user ownership check
   */
  async getTransactionById(transactionId, userId) {
    try {
      const { data, error } = await this.supabase
        .from('transactions')
        .select('*')
        .eq('id', transactionId)
        .eq('user_id', userId)
        .single();

      if (error) {
        logger.error('❌ [TRANSACTION_CONTROLLER] Transaction query error:', error);
        return null;
      }

      return data;
    } catch (error) {
      logger.error('❌ [TRANSACTION_CONTROLLER] Get transaction error:', error);
      return null;
    }
  }

  /**
   * Get transaction by reference with user ownership check
   */
  async getTransactionByReference(reference, userId) {
    try {
      const { data, error } = await this.supabase
        .from('transactions')
        .select('*')
        .eq('reference', reference)
        .eq('user_id', userId)
        .single();

      if (error) {
        logger.error('❌ [TRANSACTION_CONTROLLER] Reference query error:', error);
        return null;
      }

      return data;
    } catch (error) {
      logger.error('❌ [TRANSACTION_CONTROLLER] Get by reference error:', error);
      return null;
    }
  }

  /**
   * Check if transaction status should be updated from VTpass
   */
  shouldUpdateStatus(transaction) {
    // Don't update completed or cancelled transactions
    if (transaction.status === 'completed' || transaction.status === 'cancelled') {
      return false;
    }

    // Update pending or failed transactions
    if (transaction.status === 'pending' || transaction.status === 'failed') {
      // Check if enough time has passed since last update
      const lastUpdate = new Date(transaction.updated_at);
      const now = new Date();
      const timeDiff = now - lastUpdate;
      
      // Update if more than 30 seconds have passed
      return timeDiff > 30000;
    }

    return false;
  }

  /**
   * Update transaction status based on VTpass response
   */
  async updateTransactionFromVTpass(transactionId, vtpassResult, currentTransaction) {
    try {
      let newStatus = currentTransaction.status;
      let completedAt = null;
      let providerTransactionId = currentTransaction.provider_transaction_id;

      // Map VTpass status to our status
      if (vtpassResult.success && vtpassResult.status) {
        switch (vtpassResult.status.toLowerCase()) {
          case 'delivered':
          case 'successful':
            newStatus = 'completed';
            completedAt = new Date().toISOString();
            break;
          case 'pending':
            newStatus = 'pending';
            break;
          case 'failed':
          case 'error':
            newStatus = 'failed';
            break;
        }

        // Update provider transaction ID if available
        if (vtpassResult.transactionId) {
          providerTransactionId = vtpassResult.transactionId;
        }
      }

      // Update transaction in database
      const updateData = {
        status: newStatus,
        provider_transaction_id: providerTransactionId,
        updated_at: new Date().toISOString(),
        metadata: {
          ...currentTransaction.metadata,
          vtpass_status_query: vtpassResult,
          last_status_check: new Date().toISOString()
        }
      };

      if (completedAt) {
        updateData.completed_at = completedAt;
      }

      const { data, error } = await this.supabase
        .from('transactions')
        .update(updateData)
        .eq('id', transactionId)
        .select()
        .single();

      if (error) {
        logger.error('❌ [TRANSACTION_CONTROLLER] Transaction update error:', error);
        return currentTransaction;
      }

      logger.info('✅ [TRANSACTION_CONTROLLER] Transaction updated:', {
        transactionId,
        oldStatus: currentTransaction.status,
        newStatus: data.status,
        providerTransactionId: data.provider_transaction_id
      });

      return data;
    } catch (error) {
      logger.error('❌ [TRANSACTION_CONTROLLER] Update from VTpass error:', error);
      return currentTransaction;
    }
  }

  /**
   * Send status update notification to user
   */
  async sendStatusUpdateNotification(userId, transaction) {
    try {
      if (transaction.status === 'completed') {
        await notificationService.sendPurchaseNotification(userId, {
          type: transaction.type,
          amount: transaction.amount,
          recipient: transaction.recipient,
          provider: transaction.provider,
          transactionId: transaction.id,
          status: 'completed'
        });
      } else if (transaction.status === 'failed') {
        // Send failure notification
        await notificationService.sendTransactionNotification(userId, {
          type: 'transaction_failed',
          amount: transaction.amount,
          recipient: transaction.recipient,
          reference: transaction.reference,
          reason: 'Transaction failed at provider'
        });
      }

      logger.info('📱 [TRANSACTION_CONTROLLER] Status notification sent:', {
        userId,
        transactionId: transaction.id,
        status: transaction.status
      });
    } catch (error) {
      logger.error('❌ [TRANSACTION_CONTROLLER] Notification error:', error);
    }
  }

  /**
   * Format transaction response for API
   */
  formatTransactionResponse(transaction) {
    return {
      id: transaction.id,
      reference: transaction.reference,
      type: transaction.type,
      amount: parseFloat(transaction.amount),
      recipient: transaction.recipient,
      provider: transaction.provider,
      status: transaction.status,
      description: transaction.description,
      providerTransactionId: transaction.provider_transaction_id,
      createdAt: transaction.created_at,
      updatedAt: transaction.updated_at,
      completedAt: transaction.completed_at,
      metadata: {
        riskScore: transaction.metadata?.riskScore,
        provider: transaction.metadata?.provider,
        lastStatusCheck: transaction.metadata?.last_status_check
      }
    };
  }

  /**
   * Get user's transaction history with filtering
   * 
   * @route GET /api/v1/transactions
   * @access Private
   */
  async getTransactionHistory(req, res) {
    try {
      const userId = req.user.id;
      const { 
        page = 1, 
        limit = 20, 
        status, 
        type, 
        startDate, 
        endDate,
        provider 
      } = req.query;
      
      const offset = (page - 1) * limit;

      let query = this.supabase
        .from('transactions')
        .select('*', { count: 'exact' })
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      // Apply filters
      if (status) query = query.eq('status', status);
      if (type) query = query.eq('type', type);
      if (provider) query = query.eq('provider', provider);
      if (startDate) query = query.gte('created_at', startDate);
      if (endDate) query = query.lte('created_at', endDate);

      const { data, error, count } = await query;

      if (error) {
        logger.error('❌ [TRANSACTION_CONTROLLER] History query error:', error);
        return res.status(500).json({
          success: false,
          message: 'Failed to retrieve transaction history'
        });
      }

      return res.status(200).json({
        success: true,
        data: {
          transactions: data.map(tx => this.formatTransactionResponse(tx)),
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: count,
            pages: Math.ceil(count / limit)
          }
        }
      });
    } catch (error) {
      logger.error('❌ [TRANSACTION_CONTROLLER] Get history error:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to retrieve transaction history'
      });
    }
  }
}

// Create singleton instance
const transactionController = new TransactionController();

module.exports = {
  getTransactionStatus: transactionController.getTransactionStatus.bind(transactionController),
  getTransactionStatusByReference: transactionController.getTransactionStatusByReference.bind(transactionController),
  getTransactionHistory: transactionController.getTransactionHistory.bind(transactionController),
  getStatusQueryValidationRules: TransactionController.getStatusQueryValidationRules,
  getReferenceQueryValidationRules: TransactionController.getReferenceQueryValidationRules,
  transactionController
};
