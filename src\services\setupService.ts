import { apiService } from './apiService';
import { UserData, SetupStatus, SetupStatusResponse } from '../types/user';
import logger from './productionLogger';

export type SetupStep = 'NameSetup' | 'PinSetup' | 'PinVerification' | 'BiometricSetup' | 'SetupComplete';

export interface PinSetupRequest {
  pin: string;
  confirmPin: string;
}

export interface PinVerifyRequest {
  pin: string;
}

export interface PinVerifyResponse {
  pinVerified: boolean;
  setupStatus: SetupStatus;
  user: UserData & { balance?: string };
  welcomeMessage: string;
}

export interface BiometricSetupRequest {
  enabled: boolean;
  biometricType?: 'fingerprint' | 'face' | 'voice' | 'iris';
  deviceInfo?: {
    platform: string;
    version: string | number;
    model: string;
  };
}

export interface ProfileSetupRequest {
  firstName: string;
  lastName?: string;
  dateOfBirth?: string;
  avatar?: string;
}

class SetupService {
  /**
   * Get current setup status for the user with enhanced validation
   */
  async getSetupStatus(): Promise<SetupStatusResponse> {
    try {
      logger.debug('🔍 [SETUP-SERVICE] Fetching setup status from backend...', null, 'setup');

      // Use optimized configuration for setup status
      const response = await apiService.get('/setup/status', {}, {
        timeout: 15000, // 15 seconds timeout
        retries: 2, // Allow 2 retries for reliability
        cache: false, // Disable caching for fresh data on app return
      });

      logger.debug('✅ [SETUP-SERVICE] Setup status API response received', {
        hasData: !!response.data,
        status: response.data?.status,
        hasSetupStatus: !!response.data?.data?.setupStatus,
        hasUser: !!response.data?.data?.user
      }, 'setup');
      
      if (response.data.status === 'success') {
        logger.info('Setup status retrieved successfully', {
          hasSetupStatus: !!response.data.data?.setupStatus,
          hasUser: !!response.data.data?.user
        }, 'setup');

        // Validate and normalize the setup status data
        const validatedData = this.validateSetupStatusResponse(response.data.data);

        logger.debug('✅ [SETUP-SERVICE] Setup status validated', {
          hasProfileSetup: validatedData.setupStatus.hasProfileSetup,
          hasPinSetup: validatedData.setupStatus.hasPinSetup,
          setupComplete: validatedData.setupStatus.setupComplete,
          userFirstName: validatedData.user.firstName
        }, 'setup');

        return validatedData;
      } else {
        throw new Error(response.data.message || 'Failed to get setup status');
      }
    } catch (error: any) {
      logger.error('Get setup status error', error, 'setup');
      
      // Provide more specific error messages for better debugging
      if (error.name === 'AbortError' || error.message === 'Aborted') {
        throw new Error('Setup status request timed out. Please check your connection and try again.');
      } else if (error.message?.includes('Network')) {
        throw new Error('Network error while checking setup status. Please check your connection.');
      } else {
        throw new Error(error.response?.data?.message || error.message || 'Failed to get setup status');
      }
    }
  }

  /**
   * Validate and normalize setup status response data
   */
  private validateSetupStatusResponse(data: any): SetupStatusResponse {
    // Ensure we have the required structure
    const setupStatus: SetupStatus = {
      hasPinSetup: Boolean(data.setupStatus?.hasPinSetup),
      hasBiometricSetup: Boolean(data.setupStatus?.hasBiometricSetup),
      hasProfileSetup: Boolean(data.setupStatus?.hasProfileSetup),
      isEmailVerified: Boolean(data.setupStatus?.isEmailVerified),
      isPhoneVerified: Boolean(data.setupStatus?.isPhoneVerified),
      setupComplete: Boolean(data.setupStatus?.setupComplete),
    };

    // Additional validation for profile setup based on user data
    if (data.user?.firstName && data.user.firstName.trim() !== '') {
      setupStatus.hasProfileSetup = true;
    }

    const user: UserData = {
      id: data.user?.id || '',
      firstName: data.user?.firstName || '',
      lastName: data.user?.lastName || '',
      email: data.user?.email || '',
      phoneNumber: data.user?.phoneNumber || '',
      isEmailVerified: Boolean(data.user?.isEmailVerified),
      isPhoneVerified: Boolean(data.user?.isPhoneVerified),
      picture: data.user?.picture || '',
    };

    logger.debug('🔍 [SETUP-SERVICE] Setup status validation complete', {
      originalHasProfileSetup: data.setupStatus?.hasProfileSetup,
      validatedHasProfileSetup: setupStatus.hasProfileSetup,
      userFirstName: user.firstName,
      hasPinSetup: setupStatus.hasPinSetup
    }, 'setup');

    return {
      setupStatus,
      user,
    };
  }

  /**
   * Get comprehensive setup status including local storage checks
   */
  async getComprehensiveSetupStatus(): Promise<SetupStatusResponse> {
    try {
      // Get backend setup status
      const backendStatus = await this.getSetupStatus();

      // Check local PIN storage status
      const { secureStorage } = await import('./secureStorageService');
      const isPinStoredLocally = await secureStorage.isPinStored();

      // Combine backend and local status for more accurate routing
      const enhancedSetupStatus = {
        ...backendStatus.setupStatus,
        hasPinSetup: backendStatus.setupStatus.hasPinSetup && isPinStoredLocally,
      };

      logger.debug('🔍 [SETUP-SERVICE] Comprehensive setup status', {
        backendPinSetup: backendStatus.setupStatus.hasPinSetup,
        localPinStored: isPinStoredLocally,
        finalPinSetup: enhancedSetupStatus.hasPinSetup,
        hasProfileSetup: enhancedSetupStatus.hasProfileSetup
      }, 'setup');

      return {
        setupStatus: enhancedSetupStatus,
        user: backendStatus.user,
      };
    } catch (error) {
      logger.error('Failed to get comprehensive setup status', error, 'setup');
      throw error;
    }
  }

  /**
   * Set up user PIN (for new users)
   */
  async setupPin(pinData: PinSetupRequest): Promise<void> {
    try {
      const response = await apiService.post('/setup/pin', pinData);
      
      if (response.data.status !== 'success') {
        throw new Error(response.data.message || 'Failed to set up PIN');
      }
    } catch (error: any) {
      logger.error('PIN setup error', error, 'setup');
      
      // Check if PIN is already set and redirect to verification
      if (error.response?.data?.data?.pinAlreadySet) {
        throw new Error('PIN_ALREADY_SET');
      }
      
      throw new Error(error.response?.data?.message || 'Failed to set up PIN');
    }
  }

  /**
   * Verify existing PIN (for returning users)
   */
  async verifyPin(pinData: PinVerifyRequest): Promise<PinVerifyResponse> {
    try {
      logger.info('Starting PIN verification with extended timeout', null, 'setup');
      // Use extended timeout for PIN verification as it may take longer
      // Disable retries to prevent multiple failed attempts from being counted
      const response = await apiService.post('/setup/verify-pin', pinData, {
        timeout: 30000, // 30 seconds timeout
        retries: 0 // No retries to prevent multiple login attempt increments
      });
      
      if (response.data.status === 'success') {
        logger.info('PIN verification successful', null, 'setup');
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to verify PIN');
      }
    } catch (error: any) {
      logger.error('PIN verification error', error, 'setup');
      
      // Provide more specific error messages
      if (error.name === 'AbortError' || error.message === 'Aborted') {
        throw new Error('Request timed out. Please try again.');
      } else if (error.message?.includes('Network')) {
        throw new Error('Network error. Please check your connection.');
      } else {
        throw new Error(error.response?.data?.message || error.message || 'Failed to verify PIN');
      }
    }
  }

  /**
   * Set up biometric authentication
   */
  async setupBiometric(biometricData: BiometricSetupRequest): Promise<void> {
    try {
      const response = await apiService.post('/setup/biometric', biometricData);
      
      if (response.data.status !== 'success') {
        throw new Error(response.data.message || 'Failed to set up biometric authentication');
      }
    } catch (error: any) {
      logger.error('Biometric setup error', error, 'setup');
      throw new Error(error.response?.data?.message || 'Failed to set up biometric authentication');
    }
  }

  /**
   * Complete profile setup
   */
  async setupProfile(profileData: ProfileSetupRequest): Promise<UserData> {
    try {
      const response = await apiService.post('/setup/profile', profileData);
      
      if (response.data.status === 'success') {
        return response.data.data.user;
      } else {
        throw new Error(response.data.message || 'Failed to set up profile');
      }
    } catch (error: any) {
      logger.error('Profile setup error', error, 'setup');
      throw new Error(error.response?.data?.message || 'Failed to set up profile');
    }
  }

  /**
   * Mark setup as complete
   */
  async completeSetup(): Promise<void> {
    try {
      const response = await apiService.post('/setup/complete');
      
      if (response.data.status !== 'success') {
        throw new Error(response.data.message || 'Failed to complete setup');
      }
    } catch (error: any) {
      logger.error('Complete setup error', error, 'setup');
      throw new Error(error.response?.data?.message || 'Failed to complete setup');
    }
  }

  /**
   * Check if user needs to go through setup flow
   */
  async shouldShowSetup(): Promise<boolean> {
    try {
      const { setupStatus } = await this.getSetupStatus();
      return !setupStatus.setupComplete;
    } catch (error) {
      logger.error('Error checking setup status', error, 'setup');
      // If we can't check, assume setup is needed
      return true;
    }
  }

  /**
   * Get next setup step based on current status
   */
  async getNextSetupStep(): Promise<SetupStep | null> {
    try {
      const { setupStatus } = await this.getSetupStatus();
      
      if (setupStatus.setupComplete) {
        return null; // No setup needed
      }
      
      if (!setupStatus.hasProfileSetup) {
        return 'NameSetup';
      }
      
      if (!setupStatus.hasPinSetup) {
        return 'PinSetup'; // Create new PIN
      }
      
      // If PIN is set but setup is not complete, verify PIN first
      if (setupStatus.hasPinSetup && !setupStatus.setupComplete) {
        return 'PinVerification'; // Verify existing PIN
      }
      
      if (!setupStatus.hasBiometricSetup) {
        return 'BiometricSetup';
      }
      
      return 'SetupComplete';
    } catch (error) {
      logger.error('Error getting next setup step', error, 'setup');
      return 'NameSetup'; // Default to first step
    }
  }

  /**
   * Determine if user should see PIN setup or PIN verification
   */
  async getPinFlowType(): Promise<'setup' | 'verify' | 'complete'> {
    try {
      const { setupStatus } = await this.getSetupStatus();
      
      if (setupStatus.setupComplete) {
        return 'complete'; // No PIN flow needed
      }
      
      if (setupStatus.hasPinSetup) {
        return 'verify'; // PIN exists, show verification
      } else {
        return 'setup'; // No PIN, show setup
      }
    } catch (error) {
      logger.error('Error determining PIN flow type', error, 'setup');
      return 'setup'; // Default to setup
    }
  }
}

export const setupService = new SetupService();