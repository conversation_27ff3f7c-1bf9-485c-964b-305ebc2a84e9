import { PERFORMANCE_CONFIG } from '../config/environment';

interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  expiresAt: number;
  accessCount: number;
  lastAccessed: number;
  size: number; // Approximate size in bytes
}

interface CacheStats {
  hits: number;
  misses: number;
  evictions: number;
  totalRequests: number;
  hitRate: number;
  memoryUsage: number;
  entryCount: number;
}

class CacheService {
  private cache = new Map<string, CacheEntry>();
  private accessOrder = new Map<string, number>(); // For LRU tracking
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    totalRequests: 0,
    hitRate: 0,
    memoryUsage: 0,
    entryCount: 0,
  };
  
  private readonly maxSize = PERFORMANCE_CONFIG.CACHE_SIZE_LIMIT;
  private readonly defaultTtl = 5 * 60 * 1000; // 5 minutes
  private accessCounter = 0;

  /**
   * Calculate approximate size of data in bytes
   */
  private calculateSize(data: any): number {
    if (typeof data === 'string') {
      return data.length * 2; // UTF-16 encoding
    }
    if (typeof data === 'number') {
      return 8; // 64-bit number
    }
    if (typeof data === 'boolean') {
      return 4; // 32-bit boolean
    }
    if (data === null || data === undefined) {
      return 0;
    }
    if (typeof data === 'object') {
      try {
        return JSON.stringify(data).length * 2;
      } catch {
        return 100; // Fallback estimate
      }
    }
    return 50; // Default estimate
  }

  /**
   * Update cache statistics
   */
  private updateStats(): void {
    this.stats.totalRequests = this.stats.hits + this.stats.misses;
    this.stats.hitRate = this.stats.totalRequests > 0 
      ? (this.stats.hits / this.stats.totalRequests) * 100 
      : 0;
    this.stats.entryCount = this.cache.size;
    
    // Calculate memory usage
    this.stats.memoryUsage = Array.from(this.cache.values())
      .reduce((total, entry) => total + entry.size, 0);
  }

  /**
   * Evict expired entries
   */
  private evictExpired(): number {
    const now = Date.now();
    let evicted = 0;
    
    const expiredKeys = Array.from(this.cache.entries())
      .filter(([_, entry]) => now > entry.expiresAt)
      .map(([key, _]) => key);
    
    for (const key of expiredKeys) {
      this.cache.delete(key);
      this.accessOrder.delete(key);
      evicted++;
    }
    
    this.stats.evictions += evicted;
    return evicted;
  }

  /**
   * Evict least recently used entries to make space
   */
  private evictLRU(count: number = 1): number {
    const sortedByAccess = Array.from(this.accessOrder.entries())
      .sort(([, a], [, b]) => a - b) // Sort by access order (oldest first)
      .slice(0, count)
      .map(([key]) => key);
    
    let evicted = 0;
    for (const key of sortedByAccess) {
      if (this.cache.delete(key)) {
        this.accessOrder.delete(key);
        evicted++;
      }
    }
    
    this.stats.evictions += evicted;
    return evicted;
  }

  /**
   * Ensure cache doesn't exceed size limit
   */
  private ensureCapacity(): void {
    // First, remove expired entries
    this.evictExpired();
    
    // If still over capacity, use LRU eviction
    if (this.cache.size >= this.maxSize) {
      const toEvict = this.cache.size - this.maxSize + 1;
      this.evictLRU(toEvict);
    }
  }

  /**
   * Generate cache key from components
   */
  createKey(...components: (string | number | boolean)[]): string {
    return components
      .map(c => String(c))
      .join(':')
      .toLowerCase()
      .replace(/[^a-z0-9:_-]/g, '_');
  }

  /**
   * Store data in cache
   */
  set<T>(key: string, data: T, ttl: number = this.defaultTtl): void {
    this.ensureCapacity();
    
    const now = Date.now();
    const size = this.calculateSize(data);
    
    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      expiresAt: now + ttl,
      accessCount: 1,
      lastAccessed: now,
      size,
    };
    
    this.cache.set(key, entry);
    this.accessOrder.set(key, ++this.accessCounter);
    this.updateStats();
  }

  /**
   * Retrieve data from cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key) as CacheEntry<T> | undefined;
    
    if (!entry) {
      this.stats.misses++;
      this.updateStats();
      return null;
    }
    
    const now = Date.now();
    
    // Check if expired
    if (now > entry.expiresAt) {
      this.cache.delete(key);
      this.accessOrder.delete(key);
      this.stats.misses++;
      this.stats.evictions++;
      this.updateStats();
      return null;
    }
    
    // Update access tracking
    entry.accessCount++;
    entry.lastAccessed = now;
    this.accessOrder.set(key, ++this.accessCounter);
    
    this.stats.hits++;
    this.updateStats();
    
    return entry.data;
  }

  /**
   * Check if key exists and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (Date.now() > entry.expiresAt) {
      this.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * Delete entry from cache
   */
  delete(key: string): boolean {
    const existed = this.cache.delete(key);
    this.accessOrder.delete(key);
    
    if (existed) {
      this.updateStats();
    }
    
    return existed;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    this.accessOrder.clear();
    this.accessCounter = 0;
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      totalRequests: 0,
      hitRate: 0,
      memoryUsage: 0,
      entryCount: 0,
    };
  }

  /**
   * Get or set pattern - retrieve if exists, otherwise compute and cache
   */
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T> | T,
    ttl: number = this.defaultTtl
  ): Promise<T> {
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }
    
    const data = await factory();
    this.set(key, data, ttl);
    return data;
  }

  /**
   * Set data with expiration time instead of TTL
   */
  setWithExpiry<T>(key: string, data: T, expiresAt: number): void {
    const now = Date.now();
    const ttl = Math.max(0, expiresAt - now);
    this.set(key, data, ttl);
  }

  /**
   * Extend TTL of existing entry
   */
  extend(key: string, additionalTtl: number): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    const now = Date.now();
    if (now > entry.expiresAt) {
      this.delete(key);
      return false;
    }
    
    entry.expiresAt += additionalTtl;
    return true;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    this.updateStats();
    return { ...this.stats };
  }

  /**
   * Get all cache keys (for debugging)
   */
  getKeys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Prune cache manually (remove expired entries)
   */
  prune(): number {
    return this.evictExpired();
  }

  /**
   * Get cache entry metadata
   */
  getMetadata(key: string): Omit<CacheEntry, 'data'> | null {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    const { data, ...metadata } = entry;
    return metadata;
  }

  /**
   * Bulk operations
   */
  setMany<T>(entries: Array<{ key: string; data: T; ttl?: number }>): void {
    for (const { key, data, ttl } of entries) {
      this.set(key, data, ttl);
    }
  }

  getMany<T>(keys: string[]): Array<{ key: string; data: T | null }> {
    return keys.map(key => ({ key, data: this.get<T>(key) }));
  }

  deleteMany(keys: string[]): number {
    let deleted = 0;
    for (const key of keys) {
      if (this.delete(key)) {
        deleted++;
      }
    }
    return deleted;
  }

  /**
   * Get cache efficiency report
   */
  getEfficiencyReport(): {
    hitRate: number;
    memoryUsage: string;
    totalEntries: number;
    averageEntrySize: string;
    oldestEntry: string;
    newestEntry: string;
    recommendation: string;
  } {
    this.updateStats();
    
    const entries = Array.from(this.cache.entries());
    const now = Date.now();
    
    let oldestTime = now;
    let newestTime = 0;
    
    for (const [_, entry] of entries) {
      if (entry.timestamp < oldestTime) oldestTime = entry.timestamp;
      if (entry.timestamp > newestTime) newestTime = entry.timestamp;
    }
    
    const avgSize = this.stats.entryCount > 0 
      ? this.stats.memoryUsage / this.stats.entryCount 
      : 0;
    
    let recommendation = 'Cache is performing well.';
    if (this.stats.hitRate < 50) {
      recommendation = 'Low hit rate. Consider increasing TTL or reviewing cache strategy.';
    } else if (this.stats.memoryUsage > 10 * 1024 * 1024) { // 10MB
      recommendation = 'High memory usage. Consider reducing cache size or TTL.';
    } else if (this.stats.hitRate > 90) {
      recommendation = 'Excellent hit rate. Cache is very effective.';
    }
    
    return {
      hitRate: Math.round(this.stats.hitRate * 100) / 100,
      memoryUsage: this.formatBytes(this.stats.memoryUsage),
      totalEntries: this.stats.entryCount,
      averageEntrySize: this.formatBytes(avgSize),
      oldestEntry: oldestTime === now ? 'N/A' : this.formatDuration(now - oldestTime),
      newestEntry: newestTime === 0 ? 'N/A' : this.formatDuration(now - newestTime),
      recommendation,
    };
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  private formatDuration(ms: number): string {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ${minutes % 60}m ago`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s ago`;
    return `${seconds}s ago`;
  }
}

// Export singleton instance
export const cacheService = new CacheService();
export default cacheService;
