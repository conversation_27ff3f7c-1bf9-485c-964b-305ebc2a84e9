import React, { memo, useMemo, useCallback } from 'react';
import { createBottomTabNavigator, BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import { View, StyleSheet, Text } from 'react-native';
import { useTheme } from '../components/ThemeContext';
import { BottomTabParamList, RootStackParamList } from '../types/navigation';
import { CompositeScreenProps } from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import {
  HomeIcon,
  PaymentCardIcon,
  WalletIcon,
  SettingsIcon
} from '../components/icons';

import HomeScreen from '../screens/CustomHome/CustomHomeScreen';
import PaymentsScreen from '../screens/Payments/PaymentsScreen';
import HistoryScreen from '../screens/History/HistoryScreen';
import SettingsScreen from '../screens/Settings/SettingsScreen';

const Tab = createBottomTabNavigator<BottomTabParamList>();

// Type-safe wrapper component that properly adapts tab navigation to stack navigation
type HomeTabScreenProps = BottomTabScreenProps<BottomTabParamList, 'HomeTab'>;

const HomeTabScreen: React.FC<HomeTabScreenProps> = ({ navigation }) => {
  // Create a type-safe navigation object that matches what HomeScreen expects
  const stackNavigation = {
    ...navigation,
    navigate: (screen: keyof RootStackParamList, params?: any) => {
      // Handle navigation to stack screens from tab context
      if (screen === 'Home') {
        // Already on home tab, no need to navigate
        return;
      }
      // Navigate to other stack screens
      navigation.navigate(screen as any, params);
    },
    push: (screen: keyof RootStackParamList, params?: any) => {
      navigation.navigate(screen as any, params);
    },
    replace: (screen: keyof RootStackParamList, params?: any) => {
      navigation.navigate(screen as any, params);
    },
    goBack: () => navigation.goBack(),
    canGoBack: () => navigation.canGoBack(),
  };

  const stackRoute = {
    key: 'Home',
    name: 'Home' as const,
    params: undefined,
  };

  return <HomeScreen navigation={stackNavigation as any} route={stackRoute as any} />;
};

interface TabIconProps {
  focused: boolean;
  color: string;
  size: number;
  icon: React.ComponentType<{ size?: number; color?: string }>;
  label: string;
}

const TabIcon: React.FC<TabIconProps> = memo(({ focused, color, icon: IconComponent, label }) => {
  const { theme } = useTheme();

  // Memoize the icon color calculation - use exact colors from reference
  const iconColor = useMemo(() =>
    focused ? '#8B5CF6' : '#9CA3AF', // Purple when active, gray when inactive
    [focused]
  );

  const labelColor = useMemo(() =>
    focused ? '#8B5CF6' : '#6B7280', // Slightly darker gray for text
    [focused]
  );

  return (
    <View style={styles.tabIconContainer}>
      <IconComponent
        size={24}
        color={iconColor}
      />
    </View>
  );
});

const BottomTabNavigator: React.FC = memo(() => {
  const { theme } = useTheme();

  // Memoize screen options for performance
  const screenOptions = useMemo(() => ({
    headerShown: false,
    tabBarStyle: {
      backgroundColor: theme.colors.background,
      borderTopWidth: 0,
      height: 60,
      paddingBottom: 10,
      paddingTop: 10,
      paddingHorizontal: 0, // Remove horizontal padding to start from edge
      elevation: 0,
      shadowOpacity: 0,
    },
    tabBarActiveTintColor: theme.colors.primary,
    tabBarInactiveTintColor: theme.colors.muted,
    tabBarShowLabel: false,
  }), [theme]);

  // Memoized tab icon render functions for performance
  const renderHomeIcon = useCallback(({ focused, color, size }: any) => (
    <TabIcon focused={focused} color={color} size={size} icon={HomeIcon} label="Home" />
  ), []);

  const renderCardsIcon = useCallback(({ focused, color, size }: any) => (
    <TabIcon focused={focused} color={color} size={size} icon={PaymentCardIcon} label="Cards" />
  ), []);

  const renderHistoryIcon = useCallback(({ focused, color, size }: any) => (
    <TabIcon focused={focused} color={color} size={size} icon={WalletIcon} label="History" />
  ), []);

  const renderSettingsIcon = useCallback(({ focused, color, size }: any) => (
    <TabIcon focused={focused} color={color} size={size} icon={SettingsIcon} label="Settings" />
  ), []);

  return (
    <Tab.Navigator screenOptions={screenOptions}>
      <Tab.Screen
        name="HomeTab"
        component={HomeTabScreen}
        options={{ tabBarIcon: renderHomeIcon }}
      />
      <Tab.Screen
        name="PaymentsTab"
        component={PaymentsScreen}
        options={{ tabBarIcon: renderCardsIcon }}
      />
      <Tab.Screen
        name="HistoryTab"
        component={HistoryScreen}
        options={{ tabBarIcon: renderHistoryIcon }}
      />
      <Tab.Screen
        name="ProfileTab"
        component={SettingsScreen}
        options={{ tabBarIcon: renderSettingsIcon }}
      />
    </Tab.Navigator>
  );
});

const styles = StyleSheet.create({
  tabIconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    paddingVertical: 8,
  },
  iconBackground: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default BottomTabNavigator;
