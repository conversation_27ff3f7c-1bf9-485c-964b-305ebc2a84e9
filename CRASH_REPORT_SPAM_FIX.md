# Crash Report Spam Fix

## Problem Description

The mobile app was repeatedly sending crash reports to the `/api/v1/crash-reports` endpoint, causing spam. The issue was identified as an infinite loop where:

1. Session expiry errors occurred in the mobile app
2. These errors were logged through the logger service
3. The logger service sent them to the crash reporting service
4. The crash reporting service tried to send them to the backend API
5. The API call failed due to expired session, generating another session expiry error
6. This created an infinite loop of crash reports

## Root Cause Analysis

The terminal output showed repeated POST requests to `/api/v1/crash-reports` with the message "Session expired. Please login again...." This indicated that session expiry errors were being treated as crashes rather than normal authentication flow events.

## Fixes Implemented

### 1. Mobile App - Crash Reporting Service (`src/services/crashReportingService.ts`)

**Added session error detection:**
- Added logic to detect session/auth related errors and skip crash reporting for them
- Added rate limiting to prevent spam even if other issues occur
- Session errors now only add breadcrumbs for debugging instead of full crash reports

**Changes:**
- Added rate limiting properties: `crashReportCount`, `crashReportWindow`, `maxCrashReportsPerWindow`
- Added `canSendCrashReport()` method for rate limiting
- Enhanced `recordError()` to skip session/auth errors
- Limited to 5 crash reports per minute per client

### 2. Mobile App - Production Logger (`src/services/productionLogger.ts`)

**Improved error categorization:**
- Session/auth errors are no longer sent to crash reporting
- These errors now only add breadcrumbs instead of triggering crash reports
- Maintains logging for debugging while preventing spam

### 3. Mobile App - Auth Utils (`src/utils/authUtils.ts`)

**Better session expiry handling:**
- Changed session expiry logging from `logger.security()` to `logger.info()`
- Treats session expiry as normal flow rather than security incident
- Prevents unnecessary crash report generation

### 4. Mobile App - API Service (`src/services/apiService.ts`)

**Graceful session handling:**
- Changed session expiry logging from `logger.warn()` to `logger.info()`
- Treats token refresh failures as expected behavior
- Reduces noise in error reporting

### 5. Backend - Crash Reports Route (`backend/routes/crashReports.js`)

**Server-side protection:**
- Added detection for session expiry messages
- Session expiry reports return success but don't store as crashes
- Enhanced rate limiting (reduced from 10 to 5 reports per minute)
- Added skip logic for successful requests in rate limiter

## Testing

Created test script `backend/scripts/test-crash-report-fixes.js` to verify:
1. Session expiry reports are handled without creating crashes
2. Legitimate crash reports still work properly
3. Rate limiting prevents spam

## Usage

To test the fixes:
```bash
cd backend
TEST_USER_TOKEN=your_valid_token node scripts/test-crash-report-fixes.js
```

## Expected Behavior After Fix

1. **Session Expiry**: No longer generates crash reports, handled gracefully by auth system
2. **Rate Limiting**: Maximum 5 crash reports per minute per client
3. **Legitimate Crashes**: Still properly reported and stored
4. **No Infinite Loops**: Session errors don't trigger additional session errors

## Monitoring

- Check server logs for reduced crash report spam
- Monitor `/api/v1/crash-reports` endpoint for normal traffic patterns
- Verify legitimate crashes are still being captured
- Watch for 429 (rate limited) responses indicating spam prevention is working

## Files Modified

### Mobile App
- `src/services/crashReportingService.ts` - Added session error detection and rate limiting
- `src/services/productionLogger.ts` - Improved error categorization
- `src/utils/authUtils.ts` - Better session expiry handling
- `src/services/apiService.ts` - Graceful session handling

### Backend
- `backend/routes/crashReports.js` - Server-side session error detection and enhanced rate limiting

### Testing
- `backend/scripts/test-crash-report-fixes.js` - Test script for verification
- `CRASH_REPORT_SPAM_FIX.md` - This documentation

## Prevention Measures

1. **Client-side rate limiting** prevents spam from any source
2. **Error categorization** ensures only real crashes are reported
3. **Server-side filtering** provides additional protection
4. **Graceful session handling** treats auth issues as normal flow
5. **Comprehensive testing** ensures fixes work as expected

The fixes maintain full crash reporting functionality while eliminating the infinite loop that was causing spam.
