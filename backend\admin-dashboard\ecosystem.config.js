module.exports = {
  apps: [{
    name: 'vendy-admin-dashboard',
    script: 'server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      ADMIN_PORT: 3001,
      API_BASE_URL: 'https://api.payvendy.name.ng'
    },
    env_development: {
      NODE_ENV: 'development',
      ADMIN_PORT: 3001,
      API_BASE_URL: 'http://localhost:8000'
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    
    // Advanced PM2 features
    min_uptime: '10s',
    max_restarts: 10,
    
    // Monitoring
    pmx: true,
    
    // Source map support
    source_map_support: true,
    
    // Environment variables
    env_vars: {
      'NODE_OPTIONS': '--max-old-space-size=1024'
    }
  }],

  deploy: {
    production: {
      user: 'root',
      host: 'payvendy.name.ng',
      ref: 'origin/main',
      repo: '**************:vendy/admin-dashboard.git',
      path: '/var/www/vendy-admin',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
