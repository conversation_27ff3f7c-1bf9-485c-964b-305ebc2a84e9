import React from 'react';
import { Platform, StyleSheet, View, ViewStyle } from 'react-native';
import { BlurView } from '@react-native-community/blur';
import { useTheme } from '../components/ThemeContext';

interface GlassyBoxProps {
  children?: React.ReactNode;
  style?: ViewStyle;
  intensity?: 'light' | 'medium' | 'strong';
  glow?: boolean;
}

const GlassyBox: React.FC<GlassyBoxProps> = ({ 
  children, 
  style, 
  intensity = 'medium',
  glow = false 
}) => {
  const { isDark } = useTheme();

  // Dynamic blur amounts based on intensity
  const blurAmounts = {
    light: 15,
    medium: 25,
    strong: 35
  };

  // Dynamic opacity based on intensity
  const getOpacity = () => {
    switch (intensity) {
      case 'light': return isDark ? 0.08 : 0.4;
      case 'medium': return isDark ? 0.12 : 0.5;
      case 'strong': return isDark ? 0.18 : 0.6;
      default: return isDark ? 0.12 : 0.5;
    }
  };

  // Dynamic border opacity
  const getBorderOpacity = () => {
    switch (intensity) {
      case 'light': return isDark ? 0.15 : 0.08;
      case 'medium': return isDark ? 0.25 : 0.12;
      case 'strong': return isDark ? 0.35 : 0.18;
      default: return isDark ? 0.25 : 0.12;
    }
  };

  if (Platform.OS === 'ios') {
    return (
      <BlurView
        style={[
          styles.container, 
          glow && styles.glowContainer,
          {
            backgroundColor: `rgba(255, 255, 255, ${getOpacity()})`,
            borderColor: `rgba(255, 255, 255, ${getBorderOpacity()})`,
          },
          style
        ]}
        blurType={isDark ? "dark" : "light"}
        blurAmount={blurAmounts[intensity]}
        reducedTransparencyFallbackColor={isDark ? "rgba(0, 0, 0, 0.8)" : "rgba(255, 255, 255, 0.8)"}
      >
        {children}
      </BlurView>
    );
  } else {
    return (
      <View style={[
        styles.container, 
        styles.androidFallback,
        glow && styles.glowContainer,
        { 
          backgroundColor: `rgba(${isDark ? '255, 255, 255' : '255, 255, 255'}, ${getOpacity()})`,
          borderColor: `rgba(${isDark ? '255, 255, 255' : '0, 0, 0'}, ${getBorderOpacity()})`,
        }, 
        style
      ]}>
        {children}
      </View>
    );
  }
};

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
    borderRadius: 12,
    borderWidth: 1,
    // Subtle shadow for depth
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 4,
  },
  androidFallback: {
    // Subtle Android shadows
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.12,
    shadowRadius: 8,
    elevation: 6,
    borderWidth: 1,
  },
  glowContainer: {
    // Subtle glow effect for balance card only
    shadowColor: '#ffffff',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 8,
  },
});

export default GlassyBox;
