import React, { useState, useCallback } from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  StyleSheet,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Svg, Path } from 'react-native-svg';
import GoogleAuthService from '../services/googleAuthService';
import logger from '../services/productionLogger';

interface GoogleSignInButtonProps {
  onSuccess?: (result: any) => void;
  onError?: (error: Error) => void;
  disabled?: boolean;
  style?: any;
  size?: number;
  text?: string;
}

const GoogleSignInButton: React.FC<GoogleSignInButtonProps> = ({
  onSuccess,
  onError,
  disabled = false,
  style,
  size = 48,
  text = "Sign in with Google",
}) => {
  const [loading, setLoading] = useState(false);

  const signInWithGoogle = useCallback(async () => {
    if (loading || disabled) return;

    try {
      setLoading(true);
      logger.userAction('google_signin_button_pressed', {
        component: 'GoogleSignInButton'
      });

      logger.info('Starting native Google Play Services dialog');

      // This triggers the native Google Play Services bottom sheet
      // Shows account picker and consent screen just like TikTok/X
      const result = await GoogleAuthService.signIn();

      logger.info('Google Sign-In completed successfully', {
        email: result.user?.email
      });
      onSuccess?.(result);
      
    } catch (error: any) {
      logger.error('Google Sign-In error', {
        message: error.message,
        code: error.code
      });
      
      let errorMessage = 'Sign in failed';
      
      // Handle specific error cases
      if (error.message === 'Sign in cancelled') {
        // Don't show error for user cancellation
        return;
      } else if (error.message === 'Google Play Services not available') {
        errorMessage = 'Google Play Services not available. Please update Google Play Services.';
      } else if (error.message === 'Sign in already in progress') {
        errorMessage = 'Sign in already in progress. Please wait.';
      } else if (error.code === 'auth/account-exists-with-different-credential') {
        errorMessage = 'An account already exists with this email using a different sign-in method';
      } else if (error.code === 'auth/network-request-failed') {
        errorMessage = 'Network error. Please check your connection and try again';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert('Sign In Error', errorMessage, [{ text: 'OK' }]);
      
      onError?.(error);
    } finally {
      setLoading(false);
    }
  }, [loading, disabled, onSuccess, onError]);

  // Google Logo SVG Component
  const GoogleLogo = () => {
    const logoSize = 20;
    return (
      <Svg width={logoSize} height={logoSize} viewBox="0 0 24 24">
        <Path
          fill="#4285F4"
          d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
        />
        <Path
          fill="#34A853"
          d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
        />
        <Path
          fill="#FBBC05"
          d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
        />
        <Path
          fill="#EA4335"
          d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
        />
      </Svg>
    );
  };

  return (
    <TouchableOpacity
      style={[
        styles.button,
        style,
        (disabled || loading) && styles.disabled
      ]}
      onPress={signInWithGoogle}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      <View style={styles.content}>
        {loading ? (
          <ActivityIndicator size="small" color="#666" style={styles.loader} />
        ) : (
          <GoogleLogo />
        )}
        <Text style={[styles.text, loading && styles.textLoading]}>
          {loading ? 'Signing in...' : text}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#dadce0',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  disabled: {
    opacity: 0.6,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    color: '#3c4043',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 12,
  },
  textLoading: {
    color: '#666',
  },
  loader: {
    marginRight: 8,
  },
});

export default GoogleSignInButton;
