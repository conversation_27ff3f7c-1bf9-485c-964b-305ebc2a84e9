# PayVendy Admin Dashboard - Complete Setup Guide

This guide will walk you through setting up the PayVendy Admin Dashboard with the existing backend.

## 🚀 Quick Start

### 1. Backend Setup (Required First)

Make sure your PayVendy backend is running:

```bash
# Navigate to backend directory
cd backend

# Install dependencies (if not already done)
npm install

# Start the backend server
npm start
```

The backend should be running on `http://localhost:8000`

### 2. Create Admin User

You need an admin user to access the dashboard. You can create one using the backend API or directly in the database.

#### Option A: Using Database (Recommended)

Connect to your Supabase database and run:

```sql
-- Update an existing user to admin role
UPDATE users 
SET role = 'admin', 
    is_active = true,
    is_email_verified = true
WHERE email = '<EMAIL>';

-- Or create a new admin user
INSERT INTO users (
    id,
    email,
    role,
    is_active,
    is_email_verified,
    pin,
    created_at,
    updated_at
) VALUES (
    gen_random_uuid(),
    '<EMAIL>',
    'admin',
    true,
    true,
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBdXig/8SBVeMO', -- bcrypt hash of '1234'
    NOW(),
    NOW()
);
```

#### Option B: Using Backend API

```bash
# First create a regular user via OTP
curl -X POST http://localhost:8000/api/v1/auth/send-otp \
  -H "Content-Type: application/json" \
  -d '{"phoneNumber": "+1234567890"}'

# Verify OTP and complete registration
# Then update the user role in database to 'admin'
```

### 3. Admin Dashboard Setup

```bash
# Navigate to admin dashboard directory
cd admin-web

# Install dependencies
npm install

# Start the development server
npm run dev
```

The admin dashboard will be available at `http://localhost:3001`

### 4. Login to Admin Dashboard

1. Open `http://localhost:3001` in your browser
2. You'll be redirected to the login page
3. Enter your admin email and PIN
4. Click "Sign in"

**Default Admin Credentials (if you used the SQL above):**
- Email: `<EMAIL>`
- PIN: `1234`

## 🔧 Configuration Details

### Backend Configuration

The backend is configured to run on port 8000 with the following key settings:

- **Port**: 8000
- **JWT Secret**: From `.env` file
- **Database**: Supabase
- **Admin Routes**: `/api/v1/admin/*`

### Admin Dashboard Configuration

The admin dashboard is configured with:

- **Port**: 3001
- **Backend URL**: `http://localhost:8000`
- **API Endpoints**: Matching backend routes
- **Authentication**: JWT-based with PIN login

### Environment Variables

The admin dashboard uses these environment variables (already configured in `.env.local`):

```env
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
BACKEND_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME=PayVendy Admin Dashboard
NEXT_PUBLIC_APP_VERSION=1.0.0
```

## 📊 Available Features

### Dashboard Overview
- System health monitoring
- Crash reports summary
- User statistics
- Recent activity feed

### Crash Reports Management
- View all crash reports
- Filter by platform, severity, status
- Resolve crash reports
- Detailed crash analysis
- Export functionality

### User Management
- View all users
- Search and filter users
- User profile management
- Activity monitoring

### Analytics
- Interactive charts and graphs
- Trend analysis
- Performance metrics

## 🔐 Security Features

### Authentication
- JWT token-based authentication
- PIN-based login for admins
- Automatic token refresh
- Session management

### Authorization
- Role-based access control
- Admin-only routes
- Protected API endpoints

### Security Headers
- Content Security Policy
- XSS Protection
- Frame Options
- CSRF Protection

## 🛠 Development

### Project Structure

```
admin-web/
├── src/
│   ├── app/                 # Next.js pages
│   ├── components/          # React components
│   ├── services/           # API services
│   ├── types/              # TypeScript types
│   └── config/             # Configuration
├── public/                 # Static assets
└── package.json           # Dependencies
```

### Key Technologies

- **Next.js 14**: React framework with App Router
- **TypeScript**: Type safety
- **Tailwind CSS**: Styling
- **Recharts**: Data visualization
- **React Hook Form**: Form handling
- **Zod**: Validation

### API Integration

The dashboard integrates with these backend endpoints:

- `POST /api/v1/admin/login` - Admin authentication
- `GET /api/v1/auth/verify-token` - Token validation
- `GET /api/v1/admin/crash-reports/dashboard` - Dashboard data
- `GET /api/v1/admin/crash-reports` - Crash reports list
- `POST /api/v1/admin/crash-reports/{id}/resolve` - Resolve crash

## 🚨 Troubleshooting

### Common Issues

1. **Backend not running**
   - Ensure backend is running on port 8000
   - Check backend logs for errors

2. **Login fails**
   - Verify admin user exists with correct role
   - Check PIN is correctly hashed in database
   - Ensure user is active and email verified

3. **API errors**
   - Check backend URL in `.env.local`
   - Verify backend routes are accessible
   - Check CORS configuration

4. **Dashboard not loading**
   - Ensure Node.js 18+ is installed
   - Check for dependency installation errors
   - Verify port 3001 is available

### Debug Mode

Enable debug mode by setting:

```env
NEXT_PUBLIC_DEBUG=true
```

This will show detailed API request/response logs in the browser console.

## 📝 Next Steps

1. **Customize the dashboard** - Modify components in `src/components/`
2. **Add new features** - Extend the API integration
3. **Configure production** - Set up production environment variables
4. **Deploy** - Deploy to your preferred hosting platform

## 🆘 Support

For issues or questions:

1. Check the backend API logs
2. Review browser console for errors
3. Verify database user permissions
4. Ensure all environment variables are set correctly

---

**PayVendy Admin Dashboard** - Production-ready administrative interface for the PayVendy platform.
