/**
 * Component prop interfaces and types for modals and state machine
 */

// Base types
export interface GoogleAccount {
  id: string;
  name: string;
  email: string;
  photo?: string;
  isCurrent?: boolean;
}

export interface AuthScope {
  id: string;
  name: string;
  description: string;
  required: boolean;
}

// State machine types
export type AuthFlowState = 
  | 'IDLE'
  | 'ACCOUNT_PICKER'
  | 'CONSENT'
  | 'PROCESSING'
  | 'DONE'
  | 'ERROR';

export interface AuthFlowError {
  code: string;
  message: string;
  recoverable?: boolean;
}

export interface AuthFlowResult {
  success: boolean;
  user?: GoogleAccount;
  scopes?: string[];
  error?: AuthFlowError;
}

// Modal prop interfaces

export interface ConsentModalProps {
  visible: boolean;
  onClose: () => void;
  onConsentResult: (accepted: boolean, scopes: string[]) => void;
  account?: GoogleAccount;
  scopes: AuthScope[];
  appName?: string;
  loading?: boolean;
}

// State machine button props
export interface AuthFlowButtonProps {
  // Appearance
  size?: number;
  style?: any;
  disabled?: boolean;
  appName?: string;
  mode?: 'signin' | 'signup' | 'link';
  
  // State management
  initialState?: AuthFlowState;
  
  // Flow callbacks
  onAccountChosen?: (account: GoogleAccount) => void;
  onConsentResult?: (accepted: boolean, scopes: string[]) => void;
  onFlowComplete?: (result: AuthFlowResult) => void;
  onFlowError?: (error: AuthFlowError) => void;
  onStateChange?: (state: AuthFlowState) => void;
  
  // Configuration
  scopes?: AuthScope[];
  accounts?: GoogleAccount[];
  autoAdvance?: boolean; // Whether to automatically advance through states
}

// State machine context
export interface AuthFlowContext {
  state: AuthFlowState;
  account?: GoogleAccount;
  scopes: string[];
  error?: AuthFlowError;
  loading: boolean;
}

// State machine actions
export type AuthFlowAction =
  | { type: 'START_FLOW' }
  | { type: 'SHOW_ACCOUNT_PICKER' }
  | { type: 'ACCOUNT_CHOSEN'; payload: GoogleAccount }
  | { type: 'SHOW_CONSENT'; payload: { account: GoogleAccount; scopes: AuthScope[] } }
  | { type: 'CONSENT_GIVEN'; payload: { accepted: boolean; scopes: string[] } }
  | { type: 'START_PROCESSING' }
  | { type: 'PROCESSING_COMPLETE'; payload: AuthFlowResult }
  | { type: 'ERROR_OCCURRED'; payload: AuthFlowError }
  | { type: 'RESET_FLOW' }
  | { type: 'RETRY_FLOW' };

// State transition configuration
export interface StateTransition {
  from: AuthFlowState;
  to: AuthFlowState;
  condition?: (context: AuthFlowContext) => boolean;
  action?: (context: AuthFlowContext) => void;
}

// Validation types
export interface ValidationResult {
  valid: boolean;
  errors?: string[];
}

export interface AccountValidation extends ValidationResult {
  account?: GoogleAccount;
}

export interface ConsentValidation extends ValidationResult {
  missingScopes?: string[];
}
