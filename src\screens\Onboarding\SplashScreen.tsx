import React, { useEffect } from "react"
import { View, StyleSheet, StatusBar, SafeAreaView, Image } from "react-native"
import type { NativeStackScreenProps } from "@react-navigation/native-stack"
import type { RootStackParamList } from "../../types/navigation"
import { useTheme } from "../../components/ThemeContext"
import logger from '../../services/productionLogger'
import authSessionService from '../../services/authSessionService'

type Props = NativeStackScreenProps<RootStackParamList, "Splash">

const SplashScreen = ({ navigation }: Props) => {
  const { isDark } = useTheme()

  useEffect(() => {
    const checkAuthAndNavigate = async () => {
      try {
        const session = await authSessionService.checkExistingSession();

        if (session.isAuthenticated) {
          const setupStatus = session.setupStatus;
          const userInfo = session.user;
          const hasName = userInfo?.firstName && userInfo.firstName.trim() !== '';
          const hasProfileSetup = setupStatus?.hasProfileSetup || hasName;

          if (!setupStatus) {
            navigation.replace('Startup');
          } else if (!hasProfileSetup) {
            navigation.replace('NameSetup', { userData: userInfo });
          } else if (!setupStatus.hasPinSetup) {
            navigation.replace('PinSetup', { userData: userInfo });
          } else {
            navigation.replace('PinVerification', { user: userInfo });
          }
        } else {
          navigation.replace('Startup');
        }
      } catch (error) {
        logger.error('Auth check failed', error, 'splash');
        navigation.replace('Startup');
      }
    }

    const timer = setTimeout(checkAuthAndNavigate, 2000);
    return () => clearTimeout(timer);
  }, [navigation])

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar
        barStyle="light-content"
        backgroundColor="#000000"
        translucent={false}
      />

      <View style={styles.content}>
        <Image
          source={require("../../../assets/icons/vendy.png")}
          style={styles.logo}
          resizeMode="contain"
        />
      </View>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    width: 120,
    height: 120,
  },
})

export default SplashScreen
