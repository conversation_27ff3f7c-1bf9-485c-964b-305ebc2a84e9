import { useCallback, useMemo, useRef, useEffect, useState } from 'react'
import { Animated, InteractionManager } from 'react-native'
import logger from '../services/productionLogger'

// Debounce hook for performance
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// Throttle hook for performance
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T => {
  const lastRun = useRef(Date.now())

  return useCallback(
    ((...args) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args)
        lastRun.current = Date.now()
      }
    }) as T,
    [callback, delay]
  )
}

// Optimized animation hook
export const useOptimizedAnimation = (
  initialValue: number = 0,
  config?: {
    useNativeDriver?: boolean
    duration?: number
  }
) => {
  const animatedValue = useRef(new Animated.Value(initialValue)).current
  const isAnimating = useRef(false)

  const animate = useCallback(
    (toValue: number, durationOrCallback?: number | (() => void), callback?: () => void) => {
      if (isAnimating.current) return

      // Handle overloaded parameters
      let duration = config?.duration || 300;
      let onComplete = callback;

      if (typeof durationOrCallback === 'number') {
        duration = durationOrCallback;
      } else if (typeof durationOrCallback === 'function') {
        onComplete = durationOrCallback;
      }

      isAnimating.current = true;
      
      Animated.timing(animatedValue, {
        toValue,
        duration: duration,
        useNativeDriver: config?.useNativeDriver ?? true,
      }).start(() => {
        isAnimating.current = false
        onComplete?.()
      })
    },
    [animatedValue, config]
  )

  const reset = useCallback(() => {
    animatedValue.setValue(initialValue)
    isAnimating.current = false
  }, [animatedValue, initialValue])

  return { animatedValue, animate, reset, isAnimating: isAnimating.current }
}

// Memory-efficient typing animation
export const useTypingAnimation = (
  text: string,
  options: {
    typingSpeed?: number
    deletingSpeed?: number
    pauseDuration?: number
    enabled?: boolean
  } = {}
) => {
  const {
    typingSpeed = 100,
    deletingSpeed = 50,
    pauseDuration = 2000,
    enabled = true
  } = options

  const [displayText, setDisplayText] = useState('')
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined)
  const indexRef = useRef(0)
  const isDeletingRef = useRef(false)
  const isPausedRef = useRef(false)

  const cleanup = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
  }, [])

  const typeText = useCallback(() => {
    if (!enabled) return

    if (!isDeletingRef.current && !isPausedRef.current) {
      // Typing forward
      if (indexRef.current < text.length) {
        setDisplayText(text.substring(0, indexRef.current + 1))
        indexRef.current++
        timeoutRef.current = setTimeout(typeText, typingSpeed)
      } else {
        // Finished typing, pause before deleting
        isPausedRef.current = true
        timeoutRef.current = setTimeout(() => {
          isPausedRef.current = false
          isDeletingRef.current = true
          typeText()
        }, pauseDuration)
      }
    } else if (isDeletingRef.current && !isPausedRef.current) {
      // Deleting backward
      if (indexRef.current > 0) {
        setDisplayText(text.substring(0, indexRef.current - 1))
        indexRef.current--
        timeoutRef.current = setTimeout(typeText, deletingSpeed)
      } else {
        // Finished deleting, start typing again
        isDeletingRef.current = false
        timeoutRef.current = setTimeout(typeText, typingSpeed)
      }
    }
  }, [text, typingSpeed, deletingSpeed, pauseDuration, enabled])

  useEffect(() => {
    if (enabled) {
      // Start after interaction is complete for better performance
      InteractionManager.runAfterInteractions(() => {
        timeoutRef.current = setTimeout(typeText, 1000)
      })
    } else {
      setDisplayText(text)
    }

    return cleanup
  }, [text, enabled, typeText, cleanup])

  useEffect(() => {
    return cleanup
  }, [cleanup])

  return displayText
}

// Optimized wave animation (reduce segments based on device performance)
export const useOptimizedWaveConfig = () => {
  return useMemo(() => {
    // Reduce complexity on lower-end devices
    const isLowEndDevice = false // Could be determined by device specs
    
    return {
      segments: isLowEndDevice ? 30 : 80,
      amplitude: 15,
      frequency: 1.5,
      waveWidth: isLowEndDevice ? 12 : 8,
      overlap: isLowEndDevice ? -2 : -4,
    }
  }, [])
}

// Performance monitoring
export const usePerformanceMonitor = (componentName: string) => {
  const renderCount = useRef(0)
  const startTime = useRef(Date.now())

  useEffect(() => {
    renderCount.current++
    
if (__DEV__) {
      logger.info(`${componentName} rendered ${renderCount.current} times`)
      
      if (renderCount.current > 10) {
        logger.warn(`${componentName} is re-rendering frequently (${renderCount.current} times)`)
      }
    }
  })

  useEffect(() => {
    const mountTime = Date.now() - startTime.current
    
if (__DEV__ && mountTime > 100) {
      logger.warn(`${componentName} took ${mountTime}ms to mount`)
    }
  }, [componentName])
}

