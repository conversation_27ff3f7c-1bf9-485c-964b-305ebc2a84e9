const express = require('express');
const authService = require('../../services/authService');
const logger = require('../../utils/logger');
const { getSupabase } = require('../../config/database');

const router = express.Router();

/**
 * @route   GET /api/v1/admin/crash-reports/dashboard
 * @desc    Get crash reports dashboard data
 * @access  Private (Admin only)
 */
router.get('/dashboard',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const supabase = getSupabase();
      
      // Get dashboard statistics
      const [
        totalReports,
        recentReports,
        criticalReports,
        platformStats,
        versionStats,
        topErrors
      ] = await Promise.all([
        // Total reports count
        supabase
          .from('crash_reports')
          .select('id', { count: 'exact', head: true }),
        
        // Recent reports (last 24 hours)
        supabase
          .from('crash_reports')
          .select('id', { count: 'exact', head: true })
          .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()),
        
        // Critical reports (unresolved fatal/error)
        supabase
          .from('crash_reports')
          .select('id', { count: 'exact', head: true })
          .in('severity', ['fatal', 'error'])
          .eq('resolved', false),
        
        // Platform breakdown
        supabase
          .from('crash_reports')
          .select('platform')
          .then(({ data }) => {
            const stats = { ios: 0, android: 0 };
            data?.forEach(report => {
              if (report.platform in stats) {
                stats[report.platform]++;
              }
            });
            return stats;
          }),
        
        // Version breakdown (last 7 days)
        supabase
          .from('crash_reports')
          .select('app_version')
          .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
          .then(({ data }) => {
            const versionCounts = {};
            data?.forEach(report => {
              versionCounts[report.app_version] = (versionCounts[report.app_version] || 0) + 1;
            });
            return Object.entries(versionCounts)
              .sort(([,a], [,b]) => b - a)
              .slice(0, 5)
              .map(([version, count]) => ({ version, count }));
          }),
        
        // Top error messages
        supabase
          .from('crash_reports')
          .select('message')
          .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
          .then(({ data }) => {
            const errorCounts = {};
            data?.forEach(report => {
              const shortMessage = report.message.substring(0, 100);
              errorCounts[shortMessage] = (errorCounts[shortMessage] || 0) + 1;
            });
            return Object.entries(errorCounts)
              .sort(([,a], [,b]) => b - a)
              .slice(0, 10)
              .map(([message, count]) => ({ message, count }));
          })
      ]);

      // Get trend data (last 7 days)
      const trendData = await supabase
        .from('crash_reports')
        .select('created_at, severity')
        .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: true });

      // Process trend data by day
      const dailyTrends = {};
      trendData.data?.forEach(report => {
        const day = new Date(report.created_at).toISOString().split('T')[0];
        if (!dailyTrends[day]) {
          dailyTrends[day] = { total: 0, fatal: 0, error: 0, warning: 0, info: 0 };
        }
        dailyTrends[day].total++;
        dailyTrends[day][report.severity]++;
      });

      const dashboardData = {
        summary: {
          totalReports: totalReports.count || 0,
          recentReports: recentReports.count || 0,
          criticalReports: criticalReports.count || 0,
          resolutionRate: totalReports.count > 0 
            ? Math.round(((totalReports.count - criticalReports.count) / totalReports.count) * 100)
            : 100
        },
        platformStats,
        versionStats,
        topErrors,
        trends: Object.entries(dailyTrends).map(([date, stats]) => ({
          date,
          ...stats
        })).sort((a, b) => new Date(a.date) - new Date(b.date))
      };

      res.status(200).json({
        status: 'success',
        data: dashboardData
      });

    } catch (error) {
      logger.error('Dashboard data fetch error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to fetch dashboard data'
      });
    }
  }
);

/**
 * @route   GET /api/v1/admin/crash-reports/stats
 * @desc    Get detailed crash report statistics
 * @access  Private (Admin only)
 */
router.get('/stats',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const { timeframe = '7d' } = req.query;
      
      // Calculate date range
      let startDate;
      switch (timeframe) {
        case '1d':
          startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90d':
          startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      }

      const supabase = getSupabase();
      
      // Get comprehensive statistics
      const { data: stats, error } = await supabase
        .from('crash_reports')
        .select(`
          id,
          platform,
          app_version,
          severity,
          resolved,
          created_at,
          device_info,
          user_id
        `)
        .gte('created_at', startDate.toISOString());

      if (error) {
        throw error;
      }

      // Process statistics
      const processedStats = {
        totalReports: stats.length,
        byPlatform: {},
        byVersion: {},
        bySeverity: {},
        byResolution: { resolved: 0, unresolved: 0 },
        uniqueUsers: new Set(),
        deviceModels: {},
        hourlyDistribution: Array(24).fill(0),
        dailyTrend: {}
      };

      stats.forEach(report => {
        // Platform stats
        processedStats.byPlatform[report.platform] = 
          (processedStats.byPlatform[report.platform] || 0) + 1;

        // Version stats
        processedStats.byVersion[report.app_version] = 
          (processedStats.byVersion[report.app_version] || 0) + 1;

        // Severity stats
        processedStats.bySeverity[report.severity] = 
          (processedStats.bySeverity[report.severity] || 0) + 1;

        // Resolution stats
        if (report.resolved) {
          processedStats.byResolution.resolved++;
        } else {
          processedStats.byResolution.unresolved++;
        }

        // Unique users
        if (report.user_id) {
          processedStats.uniqueUsers.add(report.user_id);
        }

        // Device models
        if (report.device_info?.model) {
          processedStats.deviceModels[report.device_info.model] = 
            (processedStats.deviceModels[report.device_info.model] || 0) + 1;
        }

        // Hourly distribution
        const hour = new Date(report.created_at).getHours();
        processedStats.hourlyDistribution[hour]++;

        // Daily trend
        const day = new Date(report.created_at).toISOString().split('T')[0];
        processedStats.dailyTrend[day] = (processedStats.dailyTrend[day] || 0) + 1;
      });

      // Convert unique users set to count
      processedStats.uniqueUsers = processedStats.uniqueUsers.size;

      // Sort and limit some stats
      processedStats.topVersions = Object.entries(processedStats.byVersion)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([version, count]) => ({ version, count }));

      processedStats.topDevices = Object.entries(processedStats.deviceModels)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([model, count]) => ({ model, count }));

      res.status(200).json({
        status: 'success',
        data: {
          timeframe,
          startDate: startDate.toISOString(),
          endDate: new Date().toISOString(),
          stats: processedStats
        }
      });

    } catch (error) {
      logger.error('Stats fetch error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to fetch statistics'
      });
    }
  }
);

/**
 * @route   POST /api/v1/admin/crash-reports/:id/resolve
 * @desc    Mark crash report as resolved
 * @access  Private (Admin only)
 */
router.post('/:id/resolve',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const { id } = req.params;
      const { resolution_notes } = req.body;
      const adminId = req.user.id;

      const supabase = getSupabase();
      const { data, error } = await supabase
        .from('crash_reports')
        .update({
          resolved: true,
          resolved_by: adminId,
          resolved_at: new Date().toISOString(),
          resolution_notes: resolution_notes || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();

      if (error) {
        throw error;
      }

      logger.info(`Crash report ${id} resolved by admin ${adminId}`, {
        reportId: id,
        adminId,
        hasNotes: !!resolution_notes
      });

      res.status(200).json({
        status: 'success',
        message: 'Crash report marked as resolved',
        data
      });

    } catch (error) {
      logger.error('Resolve crash report error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to resolve crash report'
      });
    }
  }
);

/**
 * @route   POST /api/v1/admin/crash-reports/bulk-resolve
 * @desc    Bulk resolve crash reports
 * @access  Private (Admin only)
 */
router.post('/bulk-resolve',
  authService.protect,
  authService.restrictTo('admin'),
  async (req, res) => {
    try {
      const { report_ids, resolution_notes } = req.body;
      const adminId = req.user.id;

      if (!Array.isArray(report_ids) || report_ids.length === 0) {
        return res.status(400).json({
          status: 'error',
          message: 'report_ids must be a non-empty array'
        });
      }

      const supabase = getSupabase();
      const { data, error } = await supabase
        .from('crash_reports')
        .update({
          resolved: true,
          resolved_by: adminId,
          resolved_at: new Date().toISOString(),
          resolution_notes: resolution_notes || null,
          updated_at: new Date().toISOString()
        })
        .in('id', report_ids)
        .select('id');

      if (error) {
        throw error;
      }

      logger.info(`Bulk resolved ${data.length} crash reports by admin ${adminId}`, {
        resolvedCount: data.length,
        requestedCount: report_ids.length,
        adminId
      });

      res.status(200).json({
        status: 'success',
        message: `${data.length} crash reports marked as resolved`,
        data: {
          resolvedCount: data.length,
          resolvedIds: data.map(r => r.id)
        }
      });

    } catch (error) {
      logger.error('Bulk resolve crash reports error:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to bulk resolve crash reports'
      });
    }
  }
);

module.exports = router;
