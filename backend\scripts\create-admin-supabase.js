#!/usr/bin/env node

/**
 * Create Admin User via Supabase API
 */

const axios = require('axios');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

async function createAdminUser() {
  const email = '<EMAIL>';
  const pin = '123456';
  const phoneNumber = '09012345678';
  
  try {
    console.log('🔐 Creating admin user...');
    
    // Hash the PIN
    const hashedPin = await bcrypt.hash(pin, 12);
    const userId = uuidv4();
    const now = new Date().toISOString();
    
    // Prepare user data
    const userData = {
      id: userId,
      email: email,
      phone_number: phoneNumber,
      pin: hashedPin,
      role: 'admin',
      is_active: true,
      is_phone_verified: true,
      is_email_verified: true,
      first_name: 'Admin',
      last_name: 'User',
      balance: 0,
      created_at: now,
      updated_at: now
    };
    
    // Insert via Supabase REST API
    const response = await axios.post(
      `${process.env.SUPABASE_URL}/rest/v1/users`,
      userData,
      {
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY,
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=representation'
        }
      }
    );
    
    if (response.status === 201) {
      console.log('✅ Admin user created successfully!');
      console.log(`📧 Email: ${email}`);
      console.log(`📱 Phone: ${phoneNumber}`);
      console.log(`🆔 ID: ${userId}`);
      console.log('');
      console.log('🔑 Login Credentials:');
      console.log(`   Dashboard: https://admin.payvendy.name.ng`);
      console.log(`   Email: ${email}`);
      console.log(`   PIN: ${pin}`);
      console.log('');
      console.log('📝 Next Steps:');
      console.log('1. Visit https://admin.payvendy.name.ng');
      console.log('2. Login with the credentials above');
      console.log('3. Start managing your OTA updates!');
    } else {
      console.log('❌ Unexpected response:', response.status);
    }
    
  } catch (error) {
    if (error.response) {
      console.log('❌ Error creating admin user:', error.response.data);
      
      // Check if user already exists
      if (error.response.status === 409 || 
          (error.response.data && error.response.data.message && 
           error.response.data.message.includes('duplicate'))) {
        console.log('');
        console.log('ℹ️  Admin user might already exist. Try logging in with:');
        console.log(`   Email: ${email}`);
        console.log(`   PIN: ${pin}`);
      }
    } else {
      console.log('❌ Error:', error.message);
    }
  }
}

async function checkExistingAdmin() {
  try {
    console.log('🔍 Checking for existing admin users...');
    
    const response = await axios.get(
      `${process.env.SUPABASE_URL}/rest/v1/users?role=eq.admin&select=id,email,phone_number,is_active,created_at`,
      {
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY,
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`
        }
      }
    );
    
    const adminUsers = response.data;
    
    if (adminUsers.length === 0) {
      console.log('❌ No admin users found.');
      console.log('🔧 Creating admin user...');
      await createAdminUser();
    } else {
      console.log(`✅ Found ${adminUsers.length} admin user(s):`);
      adminUsers.forEach((user, index) => {
        console.log(`${index + 1}. Email: ${user.email}`);
        console.log(`   Phone: ${user.phone_number}`);
        console.log(`   Active: ${user.is_active}`);
        console.log(`   Created: ${new Date(user.created_at).toLocaleString()}`);
        console.log('');
      });
      
      console.log('🔑 Login Instructions:');
      console.log('   Dashboard: https://admin.payvendy.name.ng');
      console.log('   Use your email and PIN to login');
      console.log('');
      console.log('💡 If you forgot your PIN, you can reset it by running:');
      console.log('   node scripts/create-admin-supabase.js reset-pin <email> <new-pin>');
    }
    
  } catch (error) {
    console.log('❌ Error checking admin users:', error.response?.data || error.message);
    console.log('🔧 Attempting to create admin user anyway...');
    await createAdminUser();
  }
}

async function resetPin(email, newPin) {
  try {
    console.log(`🔐 Resetting PIN for ${email}...`);
    
    // Validate PIN
    if (!/^\d{4,6}$/.test(newPin)) {
      console.log('❌ PIN must be 4-6 digits');
      return;
    }
    
    // Hash new PIN
    const hashedPin = await bcrypt.hash(newPin, 12);
    
    // Update PIN
    const response = await axios.patch(
      `${process.env.SUPABASE_URL}/rest/v1/users?email=eq.${email}&role=eq.admin`,
      {
        pin: hashedPin,
        updated_at: new Date().toISOString()
      },
      {
        headers: {
          'apikey': process.env.SUPABASE_SERVICE_ROLE_KEY,
          'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (response.status === 204) {
      console.log('✅ PIN updated successfully!');
      console.log(`📧 Email: ${email}`);
      console.log(`🔐 New PIN: ${newPin}`);
    } else {
      console.log('❌ Failed to update PIN');
    }
    
  } catch (error) {
    console.log('❌ Error updating PIN:', error.response?.data || error.message);
  }
}

// Command line interface
const args = process.argv.slice(2);
const command = args[0];

if (command === 'create') {
  createAdminUser();
} else if (command === 'reset-pin') {
  const email = args[1];
  const newPin = args[2];
  
  if (!email || !newPin) {
    console.log('Usage: node scripts/create-admin-supabase.js reset-pin <email> <new-pin>');
    console.log('Example: node scripts/create-admin-supabase.js reset-pin <EMAIL> 654321');
    process.exit(1);
  }
  
  resetPin(email, newPin);
} else {
  checkExistingAdmin();
}
