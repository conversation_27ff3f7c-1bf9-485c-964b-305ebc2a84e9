import { Platform } from 'react-native';
import logger from './productionLogger';

/**
 * Native SSL Pinning Service
 * Production-ready SSL certificate pinning using native modules
 */

interface NativeSSLPinningConfig {
  hostname: string;
  hashes: string[];
  includeSubdomains?: boolean;
}

class NativeSSLPinningService {
  private isInitialized = false;
  private sslPinning: any = null;

  constructor() {
    this.initializeNativeModule();
  }

  /**
   * Initialize the native SSL pinning module
   */
  private async initializeNativeModule() {
    try {
      // Import the native SSL pinning module
      const { SSLPinning } = require('react-native-ssl-pinning');
      this.sslPinning = SSLPinning;
      this.isInitialized = true;

      logger.info('Native SSL pinning module initialized', {
        service: 'NativeSSLPinningService',
        action: 'initializeNativeModule',
        platform: Platform.OS,
        available: true,
      });
    } catch (error) {
      logger.warn('Native SSL pinning module not available, using fallback', {
        service: 'NativeSSLPinningService',
        action: 'initializeNativeModule',
        error: error instanceof Error ? error.message : String(error),
        platform: Platform.OS,
      });
      this.isInitialized = false;
    }
  }

  /**
   * Configure SSL pinning for production domains
   */
  async configurePinning(): Promise<boolean> {
    if (!this.isInitialized || !this.sslPinning) {
      logger.warn('Native SSL pinning not available', {
        service: 'NativeSSLPinningService',
        action: 'configurePinning',
      });
      return false;
    }

    try {
      const pinningConfig: NativeSSLPinningConfig[] = [
        {
          hostname: 'funny-poems-slide.loca.lt',
          hashes: [
            'IjUc/BEPc+Jj43d+KZ+l1tyAuXfBaYeIvLuvQDaa2nY=', // Verified localtunnel cert
            'jQJTbIh0grw0/1TkHSumWb+Fs0Ggogr621gT3PvPKG0=', // Let's Encrypt R3
            'sRHdihwgkaib1P1gxX8HFszlD+7/gTfNvuAybgLPNis=', // Let's Encrypt root backup
          ],
        },
        {
          hostname: 'api.vendy.com',
          hashes: [
            // Comprehensive coverage for all major hosting providers
            'C5+lpZ7tcVwmwQIMcRtPbsQtWLABXhQzejna0wHFr8M=', // Let's Encrypt ISRG Root X1
            'jQJTbIh0grw0/1TkHSumWb+Fs0Ggogr621gT3PvPKG0=', // Let's Encrypt R3
            'i7WTqTvh0OioIruIfFR4kMPnBqrS2rdiVPl/s2uC/CY=', // DigiCert Global Root G2
            'VhQGmi/XwBuHqznoFHFHUGONNXBPkINm4fZfQTvTRHU=', // DigiCert TLS RSA SHA256 2020 CA1
            'gkehGfSbEgVOjDfzMvqt6X7F+MyVWx/1VihuqVvpW5s=', // Cloudflare Inc ECC CA-3
            'VjLZe/p3W/PJnd6lL8JVNBCGQBZynFLdZSTIqcO0SJ8=', // Amazon Root CA 1
            'h6801m+z8v3zbgkRHpq6L29Esgfzhj89C1SyUCOQmqU=', // GTS Root R1 (Google)
          ],
          includeSubdomains: true,
        },
        {
          hostname: 'staging-api.vendy.com',
          hashes: [
            'C5+lpZ7tcVwmwQIMcRtPbsQtWLABXhQzejna0wHFr8M=', // Let's Encrypt ISRG Root X1
            'jQJTbIh0grw0/1TkHSumWb+Fs0Ggogr621gT3PvPKG0=', // Let's Encrypt R3
            'i7WTqTvh0OioIruIfFR4kMPnBqrS2rdiVPl/s2uC/CY=', // DigiCert Global Root G2
            'gkehGfSbEgVOjDfzMvqt6X7F+MyVWx/1VihuqVvpW5s=', // Cloudflare Universal SSL
          ],
          includeSubdomains: true,
        },
        {
          hostname: 'supabase.co',
          hashes: [
            'gkehGfSbEgVOjDfzMvqt6X7F+MyVWx/1VihuqVvpW5s=', // Cloudflare Inc ECC CA-3
            'i7WTqTvh0OioIruIfFR4kMPnBqrS2rdiVPl/s2uC/CY=', // DigiCert Global Root G2
            'C5+lpZ7tcVwmwQIMcRtPbsQtWLABXhQzejna0wHFr8M=', // Let's Encrypt ISRG Root X1
            'VhQGmi/XwBuHqznoFHFHUGONNXBPkINm4fZfQTvTRHU=', // DigiCert TLS RSA SHA256 2020 CA1
          ],
          includeSubdomains: true,
        },
      ];

      // Configure pinning for each domain
      for (const config of pinningConfig) {
        await this.sslPinning.pin({
          hostname: config.hostname,
          hashes: config.hashes,
          includeSubdomains: config.includeSubdomains || false,
        });

        logger.info('SSL pinning configured for domain', {
          service: 'NativeSSLPinningService',
          action: 'configurePinning',
          hostname: config.hostname,
          hashCount: config.hashes.length,
          includeSubdomains: config.includeSubdomains,
        });
      }

      logger.info('Native SSL pinning configuration complete', {
        service: 'NativeSSLPinningService',
        action: 'configurePinning',
        domainsConfigured: pinningConfig.length,
        totalHashes: pinningConfig.reduce((sum, config) => sum + config.hashes.length, 0),
      });

      return true;
    } catch (error) {
      logger.error('Failed to configure native SSL pinning', {
        service: 'NativeSSLPinningService',
        action: 'configurePinning',
        error: error instanceof Error ? error.message : String(error),
      });
      return false;
    }
  }

  /**
   * Make a secure request with SSL pinning validation
   */
  async secureRequest(url: string, options: RequestInit = {}): Promise<Response> {
    if (!this.isInitialized || !this.sslPinning) {
      // Fallback to regular fetch if native pinning not available
      logger.debug('Using fallback fetch (native SSL pinning not available)', {
        service: 'NativeSSLPinningService',
        action: 'secureRequest',
        url,
      });
      return fetch(url, options);
    }

    try {
      // Use native SSL pinning for the request
      const response = await this.sslPinning.fetch(url, {
        ...options,
        sslPinning: {
          certs: [], // Certificates are already pinned via configuration
        },
      });

      logger.debug('Secure request completed with SSL pinning', {
        service: 'NativeSSLPinningService',
        action: 'secureRequest',
        url,
        status: response.status,
      });

      return response;
    } catch (error) {
      logger.error('Secure request failed - possible SSL pinning violation', {
        service: 'NativeSSLPinningService',
        action: 'secureRequest',
        url,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Check if native SSL pinning is available and configured
   */
  isAvailable(): boolean {
    return this.isInitialized && this.sslPinning !== null;
  }

  /**
   * Get SSL pinning status
   */
  getStatus() {
    return {
      available: this.isAvailable(),
      initialized: this.isInitialized,
      platform: Platform.OS,
      module: this.sslPinning ? 'react-native-ssl-pinning' : 'none',
    };
  }

  /**
   * Clear all SSL pinning configurations
   */
  async clearPinning(): Promise<boolean> {
    if (!this.isInitialized || !this.sslPinning) {
      return false;
    }

    try {
      await this.sslPinning.clearPins();
      logger.info('SSL pinning configurations cleared', {
        service: 'NativeSSLPinningService',
        action: 'clearPinning',
      });
      return true;
    } catch (error) {
      logger.error('Failed to clear SSL pinning configurations', {
        service: 'NativeSSLPinningService',
        action: 'clearPinning',
        error: error instanceof Error ? error.message : String(error),
      });
      return false;
    }
  }
}

export const nativeSSLPinningService = new NativeSSLPinningService();
export default nativeSSLPinningService;
