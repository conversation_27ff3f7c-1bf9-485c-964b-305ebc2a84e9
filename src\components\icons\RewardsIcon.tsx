import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface RewardsIconProps {
  size?: number;
  color?: string;
}

const RewardsIcon: React.FC<RewardsIconProps> = ({ size = 28, color = '#000' }) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      {/* 2D Diamond */}
      <Path d="M12 4L18 10L12 16L6 10L12 4Z" stroke={color} strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
    </Svg>
  );
};

export default RewardsIcon;
