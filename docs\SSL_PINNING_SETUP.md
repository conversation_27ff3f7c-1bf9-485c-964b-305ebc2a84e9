# SSL Certificate Pinning Setup Guide

## Overview

SSL Certificate Pinning is implemented to prevent man-in-the-middle attacks by validating that the server's certificate matches expected certificate hashes. This is crucial for a fintech application handling sensitive financial data.

## Current Implementation

### 1. Configuration Files

- **Environment Config**: `src/config/environment.ts`
- **SSL Pinning Service**: `src/services/sslPinningService.ts`
- **API Service Integration**: `src/services/apiService.ts`

### 2. Certificate Pinning Domains

Currently configured domains:
- **Development**: `funny-poems-slide.loca.lt` (localtunnel with SSL pinning enabled)
- **Production**: `api.vendy.com`, `supabase.co`
- **Staging**: `staging-api.vendy.com`, `supabase.co`

### 3. Current Certificate Hashes

**Localtunnel Domain (Development)**:
- Domain: `funny-poems-slide.loca.lt`
- Certificate Hash: `IjUc/BEPc+Jj43d+KZ+l1tyAuXfBaYeIvLuvQDaa2nY=`
- Issuer: Let's Encrypt E5
- Valid Until: Aug 18, 2025
- **⚠️ Important**: This certificate will auto-renew, update hash when it changes!

## Getting Real Certificate Hashes

### Method 1: Using the Provided Script (Recommended)

```bash
# Run the certificate hash generator script
node scripts/get-ssl-certificates.js
```

This script will automatically:
- Connect to all configured domains
- Extract certificate information
- Generate the correct public key hashes for pinning
- Show certificate expiration dates

### Method 2: Using OpenSSL (Manual)

```bash
# Get certificate from your localtunnel domain
echo | openssl s_client -servername funny-poems-slide.loca.lt -connect funny-poems-slide.loca.lt:443 2>/dev/null | openssl x509 -pubkey -noout | openssl rsa -pubin -outform der 2>/dev/null | openssl dgst -sha256 -binary | openssl enc -base64

# For production domains (when available)
echo | openssl s_client -servername api.vendy.com -connect api.vendy.com:443 2>/dev/null | openssl x509 -pubkey -noout | openssl rsa -pubin -outform der 2>/dev/null | openssl dgst -sha256 -binary | openssl enc -base64
```

### Method 2: Using Browser Developer Tools

1. Open Chrome/Firefox Developer Tools
2. Go to Security tab
3. Click "View Certificate"
4. Copy the certificate details
5. Use online tools to convert to SHA256 hash

### Method 3: Using curl

```bash
# Get certificate info
curl -vvI https://api.vendy.com 2>&1 | grep -A 10 "Server certificate"
```

## Updating Certificate Hashes

### 1. Update Environment Configuration

Edit `src/config/environment.ts`:

```typescript
certificateHashes: {
  'api.vendy.com': [
    'YOUR_ACTUAL_PRIMARY_CERT_HASH_HERE',
    'YOUR_ACTUAL_INTERMEDIATE_CA_HASH_HERE',
  ],
  'staging-api.vendy.com': [
    'YOUR_STAGING_CERT_HASH_HERE',
  ],
  'supabase.co': [
    'SUPABASE_CERT_HASH_1_HERE',
    'SUPABASE_CERT_HASH_2_HERE',
  ],
},
```

### 2. Update SSL Pinning Service

Edit `src/services/sslPinningService.ts` pins array with real hashes.

## Production Deployment Checklist

### Before Deployment:

- [ ] Replace all placeholder certificate hashes with real ones
- [ ] Test SSL pinning with staging environment
- [ ] Verify backup certificate hashes are configured
- [ ] Test certificate rotation scenario
- [ ] Ensure monitoring is in place for SSL failures

### Certificate Rotation Process:

1. **Before Certificate Expires:**
   - Get new certificate hashes
   - Add new hashes to backup pins
   - Deploy app update with both old and new hashes

2. **After Certificate Rotation:**
   - Remove old certificate hashes
   - Move backup hashes to primary
   - Add new backup hashes for next rotation

## Native Implementation (Recommended for Production)

For true SSL pinning security, consider using native modules:

### Android (react-native-ssl-pinning)

```bash
npm install react-native-ssl-pinning
```

### iOS (TrustKit)

Add TrustKit to iOS project for hardware-level certificate validation.

## Testing SSL Pinning

### 1. Development Testing

```typescript
// In development, SSL pinning is disabled
// Test with staging environment that has SSL pinning enabled
```

### 2. Staging Testing

```bash
# Test with correct certificate
curl -H "Content-Type: application/json" https://staging-api.vendy.com/api/v1/health

# Test with invalid certificate (should fail)
# Use proxy tools like Charles Proxy or mitmproxy
```

### 3. Production Testing

- Monitor SSL pinning failures in production logs
- Set up alerts for certificate validation failures
- Test certificate rotation process

## Monitoring and Alerts

### Key Metrics to Monitor:

1. **SSL Pinning Failures**: Track failed certificate validations
2. **Certificate Expiry**: Monitor certificate expiration dates
3. **Network Errors**: Distinguish SSL failures from network issues

### Logging:

All SSL pinning events are logged with:
- Service: `SSLPinningService`
- Action: `validateRequest`
- Hostname and validation result

## Security Considerations

### 1. Certificate Backup Strategy

- Always maintain backup certificate hashes
- Plan for emergency certificate rotation
- Test fallback mechanisms

### 2. Attack Scenarios

- **Certificate Pinning Bypass**: Use native modules for hardware validation
- **Certificate Substitution**: Pin both leaf and intermediate certificates
- **Downgrade Attacks**: Ensure HTTPS-only communication

### 3. Compliance

- Ensure SSL pinning meets regulatory requirements
- Document security measures for audits
- Regular security assessments

## Troubleshooting

### Common Issues:

1. **Certificate Mismatch**: Update certificate hashes
2. **Network Failures**: Check if SSL pinning is causing legitimate failures
3. **Development Issues**: Ensure SSL pinning is disabled for localhost

### Debug Commands:

```bash
# Check certificate details
openssl s_client -connect api.vendy.com:443 -showcerts

# Verify certificate chain
openssl verify -CAfile ca-bundle.crt certificate.crt
```

## Next Steps

1. **Get Real Certificate Hashes**: Replace placeholder hashes with actual ones
2. **Test Implementation**: Verify SSL pinning works in staging
3. **Native Module Integration**: Consider react-native-ssl-pinning for production
4. **Monitoring Setup**: Implement SSL failure monitoring and alerts
5. **Documentation**: Update team documentation with certificate rotation procedures
