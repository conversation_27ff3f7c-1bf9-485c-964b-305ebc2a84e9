/**
 * Firebase Initialization Service for React Native
 * Handles proper initialization and health checks for React Native Firebase
 */

import { getApp, getApps } from '@react-native-firebase/app';
import messaging from '@react-native-firebase/messaging';
import logger from './productionLogger';

// Project configuration for validation
const PROJECT_ID = "vendy-511fb";

class FirebaseInitService {
  private initialized = false;
  private initializationPromise: Promise<boolean> | null = null;

  /**
   * Initialize Firebase - React Native Firebase auto-initializes from google-services.json
   * This method ensures Firebase is ready and performs health checks
   */
  async initialize(): Promise<boolean> {
    // Return existing promise if initialization is already in progress
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    // Return immediately if already initialized
    if (this.initialized) {
      return true;
    }

    this.initializationPromise = this.performInitialization();
    return this.initializationPromise;
  }

  private async performInitialization(): Promise<boolean> {
    try {
      logger.info('Starting Firebase initialization', {
        service: 'firebaseInitService',
        method: 'performInitialization'
      });
      
      // Import Platform to check current platform
      const { Platform } = await import('react-native');
      logger.info('Firebase initialization platform detected', {
        service: 'firebaseInitService',
        method: 'performInitialization',
        platform: Platform.OS
      });

      // For now, only initialize on Android until iOS config is added
      if (Platform.OS === 'ios') {
        logger.warn('iOS Firebase configuration not available, skipping initialization', {
          service: 'firebaseInitService',
          method: 'performInitialization',
          platform: Platform.OS,
          reason: 'Missing GoogleService-Info.plist'
        });
        logger.warn('Add GoogleService-Info.plist to enable Firebase on iOS', {
          service: 'firebaseInitService',
          method: 'performInitialization',
          platform: Platform.OS
        });
        this.initialized = false;
        this.initializationPromise = null;
        return false;
      }

      // Proceed with Android initialization
      logger.info('Checking Firebase auto-initialization for Android platform', {
        service: 'firebaseInitService',
        method: 'performInitialization',
        platform: Platform.OS
      });

      // React Native Firebase should auto-initialize from google-services.json
      // We just need to verify it's working
      let app;
      
      try {
        // React Native Firebase auto-initializes from google-services.json
        // For React Native Firebase, we don't call initializeApp() - it's automatic
        app = getApp();
        logger.info('React Native Firebase app is available (auto-initialized)', {
          service: 'firebaseInitService',
          method: 'performInitialization',
          appName: app.name,
          projectId: app.options?.projectId || PROJECT_ID,
        });
      } catch (appError) {
        logger.error('React Native Firebase app not available - auto-initialization failed', {
          service: 'firebaseInitService',
          method: 'performInitialization',
          error: appError.message,
          availableApps: getApps().length || 0,
          errorCode: appError.code || 'unknown',
          platform: Platform.OS
        });
        
        // Check if this is a configuration issue
        if (appError.message?.includes('No Firebase App') || appError.message?.includes('initializeApp')) {
          throw new Error('React Native Firebase configuration missing. Ensure google-services.json is properly configured and React Native Firebase packages are installed.');
        }
        
        // This is a critical error - React Native Firebase should auto-initialize
        throw new Error(`React Native Firebase auto-initialization failed: ${appError.message}`);
      }
      
      // Safely get project ID
      const projectId = app.options?.projectId || PROJECT_ID;
      logger.info('Firebase project ID configured', {
        service: 'firebaseInitService',
        method: 'performInitialization',
        projectId: projectId
      });

      // Validate project configuration
      if (!projectId) {
        throw new Error('Firebase project ID not found in configuration');
      }

      // Test messaging service
      const messagingService = messaging(app);
      logger.info('Messaging service initialized', {
        service: 'firebaseInitService',
        method: 'performInitialization'
      });

      // Check if we can get a token (this will fail if Firebase isn't properly set up)
      try {
        const token = await messagingService.getToken();
        if (token) {
          logger.info('FCM token service is working', {
            service: 'firebaseInitService',
            method: 'performInitialization',
            tokenLength: token.length
          });
        } else {
        logger.warn('FCM token is null or empty (may be expected in emulator)', {
          service: 'firebaseInitService',
          method: 'performInitialization',
          reason: 'Token null or empty'
        });
        }
      } catch (tokenError) {
        logger.warn('FCM token test failed', {
          service: 'firebaseInitService',
          method: 'performInitialization',
          error: tokenError.message
        });
        
        // More specific error handling
        if (tokenError && typeof tokenError === 'object' && 'code' in tokenError) {
          logger.warn('FCM token error code', {
            service: 'firebaseInitService',
            method: 'performInitialization',
            errorCode: tokenError.code
          });
          
          // If it's a configuration error, throw it
          if (tokenError.code === 'messaging/invalid-configuration' || 
              tokenError.code === 'messaging/registration-token-not-registered') {
            throw tokenError;
          }
        }
        
        // For other errors (like emulator issues in development), continue
        logger.warn('Continuing despite token error (may be expected in development/emulator)', {
          service: 'firebaseInitService',
          method: 'performInitialization',
          reason: 'Development/Emulator Environment'
        });
      }

      this.initialized = true;
      logger.info('Firebase initialization completed successfully for Android', {
        service: 'firebaseInitService',
        method: 'performInitialization'
      });
      return true;

    } catch (error) {
      logger.error('Firebase initialization failed', {
        service: 'firebaseInitService',
        method: 'performInitialization',
        error: error.message
      });
      
      // Log more details about the error
      if (error && typeof error === 'object') {
        logger.error('Error details', {
          service: 'firebaseInitService',
          method: 'performInitialization',
          message: error.message || 'Unknown error',
          code: ('code' in error) ? error.code : 'No error code',
          stack: ('stack' in error) ? error.stack : 'No stack trace'
        });
      }
      
      this.initialized = false;
      this.initializationPromise = null;
      return false;
    }
  }

  /**
   * Check if Firebase is properly initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Get Firebase app instance
   */
  getApp() {
    if (!this.initialized) {
      throw new Error('Firebase not initialized. Call initialize() first.');
    }

    try {
      return getApp();
    } catch (error) {
      logger.error('Error getting Firebase app', {
        service: 'firebaseInitService',
        method: 'getApp',
        error: error.message
      });
      throw new Error('Firebase app not available. Ensure proper initialization.');
    }
  }

  /**
   * Force re-initialization (useful when adding iOS config later)
   */
  async reinitialize(): Promise<boolean> {
    this.initialized = false;
    this.initializationPromise = null;
    return this.initialize();
  }

  /**
   * Check current platform support
   */
  getPlatformSupport(): { 
    currentPlatform: string; 
    isSupported: boolean; 
    reason?: string; 
  } {
    const Platform = require('react-native').Platform;
    const currentPlatform = Platform.OS;
    
    if (currentPlatform === 'android') {
      return {
        currentPlatform,
        isSupported: true
      };
    } else if (currentPlatform === 'ios') {
      return {
        currentPlatform,
        isSupported: false,
        reason: 'iOS Firebase configuration (GoogleService-Info.plist) not added yet'
      };
    } else {
      return {
        currentPlatform,
        isSupported: false,
        reason: 'Unsupported platform'
      };
    }
  }

  /**
   * Health check for Firebase services
   */
  async healthCheck(): Promise<{ 
    app: boolean; 
    messaging: boolean; 
    projectId: string | null; 
    platform: string;
    platformSupported: boolean;
  }> {
    const Platform = require('react-native').Platform;
    const platformSupport = this.getPlatformSupport();
    
    try {
      // Check if Firebase app exists with proper error handling
      let app;
      try {
        app = getApp();
      } catch (appError) {
        logger.warn('Firebase app not available during health check', {
          service: 'firebaseInitService',
          method: 'healthCheck',
          error: appError.message
        });
        return {
          app: false,
          messaging: false,
          projectId: null,
          platform: Platform.OS,
          platformSupported: platformSupport.isSupported
        };
      }
      
      const projectId = app.options?.projectId || PROJECT_ID || null;
      
      let messagingHealthy = false;
      if (platformSupport.isSupported) {
        try {
          const messagingService = messaging();
          await messagingService.getToken();
          messagingHealthy = true;
        } catch (error) {
          logger.warn('Messaging service test failed during health check', {
            service: 'firebaseInitService',
            method: 'healthCheck',
            error: error.message
          });
        }
      }

      return {
        app: true,
        messaging: messagingHealthy,
        projectId,
        platform: Platform.OS,
        platformSupported: platformSupport.isSupported
      };
    } catch (error) {
      logger.error('Health check failed', {
        service: 'firebaseInitService',
        method: 'healthCheck',
        error: error.message
      });
      return {
        app: false,
        messaging: false,
        projectId: null,
        platform: Platform.OS,
        platformSupported: platformSupport.isSupported
      };
    }
  }
}

// Export singleton instance
export const firebaseInitService = new FirebaseInitService();
export default firebaseInitService;