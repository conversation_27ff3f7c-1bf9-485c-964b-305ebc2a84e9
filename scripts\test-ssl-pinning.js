#!/usr/bin/env node

/**
 * SSL Pinning Test Script
 * Tests the SSL pinning configuration and API connectivity
 */

const https = require('https');
const crypto = require('crypto');

// Test configuration
const testConfig = {
  baseUrl: 'https://funny-poems-slide.loca.lt',
  expectedHash: 'IjUc/BEPc+Jj43d+KZ+l1tyAuXfBaYeIvLuvQDaa2nY=',
  testEndpoints: [
    '/api/v1/health',
    '/api/v1/feature-flags',
    '/api/v1/auth/status'
  ]
};

/**
 * Get certificate hash from a URL
 */
function getCertificateHash(url) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: 443,
      method: 'GET',
      rejectUnauthorized: false,
    };

    const req = https.request(options, (res) => {
      const cert = res.socket.getPeerCertificate();
      
      if (!cert || Object.keys(cert).length === 0) {
        reject(new Error('No certificate found'));
        return;
      }

      const publicKeyDer = cert.pubkey;
      const publicKeyHash = crypto.createHash('sha256').update(publicKeyDer).digest('base64');
      
      resolve({
        hash: publicKeyHash,
        subject: cert.subject.CN,
        issuer: cert.issuer.CN,
        validFrom: cert.valid_from,
        validTo: cert.valid_to
      });
    });

    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

/**
 * Test API endpoint connectivity
 */
function testEndpoint(url) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    https.get(url, {
      timeout: 10000,
      headers: {
        'User-Agent': 'Vendy-SSL-Test/1.0',
        'Accept': 'application/json'
      }
    }, (res) => {
      const duration = Date.now() - startTime;
      
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          duration: duration,
          headers: res.headers,
          data: data.substring(0, 200) // First 200 chars
        });
      });
    }).on('error', reject);
  });
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🔐 SSL Pinning & API Connectivity Test');
  console.log('=====================================\n');

  // Test 1: Certificate Hash Validation
  console.log('📋 Test 1: Certificate Hash Validation');
  console.log('---------------------------------------');
  
  try {
    const certInfo = await getCertificateHash(testConfig.baseUrl);
    
    console.log(`✅ Certificate retrieved successfully`);
    console.log(`   Subject: ${certInfo.subject}`);
    console.log(`   Issuer: ${certInfo.issuer}`);
    console.log(`   Valid: ${certInfo.validFrom} - ${certInfo.validTo}`);
    console.log(`   Actual Hash: ${certInfo.hash}`);
    console.log(`   Expected Hash: ${testConfig.expectedHash}`);
    
    if (certInfo.hash === testConfig.expectedHash) {
      console.log('✅ Certificate hash matches! SSL pinning will work.');
    } else {
      console.log('❌ Certificate hash mismatch! Update the configuration.');
      console.log('   Update these files:');
      console.log('   - src/config/environment.ts');
      console.log('   - src/services/sslPinningService.ts');
    }
    
  } catch (error) {
    console.log(`❌ Certificate test failed: ${error.message}`);
  }

  console.log('\n');

  // Test 2: API Endpoint Connectivity
  console.log('🌐 Test 2: API Endpoint Connectivity');
  console.log('------------------------------------');

  for (const endpoint of testConfig.testEndpoints) {
    const url = `${testConfig.baseUrl}${endpoint}`;
    
    try {
      console.log(`🔍 Testing ${endpoint}...`);
      const result = await testEndpoint(url);
      
      console.log(`   Status: ${result.status}`);
      console.log(`   Duration: ${result.duration}ms`);
      
      if (result.status >= 200 && result.status < 300) {
        console.log('   ✅ Endpoint accessible');
      } else if (result.status === 404) {
        console.log('   ⚠️  Endpoint not found (may not be implemented yet)');
      } else {
        console.log('   ❌ Endpoint returned error status');
      }
      
    } catch (error) {
      console.log(`   ❌ Connection failed: ${error.message}`);
    }
    
    console.log('');
  }

  // Test 3: Backend Server Status
  console.log('🖥️  Test 3: Backend Server Status');
  console.log('----------------------------------');
  
  try {
    const result = await testEndpoint(testConfig.baseUrl);
    console.log(`✅ Backend server is running`);
    console.log(`   Response time: ${result.duration}ms`);
    console.log(`   Server: ${result.headers.server || 'Unknown'}`);
  } catch (error) {
    console.log(`❌ Backend server not accessible: ${error.message}`);
    console.log('   Make sure your backend server is running and accessible via localtunnel');
  }

  console.log('\n');

  // Summary
  console.log('📊 Test Summary');
  console.log('===============');
  console.log('1. Run your backend server');
  console.log('2. Ensure localtunnel is active: https://funny-poems-slide.loca.lt');
  console.log('3. Test the React Native app with SSL pinning enabled');
  console.log('4. Monitor logs for SSL validation messages');
  console.log('\n');
  console.log('🔧 If certificate hash changes:');
  console.log('   - Run: node scripts/get-ssl-certificates.js');
  console.log('   - Update certificate hashes in configuration files');
  console.log('   - Rebuild and test the app');
}

// Run tests
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { getCertificateHash, testEndpoint };
