import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';

/**
 * App Version Utility
 * Provides dynamic app version information from native app bundle
 */

interface AppVersionInfo {
  version: string;
  buildNumber: string;
  bundleId: string;
  displayVersion: string;
}

class AppVersionService {
  private versionInfo: AppVersionInfo | null = null;

  /**
   * Get app version information
   */
  async getVersionInfo(): Promise<AppVersionInfo> {
    if (this.versionInfo) {
      return this.versionInfo;
    }

    try {
      const version = DeviceInfo.getVersion();
      const buildNumber = DeviceInfo.getBuildNumber();
      const bundleId = DeviceInfo.getBundleId();
      
      this.versionInfo = {
        version,
        buildNumber,
        bundleId,
        displayVersion: `${version} (${buildNumber})`,
      };

      return this.versionInfo;
    } catch (error) {
      // Fallback to package.json version if DeviceInfo fails
      const packageJson = require('../../package.json');
      
      this.versionInfo = {
        version: packageJson.version || '1.0.0',
        buildNumber: '1',
        bundleId: packageJson.name || 'com.vendy.app',
        displayVersion: `${packageJson.version || '1.0.0'} (1)`,
      };

      return this.versionInfo;
    }
  }

  /**
   * Get just the version string for API headers
   */
  async getVersionString(): Promise<string> {
    const versionInfo = await this.getVersionInfo();
    return versionInfo.version;
  }

  /**
   * Get display version for UI
   */
  async getDisplayVersion(): Promise<string> {
    const versionInfo = await this.getVersionInfo();
    return versionInfo.displayVersion;
  }

  /**
   * Get build number
   */
  async getBuildNumber(): Promise<string> {
    const versionInfo = await this.getVersionInfo();
    return versionInfo.buildNumber;
  }

  /**
   * Get bundle identifier
   */
  async getBundleId(): Promise<string> {
    const versionInfo = await this.getVersionInfo();
    return versionInfo.bundleId;
  }

  /**
   * Get platform-specific version info for API requests
   */
  async getApiVersionHeaders(): Promise<Record<string, string>> {
    const versionInfo = await this.getVersionInfo();
    
    return {
      'X-App-Version': versionInfo.version,
      'X-App-Build': versionInfo.buildNumber,
      'X-App-Platform': Platform.OS,
      'X-App-Platform-Version': Platform.Version.toString(),
      'X-Bundle-Id': versionInfo.bundleId,
    };
  }

  /**
   * Clear cached version info (useful for testing)
   */
  clearCache(): void {
    this.versionInfo = null;
  }
}

export const appVersionService = new AppVersionService();
export default appVersionService;

// Export types
export type { AppVersionInfo };
