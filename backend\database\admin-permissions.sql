-- PayVendy Admin Permissions Schema
-- Add advanced permission system for admin users

-- Add admin permissions column to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS admin_permissions JSONB DEFAULT '{}';

-- Add admin activity logging table
CREATE TABLE IF NOT EXISTS admin_activity_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    admin_id UUID REFERENCES users(id) ON DELETE CASCADE,
    action VARCHAR(100) NOT NULL,
    target_type VARCHAR(50) NOT NULL, -- 'user', 'transaction', 'system', etc.
    target_id UUID,
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_admin_id ON admin_activity_logs(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_created_at ON admin_activity_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_action ON admin_activity_logs(action);
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_target ON admin_activity_logs(target_type, target_id);

-- Add admin sessions tracking
CREATE TABLE IF NOT EXISTS admin_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    admin_id UUID REFERENCES users(id) ON DELETE CASCADE,
    session_token TEXT NOT NULL,
    permissions JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for admin sessions
CREATE INDEX IF NOT EXISTS idx_admin_sessions_admin_id ON admin_sessions(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_token ON admin_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_admin_sessions_expires_at ON admin_sessions(expires_at);

-- Update existing admin users with default permissions
UPDATE users 
SET admin_permissions = '{
    "read_users": true,
    "write_users": false,
    "delete_users": false,
    "manage_permissions": false,
    "view_sensitive_data": false,
    "manage_transactions": false,
    "system_settings": false,
    "audit_logs": true,
    "bulk_operations": false,
    "export_data": false
}'::jsonb
WHERE role = 'admin' AND (admin_permissions IS NULL OR admin_permissions = '{}');

-- Create super admin permissions (for initial setup)
-- Replace '<EMAIL>' with your actual admin email
UPDATE users 
SET admin_permissions = '{
    "read_users": true,
    "write_users": true,
    "delete_users": true,
    "manage_permissions": true,
    "view_sensitive_data": true,
    "manage_transactions": true,
    "system_settings": true,
    "audit_logs": true,
    "bulk_operations": true,
    "export_data": true
}'::jsonb
WHERE role = 'admin' AND email = '<EMAIL>';

-- Add function to log admin activities
CREATE OR REPLACE FUNCTION log_admin_activity(
    p_admin_id UUID,
    p_action VARCHAR(100),
    p_target_type VARCHAR(50),
    p_target_id UUID DEFAULT NULL,
    p_details JSONB DEFAULT '{}',
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO admin_activity_logs (
        admin_id,
        action,
        target_type,
        target_id,
        details,
        ip_address,
        user_agent
    ) VALUES (
        p_admin_id,
        p_action,
        p_target_type,
        p_target_id,
        p_details,
        p_ip_address,
        p_user_agent
    ) RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add function to check admin permissions
CREATE OR REPLACE FUNCTION check_admin_permission(
    p_admin_id UUID,
    p_permission VARCHAR(50)
) RETURNS BOOLEAN AS $$
DECLARE
    has_permission BOOLEAN := FALSE;
BEGIN
    SELECT COALESCE((admin_permissions->>p_permission)::boolean, false)
    INTO has_permission
    FROM users
    WHERE id = p_admin_id AND role = 'admin' AND is_active = true;
    
    RETURN COALESCE(has_permission, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add trigger to automatically log user modifications
CREATE OR REPLACE FUNCTION trigger_log_user_modifications()
RETURNS TRIGGER AS $$
BEGIN
    -- Only log if the change was made by an admin
    IF TG_OP = 'UPDATE' AND OLD.updated_at != NEW.updated_at THEN
        -- This would need to be called from the application layer
        -- as we can't easily get the admin_id in a trigger
        NULL;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create the trigger (commented out as it needs application-level admin_id)
-- CREATE TRIGGER user_modification_log
--     AFTER UPDATE ON users
--     FOR EACH ROW
--     EXECUTE FUNCTION trigger_log_user_modifications();

COMMENT ON TABLE admin_activity_logs IS 'Comprehensive logging of all admin activities for security and audit purposes';
COMMENT ON TABLE admin_sessions IS 'Enhanced session tracking for admin users with permission caching';
COMMENT ON COLUMN users.admin_permissions IS 'JSONB object containing granular admin permissions';
