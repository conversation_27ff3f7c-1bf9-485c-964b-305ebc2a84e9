import { Platform } from 'react-native';
import { NavigationContainerRef, CommonActions } from '@react-navigation/native';
import { RootStackParamList } from '../types/navigation';
import logger from '../services/productionLogger';
import crashReporting from '../services/crashReportingService';
import secureStorage from '../services/secureStorageService';

/**
 * Navigation Security Utilities for Cross-Platform Fintech App
 * Provides secure navigation patterns, deep link protection, and navigation guards
 */

interface NavigationSecurityConfig {
  enableDeepLinkProtection: boolean;
  enableNavigationLogging: boolean;
  enableAuthGuards: boolean;
  maxNavigationDepth: number;
  blockedScreensInBackground: string[];
}

const DEFAULT_CONFIG: NavigationSecurityConfig = {
  enableDeepLinkProtection: true,
  enableNavigationLogging: true,
  enableAuthGuards: true,
  maxNavigationDepth: 20,
  blockedScreensInBackground: ['PinSetup', 'PinVerification', 'BiometricSetup'],
};

class NavigationSecurityManager {
  private config: NavigationSecurityConfig;
  private navigationRef: NavigationContainerRef<RootStackParamList> | null = null;
  private navigationHistory: string[] = [];
  private isAppInBackground = false;
  private lastNavigationTime = 0;
  private navigationAttempts = 0;

  constructor(config: Partial<NavigationSecurityConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.setupAppStateListener();
  }

  /**
   * Initialize navigation security with navigation reference
   */
  initialize(navigationRef: NavigationContainerRef<RootStackParamList>) {
    this.navigationRef = navigationRef;
    logger.security('NAVIGATION_SECURITY_INITIALIZED', {
      platform: Platform.OS,
      config: this.config,
    });
  }

  /**
   * Secure navigation with authentication and security checks
   */
  async secureNavigate(
    screenName: keyof RootStackParamList,
    params?: any,
    options?: { requireAuth?: boolean; bypassGuards?: boolean }
  ): Promise<boolean> {
    try {
      // Rate limiting check
      if (!this.checkRateLimit()) {
        logger.security('NAVIGATION_RATE_LIMITED', { screenName, attempts: this.navigationAttempts });
        return false;
      }

      // Authentication guard
      if (options?.requireAuth && !options?.bypassGuards) {
        const isAuthenticated = await this.checkAuthentication();
        if (!isAuthenticated) {
          logger.security('NAVIGATION_AUTH_REQUIRED', { screenName, redirectTo: 'PhoneInput' });
          this.navigateToAuth();
          return false;
        }
      }

      // Background security check
      if (this.isAppInBackground && this.config.blockedScreensInBackground.includes(screenName)) {
        logger.security('NAVIGATION_BLOCKED_IN_BACKGROUND', { screenName });
        return false;
      }

      // Navigation depth check
      if (this.navigationHistory.length >= this.config.maxNavigationDepth) {
        logger.security('NAVIGATION_DEPTH_EXCEEDED', { 
          screenName, 
          depth: this.navigationHistory.length 
        });
        this.resetNavigationStack();
        return false;
      }

      // Perform secure navigation
      return this.performSecureNavigation(screenName, params);

    } catch (error) {
      logger.error('Secure navigation failed', error, 'navigation');
      crashReporting.recordError(error as Error, 'navigation_security');
      return false;
    }
  }

  /**
   * Deep link security validation
   */
  validateDeepLink(url: string): { isValid: boolean; sanitizedUrl?: string; reason?: string } {
    if (!this.config.enableDeepLinkProtection) {
      return { isValid: true, sanitizedUrl: url };
    }

    try {
      // Parse and validate URL
      const parsedUrl = new URL(url);
      
      // Check allowed schemes
      const allowedSchemes = ['vendy', 'https'];
      if (!allowedSchemes.includes(parsedUrl.protocol.replace(':', ''))) {
        logger.security('DEEP_LINK_INVALID_SCHEME', { url, scheme: parsedUrl.protocol });
        return { isValid: false, reason: 'Invalid URL scheme' };
      }

      // Check for malicious patterns
      const maliciousPatterns = [
        /javascript:/i,
        /data:/i,
        /vbscript:/i,
        /<script/i,
        /onload=/i,
        /onerror=/i,
      ];

      for (const pattern of maliciousPatterns) {
        if (pattern.test(url)) {
          logger.security('DEEP_LINK_MALICIOUS_PATTERN', { url, pattern: pattern.source });
          return { isValid: false, reason: 'Malicious pattern detected' };
        }
      }

      // Sanitize URL
      const sanitizedUrl = this.sanitizeUrl(url);
      
      logger.security('DEEP_LINK_VALIDATED', { originalUrl: url, sanitizedUrl });
      return { isValid: true, sanitizedUrl };

    } catch (error) {
      logger.security('DEEP_LINK_VALIDATION_ERROR', { url, error: (error as Error).message });
      return { isValid: false, reason: 'URL parsing failed' };
    }
  }

  /**
   * Navigation guard for sensitive screens
   */
  async canNavigateToScreen(screenName: keyof RootStackParamList): Promise<boolean> {
    const sensitiveScreens: (keyof RootStackParamList)[] = [
      'PinSetup',
      'PinVerification',
      'BiometricSetup',
      'Security',
    ];

    if (!sensitiveScreens.includes(screenName)) {
      return true;
    }

    // Check authentication for sensitive screens
    const isAuthenticated = await this.checkAuthentication();
    if (!isAuthenticated) {
      logger.security('SENSITIVE_SCREEN_ACCESS_DENIED', { screenName });
      return false;
    }

    // Additional biometric check for high-security screens
    const highSecurityScreens: (keyof RootStackParamList)[] = ['Security'];
    if (highSecurityScreens.includes(screenName)) {
      // TODO: Implement biometric verification
      logger.security('HIGH_SECURITY_SCREEN_ACCESS', { screenName });
    }

    return true;
  }

  /**
   * Reset navigation stack for security
   */
  resetNavigationStack() {
    if (!this.navigationRef) return;

    this.navigationRef.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name: 'MainTabs' }],
      })
    );

    this.navigationHistory = [];
    logger.security('NAVIGATION_STACK_RESET', { reason: 'security_reset' });
  }

  /**
   * Handle app state changes for security
   */
  private setupAppStateListener() {
    // This would be implemented in the main App component
    // AppState.addEventListener('change', this.handleAppStateChange);
  }

  handleAppStateChange = (nextAppState: string) => {
    const wasInBackground = this.isAppInBackground;
    this.isAppInBackground = nextAppState === 'background' || nextAppState === 'inactive';

    if (!wasInBackground && this.isAppInBackground) {
      logger.security('APP_BACKGROUNDED', { timestamp: Date.now() });
      // Clear sensitive data from navigation state if needed
    } else if (wasInBackground && !this.isAppInBackground) {
      logger.security('APP_FOREGROUNDED', { timestamp: Date.now() });
      // Re-authenticate if needed
    }
  };

  /**
   * Check authentication status with enhanced session validation
   */
  private async checkAuthentication(): Promise<boolean> {
    try {
      // Import auth session service dynamically
      const { default: authSessionService } = await import('../services/authSessionService');

      // Use the enhanced session check that validates tokens
      const session = await authSessionService.checkExistingSession();
      return session.isAuthenticated;
    } catch (error) {
      logger.error('Authentication check failed', error, 'navigation');
      return false;
    }
  }

  /**
   * Rate limiting for navigation attempts
   */
  private checkRateLimit(): boolean {
    const now = Date.now();
    const timeDiff = now - this.lastNavigationTime;

    if (timeDiff < 100) { // 100ms minimum between navigations
      this.navigationAttempts++;
      if (this.navigationAttempts > 10) { // Max 10 rapid attempts
        return false;
      }
    } else {
      this.navigationAttempts = 0;
    }

    this.lastNavigationTime = now;
    return true;
  }

  /**
   * Navigate to authentication screen with enhanced routing
   */
  private async navigateToAuth() {
    if (!this.navigationRef) return;

    try {
      // Import auth session service to check for partial authentication
      const { default: authSessionService } = await import('../services/authSessionService');

      // Check if user has any stored session data
      const session = await authSessionService.checkExistingSession();

      if (session.isAuthenticated && session.setupStatus) {
        // User has valid session - route based on setup status
        const destination = authSessionService.getNavigationDestination(session.setupStatus, session.user);

        logger.info('Routing authenticated user via navigation security', {
          screen: destination.screen,
          hasParams: !!destination.params,
          setupStatus: session.setupStatus
        }, 'navigation');

        this.navigationRef.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: destination.screen as keyof RootStackParamList, params: destination.params }],
          })
        );
      } else {
        // No valid session - start fresh authentication
        logger.debug('No valid session, routing to Startup for authentication', null, 'navigation');
        this.navigationRef.dispatch(
          CommonActions.reset({
            index: 0,
            routes: [{ name: 'Startup' }],
          })
        );
      }
    } catch (error) {
      logger.error('Error in navigateToAuth', error, 'navigation');
      // Fallback to startup screen
      this.navigationRef.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: 'Startup' }],
        })
      );
    }
  }

  /**
   * Perform the actual secure navigation
   */
  private performSecureNavigation(
    screenName: keyof RootStackParamList,
    params?: any
  ): boolean {
    if (!this.navigationRef) {
      logger.error('Navigation ref not initialized', null, 'navigation');
      return false;
    }

    try {
      this.navigationRef.navigate(screenName, params);
      this.navigationHistory.push(screenName);

      if (this.config.enableNavigationLogging) {
        logger.info('Navigation completed', { screenName, params }, 'navigation');
        crashReporting.recordNavigation(screenName, params);
      }

      return true;
    } catch (error) {
      logger.error('Navigation failed', error, 'navigation');
      return false;
    }
  }

  /**
   * Sanitize URL for security
   */
  private sanitizeUrl(url: string): string {
    return url
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/data:/gi, '') // Remove data: protocol
      .trim();
  }

  /**
   * Get navigation history for debugging
   */
  getNavigationHistory(): string[] {
    return [...this.navigationHistory];
  }

  /**
   * Clear navigation history
   */
  clearNavigationHistory() {
    this.navigationHistory = [];
    logger.security('NAVIGATION_HISTORY_CLEARED');
  }
}

// Export singleton instance
export const navigationSecurity = new NavigationSecurityManager();

// Export utility functions
export const secureNavigate = navigationSecurity.secureNavigate.bind(navigationSecurity);
export const validateDeepLink = navigationSecurity.validateDeepLink.bind(navigationSecurity);
export const canNavigateToScreen = navigationSecurity.canNavigateToScreen.bind(navigationSecurity);

export default navigationSecurity;
