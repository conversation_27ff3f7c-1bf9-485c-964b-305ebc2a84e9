import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { useTheme } from '../../components/ThemeContext';
import { GiftIcon, DefaultIcon } from '../../components/icons';

const ReferEarnScreen = () => {
  const { theme } = useTheme();
  return (
    <ScrollView style={{ flex: 1, backgroundColor: theme.colors.background }} contentContainerStyle={{ alignItems: 'center', padding: 0 }}>
      {/* Header */}
      <View style={styles.topRow}>
        <View style={[styles.iconCircle, { backgroundColor: theme.colors.card }]}> 
          <GiftIcon size={24} color={theme.colors.primary} />
        </View>
        <Text style={[styles.title, { color: theme.colors.text }]}>Refer & Earn</Text>
      </View>
      {/* Cashback Info */}
      <View style={[styles.infoCard, { backgroundColor: theme.colors.card }]}>
        <Text style={[styles.infoTitle, { color: theme.colors.primary }]}>Cashback Bonus</Text>
        <Text style={[styles.infoText, { color: theme.colors.text }]}>Earn 1.5% cashback on every purchase you make!</Text>
      </View>
      {/* Card List */}
      <View style={[styles.card, { backgroundColor: theme.colors.card }]}> 
        <TouchableOpacity style={styles.optionRow} activeOpacity={0.7}>
          <View style={[styles.optionIcon, { backgroundColor: theme.colors.primary + '22' }]}> 
            <DefaultIcon size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.optionTextWrap}>
            <Text style={[styles.optionTitle, { color: theme.colors.text }]}>Referral QR Code</Text>
            <Text style={[styles.optionSubtitle, { color: theme.colors.muted }]}>Show your code</Text>
          </View>
          <Text style={{ color: theme.colors.primary, fontWeight: '600' }}>Show</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.optionRow} activeOpacity={0.7}>
          <View style={[styles.optionIcon, { backgroundColor: theme.colors.primary + '22' }]}> 
            <DefaultIcon size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.optionTextWrap}>
            <Text style={[styles.optionTitle, { color: theme.colors.text }]}>Leaderboard</Text>
            <Text style={[styles.optionSubtitle, { color: theme.colors.muted }]}>Top referrers</Text>
          </View>
          <Text style={{ color: theme.colors.primary, fontWeight: '600' }}>View</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.optionRow} activeOpacity={0.7}>
          <View style={[styles.optionIcon, { backgroundColor: theme.colors.primary + '22' }]}> 
            <DefaultIcon size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.optionTextWrap}>
            <Text style={[styles.optionTitle, { color: theme.colors.text }]}>Rewards History</Text>
            <Text style={[styles.optionSubtitle, { color: theme.colors.muted }]}>See your rewards</Text>
          </View>
          <Text style={{ color: theme.colors.primary, fontWeight: '600' }}>See</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.optionRow} activeOpacity={0.7}>
          <View style={[styles.optionIcon, { backgroundColor: theme.colors.primary + '22' }]}> 
            <DefaultIcon size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.optionTextWrap}>
            <Text style={[styles.optionTitle, { color: theme.colors.text }]}>Invite via Contacts</Text>
            <Text style={[styles.optionSubtitle, { color: theme.colors.muted }]}>Invite friends</Text>
          </View>
          <Text style={{ color: theme.colors.primary, fontWeight: '600' }}>Invite</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.optionRow} activeOpacity={0.7}>
          <View style={[styles.optionIcon, { backgroundColor: theme.colors.primary + '22' }]}> 
            <DefaultIcon size={20} color={theme.colors.primary} />
          </View>
          <View style={styles.optionTextWrap}>
            <Text style={[styles.optionTitle, { color: theme.colors.text }]}>Social Share</Text>
            <Text style={[styles.optionSubtitle, { color: theme.colors.muted }]}>Share your link</Text>
          </View>
          <Text style={{ color: theme.colors.primary, fontWeight: '600' }}>Share</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  topRow: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 20,
    marginTop: 32,
    marginBottom: 16,
    gap: 12,
  },
  infoCard: {
    width: '92%',
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOpacity: 0.06,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  infoText: {
    fontSize: 14,
    fontWeight: '400',
  },
  iconCircle: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    flex: 1,
  },
  card: {
    width: '92%',
    borderRadius: 18,
    paddingVertical: 8,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOpacity: 0.06,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2,
  },
  optionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 18,
    paddingVertical: 18,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    gap: 16,
  },
  optionIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  optionTextWrap: {
    flex: 1,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  optionSubtitle: {
    fontSize: 13,
    marginTop: 2,
    color: '#888',
  },
});

export default ReferEarnScreen;
