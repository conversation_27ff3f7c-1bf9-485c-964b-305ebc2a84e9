import { GoogleSignin, statusCodes } from '@react-native-google-signin/google-signin';
import { Platform } from 'react-native';
import auth from '@react-native-firebase/auth';
import ApiService from './apiService';
import logger from './productionLogger';

// Initialize Google Sign-In with webClientId from Firebase Console
GoogleSignin.configure({
  webClientId: '1200340808-0ve3ueehjceva3v1ijofnppr7bbv5hhc.apps.googleusercontent.com', // Web client ID from Firebase Console
  offlineAccess: true,
  forceCodeForRefreshToken: true,
  hostedDomain: '',
  iosClientId: '1200340808-2ieb7umolma3f43chobahpcdsbbkv27r.apps.googleusercontent.com', // iOS client ID from Firebase Console
  scopes: ['openid', 'email', 'profile'], // Include openid for ID token
  profileImageSize: 120, // Optional: specify profile image size
});

// Example ApiResponse type
export type ApiResponse<T> = {
  status: string;
  user?: T;
  token?: string; // Added token property
  refreshToken?: string;
  message?: string;
  error?: string;
};

class GoogleAuthService {
  /**
   * Check if Google Play Services are available (Android only)
   */
  async checkPlayServices(): Promise<boolean> {
    try {
      if (Platform.OS === 'android') {
        return await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
      }
      return true; // iOS doesn't need Play Services
    } catch (error) {
      logger.error('Google Play Services check failed', error, 'GoogleAuthService.checkPlayServices');
      return false;
    }
  }



  /**
   * Sign in with Google using native Google Play Services dialog
   * This shows the native bottom sheet account picker and consent screen
   */
  async signIn(): Promise<any> {
    try {
      // First check if Play Services are available
      await this.checkPlayServices();

      // This triggers the native Google Play Services bottom sheet
      // Shows account picker and consent screen just like TikTok/X
      logger.info('Showing native Google Play Services dialog', null, 'GoogleAuthService.signIn');

      // Try signing in with explicit options
      const userInfo = await GoogleSignin.signIn({
        // Force account selection dialog
        forceCodeForRefreshToken: true,
      });

      // Check if we got a valid response
      if (!userInfo) {
        throw new Error('No user information received from Google Sign-In');
      }

      logger.info('Google Sign-In userInfo received', {
        userInfoKeys: Object.keys(userInfo || {}),
        hasIdToken: !!userInfo?.idToken,
        hasUser: !!userInfo?.user,
        email: userInfo?.user?.email || userInfo?.email || 'no email found',
        userInfoStructure: JSON.stringify(userInfo, null, 2)
      }, 'GoogleAuthService.signIn');

      // Check if we have user data
      if (!userInfo.user && !userInfo.data) {
        logger.error('No user data in Google Sign-In response', {
          userInfoStructure: JSON.stringify(userInfo, null, 2)
        }, 'GoogleAuthService.signIn');
        throw new Error('No user data received from Google Sign-In');
      }

      // Get the ID token for Firebase authentication
      // Try multiple ways to get the idToken
      let idToken = userInfo.idToken || userInfo.data?.idToken;

      // Get user data from the response (could be in different locations)
      const userData = userInfo.user || userInfo.data || userInfo;

      // If no ID token in userInfo, try to get tokens separately
      if (!idToken) {
        logger.info('No idToken in userInfo, attempting to get tokens', null, 'GoogleAuthService.signIn');
        try {
          const tokens = await GoogleSignin.getTokens();
          idToken = tokens.idToken;
          logger.info('Retrieved tokens separately', {
            hasIdToken: !!tokens.idToken,
            hasAccessToken: !!tokens.accessToken,
            tokensStructure: JSON.stringify(tokens, null, 2)
          }, 'GoogleAuthService.signIn');
        } catch (tokenError) {
          logger.error('Failed to get tokens separately', tokenError, 'GoogleAuthService.signIn');
        }
      }

      if (!idToken) {
        // Last resort: try to get current user and tokens
        logger.info('Attempting to get current user as fallback', null, 'GoogleAuthService.signIn');
        try {
          const currentUser = await GoogleSignin.getCurrentUser();
          logger.info('Current user retrieved', {
            hasCurrentUser: !!currentUser,
            currentUserKeys: currentUser ? Object.keys(currentUser) : [],
            hasIdToken: !!(currentUser && currentUser.idToken),
            currentUserStructure: currentUser ? JSON.stringify(currentUser, null, 2) : 'null'
          }, 'GoogleAuthService.signIn');

          if (currentUser && currentUser.idToken) {
            idToken = currentUser.idToken;
            logger.info('Got idToken from current user', null, 'GoogleAuthService.signIn');
          } else if (currentUser && currentUser.data && currentUser.data.idToken) {
            idToken = currentUser.data.idToken;
            logger.info('Got idToken from current user data', null, 'GoogleAuthService.signIn');
          }
        } catch (currentUserError) {
          logger.error('Failed to get current user', currentUserError, 'GoogleAuthService.signIn');
        }
      }

      if (!idToken) {
        logger.error('All attempts to get ID token failed', {
          userInfoStructure: JSON.stringify(userInfo, null, 2)
        }, 'GoogleAuthService.signIn');
        throw new Error('No ID token received from Google Sign-In. Please try again.');
      }

      logger.info('Google Sign-In successful, authenticating with Firebase', {
        email: userData?.email || 'no email found',
        idTokenLength: idToken?.length || 0,
        userInfoKeys: Object.keys(userInfo || {}),
        userDataKeys: Object.keys(userData || {})
      }, 'GoogleAuthService.signIn');
      
      // Create a Google credential for Firebase
      const googleCredential = auth.GoogleAuthProvider.credential(idToken);
      
      // Sign in with Firebase using the Google credential
      const firebaseResult = await auth().signInWithCredential(googleCredential);
      
      logger.info('Firebase authentication successful', { 
        uid: firebaseResult.user.uid,
        email: firebaseResult.user.email 
      }, 'GoogleAuthService.signIn');
      
      // For Google authentication, send the original Google ID token, not Firebase token
      // The backend expects the Google ID token for verification
      logger.info('Sending Google ID token to backend for verification', {
        googleIdTokenLength: idToken.length,
        googleIdTokenPreview: `${idToken.substring(0, 50)}...`,
        firebaseUid: firebaseResult.user.uid
      }, 'GoogleAuthService.signIn');

      // Authenticate with backend using Google ID token
      const response = await this.authenticateWithBackend({
        firebaseIdToken: idToken, // Send Google ID token, not Firebase token
        user: {
          id: firebaseResult.user.uid,
          name: firebaseResult.user.displayName || '',
          email: firebaseResult.user.email || '',
          photo: firebaseResult.user.photoURL || undefined,
        }
      }, 'signin');

      return {
        success: true,
        user: response.user,
        token: response.token,
        refreshToken: response.refreshToken,
        firebaseUser: firebaseResult.user,
        googleUser: {
          id: userData?.id || userData?.sub || '',
          name: userData?.name || userData?.givenName || userData?.displayName || '',
          email: userData?.email || '',
          photo: userData?.photo || userData?.photoURL || userData?.picture || undefined,
          isCurrent: true,
        }
      };
    } catch (error: any) {
      if (error instanceof Error) {
        logger.error('Google Sign-In error', { message: error.message, stack: error.stack }, 'GoogleAuthService.signIn');
      } else {
        logger.error('Google Sign-In error (non-Error)', { error }, 'GoogleAuthService.signIn');
      }
      // Handle specific error cases
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        throw new Error('Sign in cancelled');
      } else if (error.code === statusCodes.IN_PROGRESS) {
        throw new Error('Sign in already in progress');
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        throw new Error('Google Play Services not available');
      }
      throw error;
    }
  }

  /**
   * Authenticate with backend using Google ID token
   */
  async authenticateWithBackend(payload: any, mode: 'signin' | 'signup' | 'link'): Promise<any> {
    try {
      // Call backend API to authenticate with Google ID token
      const endpoint = mode === 'link' ? '/auth/google/link' : '/auth/google';
      logger.info('Calling backend authentication endpoint', { endpoint, mode }, 'GoogleAuthService.authenticateWithBackend');
      const response = await ApiService.post(endpoint, {
        idToken: payload.firebaseIdToken, // Backend expects 'idToken' not 'firebaseIdToken'
        deviceInfo: {
          platform: 'mobile',
          userAgent: 'React Native App'
        }
      });
      
      if (response.status === 'success' || (response.data && response.data.status === 'success')) {
        // Handle the response format from /auth/google endpoint
        const responseData = response.data || response;
        return {
          success: true,
          user: responseData.data?.user || responseData.user,
          token: responseData.data?.tokens?.accessToken || responseData.tokens?.accessToken,
          refreshToken: responseData.data?.tokens?.refreshToken || responseData.tokens?.refreshToken,
          isNewUser: responseData.data?.isNewUser || responseData.isNewUser
        };
      } else {
        throw new Error(response.message || (response.data ? response.data.message : 'Authentication failed'));
      }
    } catch (error) {
      logger.error('Backend authentication error', { error }, 'GoogleAuthService.authenticateWithBackend');
      throw error;
    }
  }

  /**
   * Link Google account to existing Firebase user
   */
  async linkAccount(): Promise<any> {
    try {
      // Get the current Firebase user
      const currentUser = auth().currentUser;
      if (!currentUser) {
        throw new Error('No authenticated user to link account to');
      }
      
      // Start Google Sign-In flow
      logger.info('Linking Google account to current user', {}, 'GoogleAuthService.linkAccount');
      const userInfo = await GoogleSignin.signIn();
      
      // Get the ID token for Firebase authentication
      const { idToken } = userInfo;
      if (!idToken) {
        throw new Error('No ID token received from Google Sign-In');
      }
      
      // Create a Google credential for Firebase
      const googleCredential = auth.GoogleAuthProvider.credential(idToken);
      
      // Link the credential to the current user
      const linkResult = await currentUser.linkWithCredential(googleCredential);
      
      logger.info('Google account linked successfully', { 
        uid: linkResult.user.uid,
        email: linkResult.user.email 
      }, 'GoogleAuthService.linkAccount');
      
      // For linking, we need to send the Google ID token for verification
      // Update backend with linked account using Google ID token
      const response = await this.authenticateWithBackend({
        firebaseIdToken: idToken, // Send Google ID token for verification
        user: {
          id: linkResult.user.uid,
          name: linkResult.user.displayName || '',
          email: linkResult.user.email || '',
          photo: linkResult.user.photoURL || undefined,
        }
      }, 'link');
      
      return {
        success: true,
        user: response.user,
        token: response.token
      };
    } catch (error) {
      logger.error('Account linking error', { error }, 'GoogleAuthService.linkAccount');
      throw error;
    }
  }
  /**
   * Sign out from Google and Firebase
   */
  async signOut(): Promise<void> {
    try {
      // Sign out from Google
      await GoogleSignin.signOut();
      
      // Sign out from Firebase
      await auth().signOut();
      
      logger.info('Google and Firebase Sign-Out successful', {}, 'GoogleAuthService.signOut');
    } catch (error) {
      logger.error('Sign-Out error', { error }, 'GoogleAuthService.signOut');
      throw error;
    }
  }

  /**
   * Check if user is currently signed in with Google
   */
  async isSignedIn(): Promise<boolean> {
    try {
      return await GoogleSignin.isSignedIn();
    } catch (error) {
      logger.error('Google isSignedIn check error', { error }, 'GoogleAuthService.isSignedIn');
      return false;
    }
  }

  /**
   * Get current user info
   */
  async getCurrentUser(): Promise<any> {
    try {
      return await GoogleSignin.getCurrentUser();
    } catch (error) {
      logger.error('Google getCurrentUser error', { error }, 'GoogleAuthService.getCurrentUser');
      return null;
    }
  }
}

export default new GoogleAuthService();