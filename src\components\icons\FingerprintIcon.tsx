import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface FingerprintIconProps {
  size?: number;
  color?: string;
}

const FingerprintIcon: React.FC<FingerprintIconProps> = ({ 
  size = 24, 
  color = '#007AFF' 
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M12 1C8.27 1 5.14 3.66 4.25 7.2C4.09 7.9 4 8.64 4 9.4V12C4 12.55 4.45 13 5 13S6 12.55 6 12V9.4C6 8.85 6.06 8.31 6.17 7.78C6.82 5.1 9.24 3 12 3S17.18 5.1 17.83 7.78C17.94 8.31 18 8.85 18 9.4V16C18 16.55 18.45 17 19 17S20 16.55 20 16V9.4C20 8.64 19.91 7.9 19.75 7.2C18.86 3.66 15.73 1 12 1Z"
        fill={color}
      />
      <Path
        d="M12 6C10.34 6 9 7.34 9 9V16C9 16.55 9.45 17 10 17S11 16.55 11 16V9C11 8.45 11.45 8 12 8S13 8.45 13 8V12C13 12.55 13.45 13 14 13S15 12.55 15 12V9C15 7.34 13.66 6 12 6Z"
        fill={color}
      />
      <Path
        d="M7 12V16C7 16.55 7.45 17 8 17S9 16.55 9 16V12C9 11.45 8.55 11 8 11S7 11.45 7 12Z"
        fill={color}
      />
      <Path
        d="M15 16V20C15 20.55 15.45 21 16 21S17 20.55 17 20V16C17 15.45 16.55 15 16 15S15 15.45 15 16Z"
        fill={color}
      />
      <Path
        d="M11 20V22C11 22.55 11.45 23 12 23S13 22.55 13 22V20C13 19.45 12.55 19 12 19S11 19.45 11 20Z"
        fill={color}
      />
    </Svg>
  );
};

export default FingerprintIcon;