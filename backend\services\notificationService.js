const admin = require('firebase-admin');
const { getSupabase } = require('../config/database');
const logger = require('../utils/logger');

/**
 * Save or update FCM token for a user (production scale: upsert, indexed)
 */
async function saveFcmToken(userId, fcmToken, deviceInfo = null) {
  try {
    console.log('💾 [NOTIFICATION-SERVICE] Saving FCM token to database...', {
      userId,
      tokenLength: fcmToken.length,
      hasDeviceInfo: !!deviceInfo,
      devicePlatform: deviceInfo?.platform,
      deviceModel: deviceInfo?.deviceModel,
    });

    const supabase = getSupabase();

    // Prepare the upsert data
    const upsertData = {
      user_id: userId,
      fcm_token: fcmToken,
      last_seen: new Date().toISOString()
    };

    // Add device info if provided
    if (deviceInfo) {
      upsertData.device_info = deviceInfo;
    }

    // Use Supabase upsert to insert or update FCM token
    const { data, error } = await supabase
      .from('user_fcm_tokens')
      .upsert(upsertData, {
        onConflict: 'user_id,fcm_token'
      });

    if (error) {
      console.log('❌ [NOTIFICATION-SERVICE] Database error:', error);
      throw error;
    }

    console.log('✅ [NOTIFICATION-SERVICE] FCM token saved successfully', {
      deviceInfoSaved: !!deviceInfo
    });
    logger.info(`FCM token saved for user ${userId}`, {
      deviceInfo: deviceInfo ? {
        platform: deviceInfo.platform,
        model: deviceInfo.deviceModel,
        appVersion: deviceInfo.appVersion,
        osVersion: deviceInfo.osVersion,
      } : null
    });

  } catch (error) {
    console.log('❌ [NOTIFICATION-SERVICE] Error saving FCM token:', error);
    logger.error('Error saving FCM token:', error);
    throw error;
  }
}

// Notification categories for fintech security
const NOTIFICATION_CATEGORIES = {
  TRANSACTION: 'transaction',
  SECURITY: 'security',
  ACCOUNT: 'account',
  MARKETING: 'marketing',
  SYSTEM: 'system'
};

// Security levels for different notification types
const SECURITY_LEVELS = {
  HIGH: 'high',    // Transactions, security alerts
  MEDIUM: 'medium', // Account updates
  LOW: 'low'       // Marketing, general info
};

/**
 * Send secure push notification with category validation
 */
async function sendPushNotification(userId, title, body, options = {}) {
  try {
    const {
      category = NOTIFICATION_CATEGORIES.SYSTEM,
      securityLevel = SECURITY_LEVELS.LOW,
      data = {},
      priority = 'normal',
      requireAuth = false
    } = options;

    // Validate notification category
    if (!Object.values(NOTIFICATION_CATEGORIES).includes(category)) {
      throw new Error(`Invalid notification category: ${category}`);
    }

    // Log notification attempt for audit trail
    logger.info(`Sending ${category} notification to user ${userId}`, {
      title: title.substring(0, 50), // Log truncated title for privacy
      category,
      securityLevel,
      priority
    });

    const supabase = getSupabase();

    // Get all tokens for the user
    const { data: tokens, error } = await supabase
      .from('user_fcm_tokens')
      .select('fcm_token')
      .eq('user_id', userId);

    if (error) {
      throw error;
    }

    if (!tokens || tokens.length === 0) {
      throw new Error('No FCM tokens for user');
    }

    const fcmTokens = tokens.map(t => t.fcm_token);

    // Build secure message with metadata
    const message = {
      notification: {
        title,
        body,
        // Add security metadata
        tag: `${category}_${Date.now()}` // Prevent duplicate notifications
      },
      data: {
        category,
        securityLevel,
        timestamp: new Date().toISOString(),
        requireAuth: requireAuth.toString(),
        ...data
      },
      android: {
        priority: priority === 'high' ? 'high' : 'normal',
        notification: {
          channelId: category === NOTIFICATION_CATEGORIES.TRANSACTION ? 'transactions' : 'default',
          priority: securityLevel === SECURITY_LEVELS.HIGH ? 'high' : 'default'
        }
      },
      apns: {
        payload: {
          aps: {
            alert: { title, body },
            badge: 1,
            sound: securityLevel === SECURITY_LEVELS.HIGH ? 'critical.wav' : 'default',
            category: category
          }
        }
      },
      tokens: fcmTokens,
    };

    const response = await admin.messaging().sendMulticast(message);
    logger.info(`Push sent to user ${userId}: ${response.successCount} success, ${response.failureCount} failed`);

    // Clean up invalid tokens
    if (response.failureCount > 0) {
      const invalidTokens = response.responses
        .map((r, i) => (!r.success ? fcmTokens[i] : null))
        .filter(Boolean);

      if (invalidTokens.length) {
        const { error: deleteError } = await supabase
          .from('user_fcm_tokens')
          .delete()
          .eq('user_id', userId)
          .in('fcm_token', invalidTokens);

        if (deleteError) {
          logger.error('Error cleaning up invalid FCM tokens:', deleteError);
        } else {
          logger.info(`Cleaned up ${invalidTokens.length} invalid FCM tokens for user ${userId}`);
        }
      }
    }
  } catch (error) {
    logger.error('Error sending push notification:', error);
    throw error;
  }
}

/**
 * Get all FCM tokens for a user (for admin use)
 */
async function getUserFcmTokens(userId) {
  try {
    const supabase = getSupabase();

    const { data: tokens, error } = await supabase
      .from('user_fcm_tokens')
      .select('fcm_token')
      .eq('user_id', userId);

    if (error) {
      throw error;
    }

    return tokens ? tokens.map(t => t.fcm_token) : [];
  } catch (error) {
    logger.error('Error getting user FCM tokens:', error);
    throw error;
  }
}

/**
 * Fintech-specific notification functions
 */

// Send transaction notification (high security)
async function sendTransactionNotification(userId, transactionData) {
  const { amount, type, recipient, reference } = transactionData;

  const title = type === 'debit' ? 'Transaction Alert' : 'Money Received';
  const body = type === 'debit'
    ? `₦${amount} sent to ${recipient || 'recipient'}`
    : `₦${amount} received from ${recipient || 'sender'}`;

  return sendPushNotification(userId, title, body, {
    category: NOTIFICATION_CATEGORIES.TRANSACTION,
    securityLevel: SECURITY_LEVELS.HIGH,
    priority: 'high',
    requireAuth: true,
    data: {
      transactionId: reference,
      amount: amount.toString(),
      type,
      timestamp: new Date().toISOString()
    }
  });
}

// Send security alert (critical)
async function sendSecurityAlert(userId, alertData) {
  const { type, location, device } = alertData;

  const title = 'Security Alert';
  const body = `${type} detected from ${device || 'unknown device'} in ${location || 'unknown location'}`;

  return sendPushNotification(userId, title, body, {
    category: NOTIFICATION_CATEGORIES.SECURITY,
    securityLevel: SECURITY_LEVELS.HIGH,
    priority: 'high',
    requireAuth: true,
    data: {
      alertType: type,
      location,
      device,
      timestamp: new Date().toISOString()
    }
  });
}

// Send account update notification
async function sendAccountNotification(userId, updateData) {
  const { type, message } = updateData;

  const title = 'Account Update';
  const body = message;

  return sendPushNotification(userId, title, body, {
    category: NOTIFICATION_CATEGORIES.ACCOUNT,
    securityLevel: SECURITY_LEVELS.MEDIUM,
    priority: 'normal',
    data: {
      updateType: type,
      timestamp: new Date().toISOString()
    }
  });
}

// Send airtime/data purchase confirmation
async function sendPurchaseNotification(userId, purchaseData) {
  const { type, amount, recipient, provider } = purchaseData;

  const title = 'Purchase Successful';
  const body = `₦${amount} ${type} sent to ${recipient} via ${provider}`;

  return sendPushNotification(userId, title, body, {
    category: NOTIFICATION_CATEGORIES.TRANSACTION,
    securityLevel: SECURITY_LEVELS.HIGH,
    priority: 'high',
    data: {
      purchaseType: type,
      amount: amount.toString(),
      recipient,
      provider,
      timestamp: new Date().toISOString()
    }
  });
}

module.exports = {
  saveFcmToken,
  sendPushNotification,
  getUserFcmTokens,
  sendTransactionNotification,
  sendSecurityAlert,
  sendAccountNotification,
  sendPurchaseNotification,
  NOTIFICATION_CATEGORIES,
  SECURITY_LEVELS
};
