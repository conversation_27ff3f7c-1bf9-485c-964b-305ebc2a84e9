/**
 * Transaction API Routes
 * 
 * Secure API routes for transaction management and status queries with comprehensive
 * authentication, validation, and monitoring.
 * 
 * Routes:
 * - GET /api/v1/transactions - Get transaction history
 * - GET /api/v1/transactions/:transactionId/status - Get transaction status
 * - GET /api/v1/transactions/reference/:reference/status - Get status by reference
 * - POST /api/v1/transactions/:transactionId/retry - Retry failed transaction
 * - GET /api/v1/transactions/analytics - Get transaction analytics
 * 
 * Security Features:
 * - JWT authentication required
 * - User ownership validation
 * - Rate limiting for status queries
 * - Input validation and sanitization
 * - Comprehensive audit logging
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */

const express = require('express');
const rateLimit = require('express-rate-limit');
const { param, query, validationResult } = require('express-validator');

// Import controllers and middleware
const { 
  getTransactionStatus,
  getTransactionStatusByReference,
  getTransactionHistory,
  getStatusQueryValidationRules,
  getReferenceQueryValidationRules
} = require('../controllers/transactionController');

const authService = require('../services/authService');
const logger = require('../utils/logger');
const { getSupabase } = require('../config/database');

const router = express.Router();

// =====================================================
// MIDDLEWARE SETUP
// =====================================================

/**
 * Authentication middleware - All routes require valid JWT
 */
router.use(authService.protect);

/**
 * User existence and activity check
 */
router.use(async (req, res, next) => {
  try {
    if (!req.user || !req.user.is_active) {
      logger.warn('🚫 [TRANSACTION_ROUTES] Inactive user attempted access:', {
        userId: req.user?.id,
        ip: req.ip
      });
      
      return res.status(403).json({
        success: false,
        message: 'Account is not active',
        code: 'ACCOUNT_INACTIVE'
      });
    }
    next();
  } catch (error) {
    logger.error('❌ [TRANSACTION_ROUTES] User check error:', error);
    return res.status(500).json({
      success: false,
      message: 'Authentication error'
    });
  }
});

/**
 * Rate limiting for transaction status queries
 */
const transactionStatusRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 50, // 50 status queries per 5 minutes
  keyGenerator: (req) => `transaction_status:${req.user.id}:${req.ip}`,
  message: {
    success: false,
    message: 'Too many status queries. Please try again later.',
    code: 'STATUS_RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * General rate limiting for transaction operations
 */
const transactionGeneralRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // 200 requests per 15 minutes
  keyGenerator: (req) => `transaction_general:${req.user.id}:${req.ip}`,
  message: {
    success: false,
    message: 'Too many requests. Please try again later.',
    code: 'GENERAL_RATE_LIMIT_EXCEEDED'
  }
});

// =====================================================
// ROUTE DEFINITIONS
// =====================================================

/**
 * @route   GET /api/v1/transactions
 * @desc    Get user's transaction history with filtering and pagination
 * @access  Private
 */
router.get('/',
  transactionGeneralRateLimit,
  [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100'),
    query('status')
      .optional()
      .isIn(['pending', 'completed', 'failed', 'cancelled'])
      .withMessage('Invalid status value'),
    query('type')
      .optional()
      .isIn(['airtime', 'data', 'electricity', 'cable', 'transfer'])
      .withMessage('Invalid transaction type'),
    query('provider')
      .optional()
      .isLength({ min: 1, max: 50 })
      .withMessage('Provider name must be between 1 and 50 characters'),
    query('startDate')
      .optional()
      .isISO8601()
      .withMessage('Start date must be a valid ISO 8601 date'),
    query('endDate')
      .optional()
      .isISO8601()
      .withMessage('End date must be a valid ISO 8601 date')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      logger.info('📋 [TRANSACTION_ROUTES] History request:', {
        userId: req.user.id,
        filters: {
          page: req.query.page,
          limit: req.query.limit,
          status: req.query.status,
          type: req.query.type,
          provider: req.query.provider,
          startDate: req.query.startDate,
          endDate: req.query.endDate
        }
      });

      await getTransactionHistory(req, res);
    } catch (error) {
      logger.error('❌ [TRANSACTION_ROUTES] History request failed:', {
        userId: req.user.id,
        error: error.message
      });
      
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve transaction history'
      });
    }
  }
);

/**
 * @route   GET /api/v1/transactions/:transactionId/status
 * @desc    Get transaction status by transaction ID
 * @access  Private (User can only query their own transactions)
 */
router.get('/:transactionId/status',
  transactionStatusRateLimit,
  getStatusQueryValidationRules(),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      logger.info('🔍 [TRANSACTION_ROUTES] Status query by ID:', {
        userId: req.user.id,
        transactionId: req.params.transactionId
      });

      await getTransactionStatus(req, res);
    } catch (error) {
      logger.error('❌ [TRANSACTION_ROUTES] Status query failed:', {
        userId: req.user.id,
        transactionId: req.params.transactionId,
        error: error.message
      });
      
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: 'Failed to retrieve transaction status'
        });
      }
    }
  }
);

/**
 * @route   GET /api/v1/transactions/reference/:reference/status
 * @desc    Get transaction status by reference
 * @access  Private
 */
router.get('/reference/:reference/status',
  transactionStatusRateLimit,
  getReferenceQueryValidationRules(),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      logger.info('🔍 [TRANSACTION_ROUTES] Status query by reference:', {
        userId: req.user.id,
        reference: req.params.reference
      });

      await getTransactionStatusByReference(req, res);
    } catch (error) {
      logger.error('❌ [TRANSACTION_ROUTES] Reference status query failed:', {
        userId: req.user.id,
        reference: req.params.reference,
        error: error.message
      });
      
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: 'Failed to retrieve transaction status'
        });
      }
    }
  }
);

/**
 * @route   GET /api/v1/transactions/analytics
 * @desc    Get user's transaction analytics and statistics
 * @access  Private
 */
router.get('/analytics',
  transactionGeneralRateLimit,
  [
    query('period')
      .optional()
      .isIn(['7d', '30d', '90d', '1y'])
      .withMessage('Period must be one of: 7d, 30d, 90d, 1y'),
    query('type')
      .optional()
      .isIn(['airtime', 'data', 'electricity', 'cable', 'transfer'])
      .withMessage('Invalid transaction type')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { period = '30d', type } = req.query;
      const userId = req.user.id;

      logger.info('📊 [TRANSACTION_ROUTES] Analytics request:', {
        userId,
        period,
        type
      });

      // Calculate date range based on period
      const endDate = new Date();
      const startDate = new Date();
      
      switch (period) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(endDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(endDate.getDate() - 90);
          break;
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1);
          break;
      }

      const supabase = getSupabase();
      
      // Build query
      let query = supabase
        .from('transactions')
        .select('*')
        .eq('user_id', userId)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());

      if (type) {
        query = query.eq('type', type);
      }

      const { data: transactions, error } = await query;

      if (error) {
        logger.error('❌ [TRANSACTION_ROUTES] Analytics query error:', error);
        return res.status(500).json({
          success: false,
          message: 'Failed to retrieve analytics data'
        });
      }

      // Calculate analytics
      const analytics = {
        period,
        dateRange: {
          start: startDate.toISOString(),
          end: endDate.toISOString()
        },
        summary: {
          totalTransactions: transactions.length,
          totalAmount: transactions.reduce((sum, tx) => sum + parseFloat(tx.amount), 0),
          successfulTransactions: transactions.filter(tx => tx.status === 'completed').length,
          failedTransactions: transactions.filter(tx => tx.status === 'failed').length,
          pendingTransactions: transactions.filter(tx => tx.status === 'pending').length
        },
        byType: {},
        byProvider: {},
        byStatus: {},
        trends: {
          daily: {},
          weekly: {}
        }
      };

      // Group by type
      transactions.forEach(tx => {
        if (!analytics.byType[tx.type]) {
          analytics.byType[tx.type] = { count: 0, amount: 0 };
        }
        analytics.byType[tx.type].count++;
        analytics.byType[tx.type].amount += parseFloat(tx.amount);
      });

      // Group by provider
      transactions.forEach(tx => {
        if (!analytics.byProvider[tx.provider]) {
          analytics.byProvider[tx.provider] = { count: 0, amount: 0 };
        }
        analytics.byProvider[tx.provider].count++;
        analytics.byProvider[tx.provider].amount += parseFloat(tx.amount);
      });

      // Group by status
      transactions.forEach(tx => {
        if (!analytics.byStatus[tx.status]) {
          analytics.byStatus[tx.status] = { count: 0, amount: 0 };
        }
        analytics.byStatus[tx.status].count++;
        analytics.byStatus[tx.status].amount += parseFloat(tx.amount);
      });

      // Calculate success rate
      analytics.summary.successRate = analytics.summary.totalTransactions > 0 
        ? (analytics.summary.successfulTransactions / analytics.summary.totalTransactions * 100).toFixed(2)
        : 0;

      // Calculate average transaction amount
      analytics.summary.averageAmount = analytics.summary.totalTransactions > 0
        ? (analytics.summary.totalAmount / analytics.summary.totalTransactions).toFixed(2)
        : 0;

      res.status(200).json({
        success: true,
        data: analytics
      });

    } catch (error) {
      logger.error('❌ [TRANSACTION_ROUTES] Analytics request failed:', {
        userId: req.user.id,
        error: error.message
      });
      
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve analytics data'
      });
    }
  }
);

// =====================================================
// ERROR HANDLING
// =====================================================

/**
 * Route-specific error handler
 */
router.use((error, req, res, next) => {
  logger.error('❌ [TRANSACTION_ROUTES] Unhandled route error:', {
    error: error.message,
    stack: error.stack,
    url: req.originalUrl,
    method: req.method,
    userId: req.user?.id,
    ip: req.ip
  });

  if (!res.headersSent) {
    res.status(500).json({
      success: false,
      message: 'An unexpected error occurred',
      code: 'INTERNAL_ERROR'
    });
  }
});

module.exports = router;
