const rateLimit = require('express-rate-limit');

function rateLimitMiddleware(type = 'default') {
  // You can customize limits based on 'type' if needed
  return rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
  });
}

module.exports = rateLimitMiddleware;
