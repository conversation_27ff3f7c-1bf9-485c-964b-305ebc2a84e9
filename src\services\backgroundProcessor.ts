/**
 * Background Processing Service
 * Handles background tasks, batching, and optimization for better performance
 */

import logger from './productionLogger';

interface BackgroundTask {
  id: string;
  type: string;
  payload: any;
  priority: 'low' | 'medium' | 'high';
  timestamp: number;
  retryCount: number;
  maxRetries: number;
  delay?: number;
}

interface TaskResult {
  id: string;
  success: boolean;
  error?: string;
  duration: number;
  timestamp: number;
}

interface BatchConfig {
  maxBatchSize: number;
  maxWaitTime: number;
  minBatchSize: number;
}

type TaskHandler = (payload: any) => Promise<any>;

class BackgroundProcessor {
  private taskQueue: BackgroundTask[] = [];
  private handlers: Map<string, TaskHandler> = new Map();
  private processingInterval: NodeJS.Timeout | null = null;
  private batchConfigs: Map<string, BatchConfig> = new Map();
  private pendingBatches: Map<string, BackgroundTask[]> = new Map();
  private batchTimers: Map<string, NodeJS.Timeout> = new Map();
  private maxQueueSize = 1000;
  private processingIntervalMs = 1000; // Process every second
  private isProcessing = false;

  /**
   * Initialize background processor
   */
  initialize(): void {
    try {
      logger.info('Initializing background processor', {
        service: 'backgroundProcessor',
        method: 'initialize'
      });

      // Set up default batch configurations
      this.setupDefaultBatchConfigs();

      // Start processing
      this.startProcessing();

      // Register default task handlers
      this.registerDefaultHandlers();

      logger.info('Background processor initialized successfully', {
        service: 'backgroundProcessor',
        method: 'initialize'
      });
    } catch (error) {
      logger.error('Failed to initialize background processor', {
        service: 'backgroundProcessor',
        method: 'initialize',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Set up default batch configurations
   */
  private setupDefaultBatchConfigs(): void {
    // Analytics events - batch them for efficiency
    this.batchConfigs.set('analytics', {
      maxBatchSize: 50,
      maxWaitTime: 30000, // 30 seconds
      minBatchSize: 10
    });

    // Cache updates - batch for performance
    this.batchConfigs.set('cache_update', {
      maxBatchSize: 20,
      maxWaitTime: 10000, // 10 seconds
      minBatchSize: 5
    });

    // Log uploads - batch for network efficiency
    this.batchConfigs.set('log_upload', {
      maxBatchSize: 100,
      maxWaitTime: 60000, // 1 minute
      minBatchSize: 25
    });

    // Performance metrics - batch for efficiency
    this.batchConfigs.set('performance_metric', {
      maxBatchSize: 30,
      maxWaitTime: 15000, // 15 seconds
      minBatchSize: 10
    });
  }

  /**
   * Register default task handlers
   */
  private registerDefaultHandlers(): void {
    // Analytics handler
    this.registerHandler('analytics', async (events: any[]) => {
      logger.info('Processing analytics batch', {
        service: 'backgroundProcessor',
        method: 'analytics_handler',
        eventCount: events.length
      });
      
      // Simulate analytics upload
      await new Promise(resolve => setTimeout(resolve, 500));
      return { processed: events.length };
    });

    // Cache update handler
    this.registerHandler('cache_update', async (updates: any[]) => {
      logger.info('Processing cache updates', {
        service: 'backgroundProcessor',
        method: 'cache_update_handler',
        updateCount: updates.length
      });
      
      // Process cache updates
      return { processed: updates.length };
    });

    // Log upload handler
    this.registerHandler('log_upload', async (logs: any[]) => {
      logger.info('Uploading logs batch', {
        service: 'backgroundProcessor',
        method: 'log_upload_handler',
        logCount: logs.length
      });
      
      // Simulate log upload
      await new Promise(resolve => setTimeout(resolve, 1000));
      return { uploaded: logs.length };
    });

    // Performance metrics handler
    this.registerHandler('performance_metric', async (metrics: any[]) => {
      logger.info('Processing performance metrics', {
        service: 'backgroundProcessor',
        method: 'performance_metric_handler',
        metricCount: metrics.length
      });
      
      // Process performance metrics
      return { processed: metrics.length };
    });
  }

  /**
   * Register a task handler
   */
  registerHandler(taskType: string, handler: TaskHandler): void {
    this.handlers.set(taskType, handler);
    logger.info('Task handler registered', {
      service: 'backgroundProcessor',
      method: 'registerHandler',
      taskType
    });
  }

  /**
   * Add a task to the queue
   */
  addTask(
    type: string,
    payload: any,
    priority: 'low' | 'medium' | 'high' = 'medium',
    options?: {
      maxRetries?: number;
      delay?: number;
    }
  ): string {
    const taskId = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const task: BackgroundTask = {
      id: taskId,
      type,
      payload,
      priority,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: options?.maxRetries || 3,
      delay: options?.delay
    };

    // Check if this task type should be batched
    if (this.batchConfigs.has(type)) {
      this.addToBatch(task);
    } else {
      this.addToQueue(task);
    }

    return taskId;
  }

  /**
   * Add task to batch for later processing
   */
  private addToBatch(task: BackgroundTask): void {
    const batchConfig = this.batchConfigs.get(task.type)!;
    
    if (!this.pendingBatches.has(task.type)) {
      this.pendingBatches.set(task.type, []);
    }

    const batch = this.pendingBatches.get(task.type)!;
    batch.push(task);

    // Process batch if it reaches max size
    if (batch.length >= batchConfig.maxBatchSize) {
      this.processBatch(task.type);
    } else if (batch.length === 1) {
      // Start timer for first item in batch
      const timer = setTimeout(() => {
        this.processBatch(task.type);
      }, batchConfig.maxWaitTime);
      
      this.batchTimers.set(task.type, timer);
    }
  }

  /**
   * Process a batch of tasks
   */
  private async processBatch(taskType: string): Promise<void> {
    const batch = this.pendingBatches.get(taskType);
    if (!batch || batch.length === 0) return;

    // Clear the batch and timer
    this.pendingBatches.set(taskType, []);
    const timer = this.batchTimers.get(taskType);
    if (timer) {
      clearTimeout(timer);
      this.batchTimers.delete(taskType);
    }

    const batchConfig = this.batchConfigs.get(taskType);
    if (batchConfig && batch.length < batchConfig.minBatchSize) {
      // If batch is too small, add individual tasks to queue
      batch.forEach(task => this.addToQueue(task));
      return;
    }

    // Create a single batch task
    const batchTask: BackgroundTask = {
      id: `batch_${taskType}_${Date.now()}`,
      type: taskType,
      payload: batch.map(task => task.payload),
      priority: batch.reduce((highest, task) => {
        const priorities = { low: 1, medium: 2, high: 3 };
        return priorities[task.priority] > priorities[highest] ? task.priority : highest;
      }, 'low' as 'low' | 'medium' | 'high'),
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: 3
    };

    this.addToQueue(batchTask);

    logger.info('Batch created and queued', {
      service: 'backgroundProcessor',
      method: 'processBatch',
      taskType,
      batchSize: batch.length
    });
  }

  /**
   * Add task to processing queue
   */
  private addToQueue(task: BackgroundTask): void {
    // Remove old tasks if queue is full
    if (this.taskQueue.length >= this.maxQueueSize) {
      const removedTasks = this.taskQueue.splice(0, this.taskQueue.length - this.maxQueueSize + 1);
      logger.warn('Task queue full, removed old tasks', {
        service: 'backgroundProcessor',
        method: 'addToQueue',
        removedCount: removedTasks.length
      });
    }

    // Insert task based on priority
    const insertIndex = this.findInsertIndex(task);
    this.taskQueue.splice(insertIndex, 0, task);
  }

  /**
   * Find the correct index to insert a task based on priority
   */
  private findInsertIndex(task: BackgroundTask): number {
    const priorities = { high: 3, medium: 2, low: 1 };
    const taskPriority = priorities[task.priority];

    for (let i = 0; i < this.taskQueue.length; i++) {
      const queueTaskPriority = priorities[this.taskQueue[i].priority];
      if (taskPriority > queueTaskPriority) {
        return i;
      }
    }

    return this.taskQueue.length;
  }

  /**
   * Start processing tasks
   */
  private startProcessing(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }

    this.processingInterval = setInterval(() => {
      this.processNextTask();
    }, this.processingIntervalMs);
  }

  /**
   * Process the next task in the queue
   */
  private async processNextTask(): Promise<void> {
    if (this.isProcessing || this.taskQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      const task = this.taskQueue.shift()!;
      
      // Check if task should be delayed
      if (task.delay && (Date.now() - task.timestamp) < task.delay) {
        // Re-queue the task
        this.addToQueue(task);
        return;
      }

      await this.executeTask(task);
    } catch (error) {
      logger.error('Error in task processing loop', {
        service: 'backgroundProcessor',
        method: 'processNextTask',
        error: error instanceof Error ? error.message : String(error)
      });
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Execute a single task
   */
  private async executeTask(task: BackgroundTask): Promise<TaskResult> {
    const startTime = Date.now();
    
    try {
      const handler = this.handlers.get(task.type);
      if (!handler) {
        throw new Error(`No handler registered for task type: ${task.type}`);
      }

      logger.info('Executing background task', {
        service: 'backgroundProcessor',
        method: 'executeTask',
        taskId: task.id,
        taskType: task.type,
        priority: task.priority,
        retryCount: task.retryCount
      });

      const result = await handler(task.payload);
      const duration = Date.now() - startTime;

      const taskResult: TaskResult = {
        id: task.id,
        success: true,
        duration,
        timestamp: Date.now()
      };



      logger.info('Background task completed successfully', {
        service: 'backgroundProcessor',
        method: 'executeTask',
        taskId: task.id,
        taskType: task.type,
        duration,
        result
      });

      return taskResult;

    } catch (error) {
      const duration = Date.now() - startTime;
      
      logger.error('Background task failed', {
        service: 'backgroundProcessor',
        method: 'executeTask',
        taskId: task.id,
        taskType: task.type,
        error: error instanceof Error ? error.message : String(error),
        retryCount: task.retryCount,
        maxRetries: task.maxRetries
      });

      // Retry logic
      if (task.retryCount < task.maxRetries) {
        task.retryCount++;
        task.delay = Math.min(1000 * Math.pow(2, task.retryCount), 30000); // Exponential backoff
        this.addToQueue(task);
        
        logger.info('Task queued for retry', {
          service: 'backgroundProcessor',
          method: 'executeTask',
          taskId: task.id,
          retryCount: task.retryCount,
          delay: task.delay
        });
      }

      const taskResult: TaskResult = {
        id: task.id,
        success: false,
        error: error instanceof Error ? error.message : String(error),
        duration,
        timestamp: Date.now()
      };



      return taskResult;
    }
  }

  /**
   * Get queue status
   */
  getQueueStatus(): {
    queueSize: number;
    processingStatus: boolean;
    pendingBatches: Record<string, number>;
    tasksByType: Record<string, number>;
    tasksByPriority: Record<string, number>;
  } {
    const tasksByType: Record<string, number> = {};
    const tasksByPriority: Record<string, number> = {};

    this.taskQueue.forEach(task => {
      tasksByType[task.type] = (tasksByType[task.type] || 0) + 1;
      tasksByPriority[task.priority] = (tasksByPriority[task.priority] || 0) + 1;
    });

    const pendingBatches: Record<string, number> = {};
    this.pendingBatches.forEach((batch, type) => {
      pendingBatches[type] = batch.length;
    });

    return {
      queueSize: this.taskQueue.length,
      processingStatus: this.isProcessing,
      pendingBatches,
      tasksByType,
      tasksByPriority
    };
  }

  /**
   * Configure batch settings for a task type
   */
  configureBatch(taskType: string, config: BatchConfig): void {
    this.batchConfigs.set(taskType, config);
    
    logger.info('Batch configuration updated', {
      service: 'backgroundProcessor',
      method: 'configureBatch',
      taskType,
      config
    });
  }

  /**
   * Clear all tasks from queue
   */
  clearQueue(): number {
    const clearedCount = this.taskQueue.length;
    this.taskQueue = [];
    
    // Clear pending batches
    this.pendingBatches.clear();
    this.batchTimers.forEach(timer => clearTimeout(timer));
    this.batchTimers.clear();

    logger.info('Background task queue cleared', {
      service: 'backgroundProcessor',
      method: 'clearQueue',
      clearedCount
    });

    return clearedCount;
  }

  /**
   * Stop background processing
   */
  stop(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }

    // Clear all batch timers
    this.batchTimers.forEach(timer => clearTimeout(timer));
    this.batchTimers.clear();

    logger.info('Background processor stopped', {
      service: 'backgroundProcessor',
      method: 'stop',
      remainingTasks: this.taskQueue.length
    });
  }

  /**
   * Get performance metrics
   */
  getMetrics(): {
    queueMetrics: any;
    batchMetrics: Record<string, any>;
  } {
    const status = this.getQueueStatus();
    
    const batchMetrics: Record<string, any> = {};
    this.batchConfigs.forEach((config, type) => {
      batchMetrics[type] = {
        config,
        pendingCount: status.pendingBatches[type] || 0
      };
    });

    return {
      queueMetrics: status,
      batchMetrics
    };
  }
}

// Export singleton instance
const backgroundProcessor = new BackgroundProcessor();
export default backgroundProcessor;
