import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { EyeIcon, EyeOffIcon } from '../components/icons';
import Svg, { Defs, ClipPath, Path, Rect, LinearGradient, Stop } from 'react-native-svg';

const { width: screenWidth } = Dimensions.get('window');

interface SimpleBalanceCardProps {
  balance: number;
  balanceVisible: boolean;
  onToggleVisibility: () => void;
  loading: boolean;
  theme: any;
  isDark: boolean;
}

const SimpleBalanceCard: React.FC<SimpleBalanceCardProps> = ({
  balance,
  balanceVisible,
  onToggleVisibility,
  loading,
  theme,
  isDark,
}) => {
  const styles = StyleSheet.create({
    container: {
      marginHorizontal: 20,
      marginVertical: 10,
      height: 180,
      position: 'relative',
    },
    balanceCard: {
      backgroundColor: '#8B5CF6',
      borderRadius: 20,
      padding: 20,
      height: 180,
      justifyContent: 'space-between',
      position: 'relative',
    },
    cutoutOverlay: {
      position: 'absolute',
      bottom: 10,
      right: 10,
      width: 90,
      height: 40,
      backgroundColor: 'transparent',
      borderRadius: 25,
    },
    cutoutMask: {
      position: 'absolute',
      bottom: 10,
      right: 10,
      width: 90,
      height: 40,
      backgroundColor: '#FFFFFF',
      borderRadius: 25,
    },
    balanceSection: {
      flex: 1,
      justifyContent: 'center',
    },
    balanceAmount: {
      fontSize: 32,
      fontWeight: 'bold',
      color: '#FFFFFF',
      marginBottom: 8,
    },
    balanceChange: {
      fontSize: 14,
      color: '#FFFFFF',
      opacity: 0.8,
    },
    cardBottomRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-end',
      paddingRight: 80,
    },
    cardDetails: {
      flexDirection: 'row',
      gap: 20,
    },
    cardInfo: {
      alignItems: 'flex-start',
    },
    cardLabel: {
      fontSize: 12,
      color: '#FFFFFF',
      opacity: 0.7,
      marginBottom: 4,
    },
    cardNumber: {
      fontSize: 14,
      color: '#FFFFFF',
      fontWeight: '600',
    },
    cardExpiry: {
      fontSize: 14,
      color: '#FFFFFF',
      fontWeight: '600',
    },
    addMoneyButton: {
      position: 'absolute',
      bottom: 15,
      right: 15,
      backgroundColor: '#000000',
      paddingHorizontal: 20,
      paddingVertical: 12,
      borderRadius: 25,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 6,
      elevation: 8,
      zIndex: 10,
    },
    addMoneyButtonText: {
      color: '#FFFFFF',
      fontSize: 14,
      fontWeight: '600',
    },
    eyeIcon: {
      position: 'absolute',
      top: 20,
      right: 20,
      padding: 8,
      zIndex: 5,
    },
    loadingText: {
      fontSize: 32,
      fontWeight: 'bold',
      color: '#FFFFFF',
      opacity: 0.5,
    },
  });

  const cardWidth = screenWidth - 40;
  const cardHeight = 180;

  return (
    <View style={{ marginHorizontal: 20, marginVertical: 10, position: 'relative' }}>
      {/* SVG Card with Clippath */}
      <Svg width={cardWidth} height={cardHeight}>
        <Defs>
          <LinearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor="#8B5CF6" />
            <Stop offset="100%" stopColor="#A855F7" />
          </LinearGradient>

          {/* Clippath that cuts out space for the button */}
          <ClipPath id="buttonCutout">
            <Path d={`
              M 20 0
              L ${cardWidth - 20} 0
              Q ${cardWidth} 0 ${cardWidth} 20
              L ${cardWidth} ${cardHeight - 50}
              L ${cardWidth - 110} ${cardHeight - 50}
              Q ${cardWidth - 130} ${cardHeight - 50} ${cardWidth - 130} ${cardHeight - 30}
              Q ${cardWidth - 130} ${cardHeight - 10} ${cardWidth - 110} ${cardHeight - 10}
              L ${cardWidth} ${cardHeight - 10}
              Q ${cardWidth} ${cardHeight} ${cardWidth - 20} ${cardHeight}
              L 20 ${cardHeight}
              Q 0 ${cardHeight} 0 ${cardHeight - 20}
              L 0 20
              Q 0 0 20 0
              Z
            `} />
          </ClipPath>
        </Defs>

        {/* Card background with clippath */}
        <Rect
          width={cardWidth}
          height={cardHeight}
          fill="url(#cardGradient)"
          clipPath="url(#buttonCutout)"
          rx="20"
        />
      </Svg>

      {/* Card Content Overlay */}
      <View style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        padding: 20,
        justifyContent: 'space-between',
      }}>
        {/* Balance Amount */}
        <View style={{ flex: 1, justifyContent: 'center' }}>
          {loading ? (
            <Text style={{ fontSize: 32, fontWeight: 'bold', color: '#FFFFFF', opacity: 0.5 }}>
              Loading...
            </Text>
          ) : (
            <>
              <Text style={{ fontSize: 32, fontWeight: 'bold', color: '#FFFFFF', marginBottom: 8 }}>
                {balanceVisible ? `₦${balance.toLocaleString()}.00` : '₦••••'}
              </Text>
              <Text style={{ fontSize: 14, color: '#FFFFFF', opacity: 0.8 }}>
                +3.50% from last month
              </Text>
            </>
          )}
        </View>

        {/* Card Details */}
        <View style={{ flexDirection: 'row', gap: 20, paddingRight: 120 }}>
          <View>
            <Text style={{ fontSize: 12, color: '#FFFFFF', opacity: 0.7, marginBottom: 4 }}>
              Number
            </Text>
            <Text style={{ fontSize: 14, color: '#FFFFFF', fontWeight: '600' }}>
              •••• 1214
            </Text>
          </View>
          <View>
            <Text style={{ fontSize: 12, color: '#FFFFFF', opacity: 0.7, marginBottom: 4 }}>
              Exp
            </Text>
            <Text style={{ fontSize: 14, color: '#FFFFFF', fontWeight: '600' }}>
              02/15
            </Text>
          </View>
        </View>
      </View>

      {/* Eye Icon */}
      <TouchableOpacity
        onPress={onToggleVisibility}
        style={{
          position: 'absolute',
          top: 20,
          right: 20,
          padding: 8,
          zIndex: 2,
        }}
      >
        {balanceVisible ? (
          <EyeIcon size={24} color="#FFFFFF" />
        ) : (
          <EyeOffIcon size={24} color="#FFFFFF" />
        )}
      </TouchableOpacity>

      {/* Add Money Button - Positioned in the cutout */}
      <TouchableOpacity style={{
        position: 'absolute',
        right: 20,
        bottom: 20,
        backgroundColor: '#000000',
        paddingHorizontal: 20,
        paddingVertical: 10,
        borderRadius: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.3,
        shadowRadius: 4,
        elevation: 5,
        zIndex: 2,
      }}>
        <Text style={{
          color: '#FFFFFF',
          fontSize: 14,
          fontWeight: '600',
        }}>
          Add money
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default SimpleBalanceCard;