import React, { useEffect, memo, useCallback, useMemo } from "react"
import { View, Text, StyleSheet, TouchableOpacity, StatusBar, Animated, ImageBackground, Image } from "react-native"
import Svg, { Path } from 'react-native-svg'
import { useTheme } from "../../components/ThemeContext"
import { useOptimizedAnimation, usePerformanceMonitor } from "../../utils/performance"

import type { NativeStackScreenProps } from "@react-navigation/native-stack"
import type { RootStackParamList } from "../../types/navigation"
import GoogleSignInButton from '../../components/GoogleSignInButton';
import logger from '../../services/productionLogger';
import { setupService } from '../../services/setupService';

// Memoized Google Logo for performance
const GoogleLogo = memo(() => (
  <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
    <Path
      d="M21.8055 10.0415H21V10H12V14H17.6515C16.827 16.3285 14.6115 18 12 18C8.6865 18 6 15.3135 6 12C6 8.6865 8.6865 6 12 6C13.5295 6 14.921 6.577 15.9805 7.5195L18.809 4.691C17.023 3.0265 14.634 2 12 2C6.4775 2 2 6.4775 2 12C2 17.5225 6.4775 22 12 22C17.5225 22 22 17.5225 22 12C22 11.3295 21.931 10.675 21.8055 10.0415Z"
      fill="#FFC107"
    />
    <Path
      d="M3.15295 7.3455L6.43845 9.755C7.32745 7.554 9.48045 6 12 6C13.5295 6 14.921 6.577 15.9805 7.5195L18.809 4.691C17.023 3.0265 14.634 2 12 2C8.15895 2 4.82795 4.1685 3.15295 7.3455Z"
      fill="#FF3D00"
    />
    <Path
      d="M12 22C14.583 22 16.93 21.0115 18.7045 19.404L15.6095 16.785C14.5718 17.5742 13.3038 18.001 12 18C9.39903 18 7.19053 16.3415 6.35853 14.027L3.09753 16.5395C4.75253 19.778 8.11353 22 12 22Z"
      fill="#4CAF50"
    />
    <Path
      d="M21.8055 10.0415H21V10H12V14H17.6515C17.2571 15.1082 16.5467 16.0766 15.608 16.7855L15.6095 16.7845L18.7045 19.4035C18.4855 19.6025 22 17 22 12C22 11.3295 21.931 10.675 21.8055 10.0415Z"
      fill="#1976D2"
    />
  </Svg>
))

GoogleLogo.displayName = 'GoogleLogo'

// Memoized Email Icon for performance
const EmailIcon = memo(() => (
  <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
    <Path
      d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z"
      fill="white"
    />
  </Svg>
))

EmailIcon.displayName = 'EmailIcon'

type Props = NativeStackScreenProps<RootStackParamList, "Startup">

const StartupScreen = memo<Props>(({ navigation }) => {
  const { theme, isDark } = useTheme()

  // Performance monitoring in development
  usePerformanceMonitor('StartupScreen')

  // Optimized animations
  const { animatedValue: fadeAnim, animate: animateFade } = useOptimizedAnimation(0)
  const { animatedValue: slideUpAnim, animate: animateSlideUp } = useOptimizedAnimation(30)
  const { animatedValue: logoFadeAnim, animate: animateLogoFade } = useOptimizedAnimation(0)
  const { animatedValue: contentFadeAnim, animate: animateContentFade } = useOptimizedAnimation(0)
  const { animatedValue: buttonScaleAnim, animate: animateButtonScale } = useOptimizedAnimation(1)

  // Handle Google authentication success and route based on setup status
  const handleGoogleAuthSuccess = useCallback(async (result: any) => {
    try {
      logger.info('Processing Google authentication result', {
        hasUser: !!result.user,
        isNewUser: result.isNewUser,
        email: result.user?.email
      });

      // Extract user data from the authentication result
      const userData = {
        id: result.user?.id || '',
        firstName: result.user?.name?.split(' ')[0] || '',
        lastName: result.user?.name?.split(' ').slice(1).join(' ') || '',
        email: result.user?.email || '',
        picture: result.user?.photo || '',
        isNewUser: result.isNewUser || false,
        authProvider: 'google'
      };

      // For new Google users, they already have name from Google account
      // Skip name setup and go directly to PIN setup
      if (result.isNewUser) {
        logger.info('New Google user - has name from Google account, going directly to PIN setup');
        navigation.navigate('PinSetup', { userData: userData });
        return;
      }

      // For existing users, check their setup status
      logger.info('Existing Google user - checking setup status');

      try {
        const setupStatus = await setupService.getSetupStatus();
        logger.info('Setup status retrieved', {
          hasPinSetup: setupStatus.setupStatus.hasPinSetup,
          hasProfileSetup: setupStatus.setupStatus.hasProfileSetup,
          setupComplete: setupStatus.setupStatus.setupComplete
        });

        // Route based on setup status
        // Google users already have profile info, so skip profile setup
        if (!setupStatus.setupStatus.hasPinSetup) {
          logger.info('Google user needs PIN setup');
          navigation.navigate('PinSetup', { userData: userData });
        } else {
          logger.info('Google user has PIN, going to verification');
          navigation.navigate('PinVerification', { user: userData });
        }
      } catch (setupError) {
        logger.error('Failed to get setup status, defaulting to PIN verification', setupError);
        // If we can't get setup status, assume user needs PIN verification
        navigation.navigate('PinVerification', { user: userData });
      }

    } catch (error) {
      logger.error('Error processing Google authentication result', error);
      // On error, show a generic error or retry
      navigation.navigate('SetupLoading');
    }
  }, [navigation]);

  // Memoized wave component for better performance
  const MemoizedWave = useMemo(() => (
    <Svg height="100" width="100%" style={{ position: 'absolute', bottom: 0 }}>
      <Path
        d="M0,50 Q25,30 50,50 T100,50 L100,100 L0,100 Z"
        fill={theme.colors.background}
        opacity={0.3}
      />
    </Svg>
  ), [theme.colors.background])


  // Optimized entrance animations
  useEffect(() => {
    const runAnimations = async () => {
      animateFade(1)
      await new Promise(resolve => setTimeout(resolve, 200))
      animateLogoFade(1)
      animateSlideUp(0)
      await new Promise(resolve => setTimeout(resolve, 400))
      animateContentFade(1)
    }

    runAnimations()
  }, [animateFade, animateLogoFade, animateSlideUp, animateContentFade])



  // Optimized button handlers with memoization
  const handleGetStarted = useCallback(() => {
    // Navigate directly to email input since SMS is removed
    requestAnimationFrame(() => {
      // Subtle button animation without delaying navigation
      animateButtonScale(0.96, 50, () => {
        // Navigate immediately
        navigation.navigate("EmailInput")
        // Restore button scale after navigation started
        animateButtonScale(1, 50)
      })
    })
  }, [animateButtonScale, navigation])

  const handleEmailSignup = useCallback(() => {
    navigation.navigate("EmailInput")
  }, [navigation])



  // Memoized styles for performance
  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background, // Theme-based background
    },
    imageSection: {
      flex: 0.6, // Image takes up 60% of screen (increased)
      width: '100%',
    },
    darkSection: {
      flex: 0.4, // Dark section takes up 40% of screen (reduced)
      backgroundColor: theme.colors.background, // Theme-based background
      justifyContent: 'center',
      paddingHorizontal: 40, // More horizontal padding for breathing room
      paddingBottom: 60, // More bottom padding
      paddingTop: 20, // Normal top padding
    },
    imageOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.3)', // Slight overlay on image
    },

    contentContainer: {
      alignItems: 'center',
    },
    logoContainer: {
      alignItems: 'center',
      marginBottom: 16,
    },
    logo: {
      fontSize: 24,
      color: '#FF6B6B',
      fontWeight: 'bold',
    },
    brandContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 15,
      marginTop: -120, // Move up to overlap with wave pattern area
    },
    brandIcon: {
      width: 400, // Much bigger width
      height: 200, // Much bigger height
    },
    brandName: {
      fontSize: 24, // Smaller font size
      fontWeight: '700',
      color: theme.colors.text,
      textAlign: 'center',
    },
    tagline: {
      fontSize: 18, // Bigger font size
      color: theme.colors.text,
      textAlign: 'center',
      fontWeight: '700', // Bold font weight
      marginBottom: 25, // More spacing from buttons
      marginTop: -80, // Move even further up to collide with the vendy logo
    },
    primaryButton: {
      backgroundColor: '#000000', // Black button for both themes
      borderRadius: 25, // Bigger border radius
      paddingVertical: 18, // More vertical padding for bigger button
      paddingHorizontal: 60, // Even more horizontal padding for increased length
      alignItems: 'center',
      justifyContent: 'center',
      marginBottom: 12, // Less margin
      // Significantly enhanced shadow for better button appearance
      shadowColor: isDark ? '#FFFFFF' : '#000000', // White shadow in dark mode, black in light mode
      shadowOffset: {
        width: 0,
        height: 6, // Larger offset for more pronounced shadow
      },
      shadowOpacity: isDark ? 0.2 : 0.4, // Adjusted opacity based on theme
      shadowRadius: 8, // Larger radius for more diffuse shadow
      elevation: 10, // Higher elevation for Android
      borderWidth: 0, // No border
    },
    primaryButtonText: {
      color: isDark ? '#FFFFFF' : '#FFFFFF', // White text for both themes for good contrast
      fontSize: 16, // Bigger font size
      fontWeight: '700', // Bolder text
      letterSpacing: 0.5, // More letter spacing
    },
    orContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: 12,
    },
    orLine: {
      flex: 1,
      height: 1,
      backgroundColor: theme.colors.text,
      opacity: 0.3,
    },
    orText: {
      color: theme.colors.text,
      fontSize: 12, // Smaller font
      textAlign: 'center',
      marginHorizontal: 16, // Space between text and lines
      opacity: 0.7,
    },
    socialButtonsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 18, // Less margin
    },
    socialButton: {
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.15)',
      borderRadius: 20, // Smaller border radius
      paddingVertical: 10, // Less padding
      paddingHorizontal: 20, // Less padding
      flex: 1,
      marginHorizontal: 4, // Less margin
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 1,
      borderColor: isDark ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
    },
    googleSignInButton: {
      flex: 1,
      marginHorizontal: 4,
      backgroundColor: '#fff',
      borderRadius: 20,
      minHeight: 44,
    },
    socialButtonText: {
      color: theme.colors.text,
      fontSize: 14, // Smaller font
      fontWeight: '600',
    },
  }), [theme, isDark])

  return (
    <>
      <StatusBar barStyle={isDark ? "light-content" : "dark-content"} backgroundColor="transparent" translucent={true} />
      <ImageBackground
        source={{ uri: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80' }} // Darker background image
        style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}
        resizeMode="cover"
      >
        <View style={{
          ...StyleSheet.absoluteFillObject,
          backgroundColor: isDark ? 'rgba(10,10,20,0.85)' : 'rgba(0,0,0,0.7)', // Slightly darker overlay
        }} />
        <View style={{ width: '88%', alignItems: 'center', justifyContent: 'center' }}>
          <Image
            source={require('../../../assets/icons/vendy.png')}
            style={{ width: 120, height: 120, marginBottom: 24 }}
            resizeMode="contain"
          />
          <Text style={{
            fontSize: 32,
            fontWeight: 'bold',
            color: '#fff',
            textAlign: 'center',
            marginBottom: 8,
            letterSpacing: 1.2,
          }}>
            Welcome to Vendy
          </Text>
          <Text style={{
            fontSize: 16,
            color: '#e0e0e0',
            textAlign: 'center',
            marginBottom: 36,
            opacity: 0.85,
          }}>
            Get affordable data at the best rates. Fast, secure, and easy.
          </Text>

          <GoogleSignInButton
            onSuccess={(result: any) => {
              logger.info('Google sign-in successful from StartupScreen', {
                email: result.user?.email,
                isNewUser: result.isNewUser
              });

              // Handle Google authentication success and route based on setup status
              handleGoogleAuthSuccess(result);
            }}
            onError={(error: Error) => {
              logger.error('Google sign-in failed from StartupScreen', error);
              // Handle error - could show toast or alert
            }}
            text="Continue with Google"
            style={{
              backgroundColor: '#fff',
              borderRadius: 32,
              minHeight: 64,
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: 18,
              width: '90%',
              alignSelf: 'center',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 4 },
              shadowOpacity: 0.18,
              shadowRadius: 10,
              elevation: 6,
              borderWidth: 1.5,
              borderColor: '#e0e0e0',
            }}
          />

          <View style={{ flexDirection: 'row', alignItems: 'center', marginVertical: 10, width: '90%', alignSelf: 'center' }}>
            <View style={{ flex: 1, height: 1, backgroundColor: '#fff', opacity: 0.18 }} />
            <Text style={{ color: '#fff', marginHorizontal: 12, fontSize: 15, opacity: 0.7, fontWeight: '600' }}>or</Text>
            <View style={{ flex: 1, height: 1, backgroundColor: '#fff', opacity: 0.18 }} />
          </View>

          <TouchableOpacity
            style={{
              backgroundColor: isDark ? '#23272f' : '#f7f7f7',
              borderRadius: 32,
              minHeight: 64,
              alignItems: 'center',
              justifyContent: 'center',
              width: '90%', // Slightly increased width
              alignSelf: 'center',
              flexDirection: 'row',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.10,
              shadowRadius: 8,
              elevation: 3,
              borderWidth: 1.5,
              borderColor: isDark ? '#333' : '#e0e0e0',
            }}
            activeOpacity={0.88}
            onPress={handleEmailSignup}
          >
            <EmailIcon />
            <Text style={{ color: isDark ? '#fff' : '#222', fontWeight: '700', fontSize: 17, marginLeft: 12, letterSpacing: 0.5 }}>
              Continue with Email
            </Text>
          </TouchableOpacity>
        </View>
      </ImageBackground>
    </>
  )
})

StartupScreen.displayName = 'StartupScreen'

export default StartupScreen
