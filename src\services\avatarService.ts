/**
 * Avatar Generation Service
 * Generates profile pictures for users using various online avatar services
 * Enhanced with local avatar support based on gender detection
 */

import logger from './productionLogger';

export interface AvatarOptions {
  name?: string;
  email?: string;
  size?: number;
  style?: 'initials' | 'personas' | 'avataaars' | 'adventurer' | 'bigears' | 'miniavs' | 'notionists' | 'openpeeps' | 'local';
  backgroundColor?: string;
  textColor?: string;
  gender?: 'male' | 'female';
  mood?: 'happy' | 'sad' | 'surprised' | 'wink';
  useLocalAvatars?: boolean;
  firstName?: string;
  lastName?: string;
}

export class AvatarService {
  private static readonly DEFAULT_SIZE = 200;
  private static readonly DEFAULT_STYLE = 'local'; // Changed default to local avatars
  private static readonly FALLBACK_STYLE = 'personas';

  /**
   * Simple avatar generation (fallback only - we use local avatars now)
   */
  static async generateAvatar(options: AvatarOptions): Promise<string> {
    const { name, email, size = this.DEFAULT_SIZE, firstName } = options;

    // Simple fallback for any external calls
    const seed = (firstName || name || email?.split('@')[0] || 'default').replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
    return `https://api.dicebear.com/7.x/personas/svg?seed=${seed}&size=${size}&backgroundColor=b6e3f4,c0aede,d1d4f9&mood=happy`;
  }

  /**
   * Generate initials-based avatar using UI Avatars service
   * @param options Avatar generation options
   * @returns string Avatar URL
   */
  private static generateInitialsAvatar(options: AvatarOptions): string {
    const {
      name,
      email,
      size = this.DEFAULT_SIZE,
      backgroundColor = '8B5CF6', // Purple background
      textColor = 'FFFFFF' // White text
    } = options;

    // Extract initials from name or email
    let initials = 'U'; // Default
    
    if (name) {
      const nameParts = name.trim().split(' ');
      if (nameParts.length >= 2) {
        initials = nameParts[0].charAt(0) + nameParts[1].charAt(0);
      } else {
        initials = nameParts[0].charAt(0) + nameParts[0].charAt(1);
      }
    } else if (email) {
      const emailPart = email.split('@')[0];
      initials = emailPart.charAt(0).toUpperCase();
      if (emailPart.length > 1) {
        initials += emailPart.charAt(1).toUpperCase();
      }
    }

    initials = initials.toUpperCase();

    // UI Avatars service - free initials avatar generation
    const avatarUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&size=${size}&background=${backgroundColor}&color=${textColor}&bold=true&format=png`;

    logger.info('Initials avatar generated', {
      initials,
      size,
      backgroundColor,
      textColor,
      url: avatarUrl
    }, 'avatar_service');

    return avatarUrl;
  }

  /**
   * Simple avatar options (fallback only - we use local avatars now)
   */
  static async generateAvatarOptions(options: AvatarOptions): Promise<string[]> {
    const { name, email, firstName } = options;
    const seed = (firstName || name || email?.split('@')[0] || 'default').replace(/[^a-zA-Z0-9]/g, '').toLowerCase();

    return [
      `https://api.dicebear.com/7.x/personas/svg?seed=${seed}1&size=200&backgroundColor=b6e3f4,c0aede,d1d4f9&mood=happy`,
      `https://api.dicebear.com/7.x/personas/svg?seed=${seed}2&size=200&backgroundColor=b6e3f4,c0aede,d1d4f9&mood=surprised`,
      `https://api.dicebear.com/7.x/avataaars/svg?seed=${seed}&size=200&backgroundColor=b6e3f4,c0aede,d1d4f9`
    ];
  }

  /**
   * Generate avatar based on user data with enhanced gender detection
   * @param userData User data containing name, email, etc.
   * @returns Promise<string> Avatar URL
   */
  static async generateUserAvatar(userData: {
    firstName?: string;
    lastName?: string;
    email?: string;
    name?: string;
  }): Promise<string> {
    try {
      // Construct full name
      let fullName = userData.name;
      if (!fullName && (userData.firstName || userData.lastName)) {
        fullName = `${userData.firstName || ''} ${userData.lastName || ''}`.trim();
      }

      const options: AvatarOptions = {
        name: fullName,
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        size: 200,
        style: 'local', // Default to local avatars with gender detection
        mood: 'happy',
        useLocalAvatars: true
      };

      return await this.generateAvatar(options);

    } catch (error) {
      logger.error('Failed to generate user avatar', error, 'avatar_service');

      // Use simple online avatar as fallback
      try {
        const seed = (userData.firstName || userData.name || userData.email?.split('@')[0] || 'default').replace(/[^a-zA-Z0-9]/g, '').toLowerCase();
        const fallbackUrl = `https://api.dicebear.com/7.x/personas/svg?seed=${seed}&size=200&backgroundColor=b6e3f4,c0aede,d1d4f9&mood=happy`;
        logger.info('Using online avatar fallback', { fallbackUrl }, 'avatar_service');
        return fallbackUrl;
      } catch (fallbackError) {
        logger.error('Online avatar fallback failed', fallbackError, 'avatar_service');
        // Ultimate fallback
        return this.generateInitialsAvatar({
          name: userData.firstName || userData.name,
          email: userData.email
        });
      }
    }
  }

  /**
   * Validate if avatar URL is accessible
   * @param avatarUrl Avatar URL to validate
   * @returns Promise<boolean> Whether avatar is accessible
   */
  static async validateAvatarUrl(avatarUrl: string): Promise<boolean> {
    try {
      const response = await fetch(avatarUrl, { method: 'HEAD' });
      return response.ok;
    } catch (error) {
      logger.warn('Avatar URL validation failed', { url: avatarUrl, error }, 'avatar_service');
      return false;
    }
  }

  /**
   * Get fallback avatar URL
   * @param userData User data for fallback generation
   * @returns string Fallback avatar URL
   */
  static getFallbackAvatar(userData: {
    firstName?: string;
    lastName?: string;
    email?: string;
  }): string {
    return this.generateInitialsAvatar({
      name: userData.firstName || userData.lastName,
      email: userData.email,
      backgroundColor: '8B5CF6', // Purple theme
      textColor: 'FFFFFF'
    });
  }
}

export default AvatarService;
