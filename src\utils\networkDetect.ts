// src/utils/networkDetect.ts
export type Network = 'mtn' | 'glo' | 'airtel' | '9mobile' | 'unknown';

const NETWORK_PREFIXES = [
  { name: 'mtn', prefixes: [
    '0803','0806','0703','0706','0813','0816','0810','0814','0903','0906','0913','0916','07025','07026','0704'
  ] },
  { name: 'glo', prefixes: [
    '0805','0807','0705','0811','0815','0905','0915'
  ] },
  { name: 'airtel', prefixes: [
    '0802','0808','0708','0812','0701','0902','0907','0901','0912'
  ] },
  { name: '9mobile', prefixes: [
    '0809','0817','0818','0909','0908'
  ] },
];

export function normalizeNumber(number: string): string {
  let n = number.replace(/[^\d]/g, '');
  if (n.startsWith('234')) n = '0' + n.slice(3);
  if (n.length > 11) n = n.slice(-11);
  return n;
}

export function detectNetwork(number: string): Network {
  const n = normalizeNumber(number);
  for (const net of NETWORK_PREFIXES) {
    for (const prefix of net.prefixes) {
      if (n.startsWith(prefix)) return net.name as Network;
    }
  }
  return 'unknown';
}
