import { Platform } from 'react-native';

interface EnvironmentConfig {
  API_BASE_URL: string;
  API_TIMEOUT: number;
  MAX_RETRY_ATTEMPTS: number;
  CACHE_TTL: number;
  ENABLE_SSL_PINNING: boolean;
  ENABLE_REQUEST_SIGNING: boolean;
  MAX_PIN_ATTEMPTS: number;
  PIN_LOCKOUT_DURATION: number;
  ENABLE_BIOMETRIC_FALLBACK: boolean;
  SSL_PINNING_DOMAINS: string[];
}

const isDevelopment = __DEV__;
const isProduction = !__DEV__;

const productionConfig: EnvironmentConfig = {
  API_BASE_URL: 'https://api.payvendy.name.ng/api/v1',
  API_TIMEOUT: 10000,
  MAX_RETRY_ATTEMPTS: 2,
  CACHE_TTL: 600000,
  ENABLE_SSL_PINNING: false,
  ENABLE_REQUEST_SIGNING: true,
  MAX_PIN_ATTEMPTS: 3,
  PIN_LOCKOUT_DURATION: 900000,
  ENABL<PERSON>_BIOMETRIC_FALLBACK: false,
  SSL_PINNING_DOMAINS: ['api.payvendy.name.ng', 'supabase.co'],
};

const getEnvironmentConfig = (): EnvironmentConfig => {
  return productionConfig;
};

export const ENV_CONFIG = getEnvironmentConfig();

export const SSL_PINNING_CONFIG = {
  enabled: ENV_CONFIG.ENABLE_SSL_PINNING,
  domains: ENV_CONFIG.SSL_PINNING_DOMAINS,
  failOnMismatch: true,
  allowBackupPins: true,
  // Certificate hashes should be updated when certificates are rotated
  certificateHashes: {
    'api.payvendy.name.ng': [
      'C5+lpZ7tcVwmwQIMcRtPbsQtWLABXhQzejna0wHFr8M=',
      'jQJTbIh0grw0/1TkHSumWb+Fs0Ggogr621gT3PvPKG0=',
      'i7WTqTvh0OioIruIfFR4kMPnBqrS2rdiVPl/s2uC/CY=',
      'gkehGfSbEgVOjDfzMvqt6X7F+MyVWx/1VihuqVvpW5s=',
      'VjLZe/p3W/PJnd6lL8JVNBCGQBZynFLdZSTIqcO0SJ8=',
      'VhQGmi/XwBuHqznoFHFHUGONNXBPkINm4fZfQTvTRHU=',
    ],
    'supabase.co': [
      'gkehGfSbEgVOjDfzMvqt6X7F+MyVWx/1VihuqVvpW5s=',
      'i7WTqTvh0OioIruIfFR4kMPnBqrS2rdiVPl/s2uC/CY=',
      'VhQGmi/XwBuHqznoFHFHUGONNXBPkINm4fZfQTvTRHU=',
    ],
  },
};

export const SIGNING_CONFIG = {
  algorithm: 'HS256',
  clockTolerance: 30,
  issuer: 'vendy-mobile-app',
  audience: 'vendy-api',
};

export const PERFORMANCE_CONFIG = {
  MAX_CONCURRENT_REQUESTS: 6,
  REQUEST_QUEUE_SIZE: 50,
  CACHE_SIZE_LIMIT: 100,
  IMAGE_CACHE_SIZE: 200 * 1024 * 1024,
  ENABLE_REQUEST_DEDUPLICATION: true,
  ENABLE_PREFETCHING: true,
  PREFETCH_DELAY: 100,
};

export const SECURITY_HEADERS = {
  'X-Requested-With': 'VendyMobileApp',
  'X-Platform': Platform.OS,
  'X-Device-Type': Platform.select({
    ios: 'ios',
    android: 'android',
    default: 'unknown',
  }),
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};

export const DEBUG_CONFIG = {
  ENABLE_API_LOGGING: isDevelopment,
  ENABLE_PERFORMANCE_MONITORING: true,
  ENABLE_CRASH_REPORTING: isProduction,
  LOG_LEVEL: isDevelopment ? 'debug' : 'error',
};

export default ENV_CONFIG;
