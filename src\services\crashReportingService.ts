import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { apiService } from './apiService';
import logger from './productionLogger';

// Import package.json for version info
const packageJson = require('../../package.json');

interface ErrorLog {
  id: string;
  message: string;
  stack?: string;
  componentStack?: string;
  timestamp: string;
  userId?: string;
  appVersion: string;
  platform: string;
  deviceInfo: {
    model?: string;
    osVersion?: string;
    brand?: string;
    deviceId?: string;
    systemName?: string;
    systemVersion?: string;
    buildNumber?: string;
    bundleId?: string;
    isEmulator?: boolean;
    totalMemory?: number;
    usedMemory?: number;
    freeDiskStorage?: number;
    totalDiskCapacity?: number;
  };
  breadcrumbs: Breadcrumb[];
}

interface Breadcrumb {
  timestamp: string;
  category: string;
  message: string;
  level: 'info' | 'warning' | 'error';
  data?: Record<string, any>;
}

class CrashReportingService {
  private breadcrumbs: Breadcrumb[] = [];
  private maxBreadcrumbs = 50;
  private userId?: string;

  // Rate limiting for crash reports
  private crashReportCount = 0;
  private crashReportWindow = 60000; // 1 minute
  private maxCrashReportsPerWindow = 5;
  private lastCrashReportReset = Date.now();

  constructor() {
    this.initializeService();
    this.startPeriodicMaintenance();
  }

  private async initializeService() {
    // Load user ID if available
    try {
      const storedUserId = await AsyncStorage.getItem('userId');
      if (storedUserId) {
        this.userId = storedUserId;
      }
    } catch (error) {
      logger.warn('Failed to load user ID for crash reporting', error, 'crash');
    }
  }

  setUserId(userId: string) {
    this.userId = userId;
  }

  addBreadcrumb(breadcrumb: Omit<Breadcrumb, 'timestamp'>) {
    const fullBreadcrumb: Breadcrumb = {
      ...breadcrumb,
      timestamp: new Date().toISOString(),
    };

    this.breadcrumbs.push(fullBreadcrumb);

    // Keep only the last N breadcrumbs
    if (this.breadcrumbs.length > this.maxBreadcrumbs) {
      this.breadcrumbs = this.breadcrumbs.slice(-this.maxBreadcrumbs);
    }
  }

  async recordError(error: Error, componentStack?: string) {
    try {
      // Prevent infinite loops by checking if this is already a crash reporting error
      if (error.message?.includes('Failed to store error locally') ||
          error.message?.includes('crash reporting') ||
          error.stack?.includes('crashReportingService')) {
        if (__DEV__) {
          console.warn('Preventing infinite error loop in crash reporting:', error.message);
        }
        return;
      }

      // Prevent infinite loops for session expiry and authentication errors
      // These should be handled by the auth system, not crash reporting
      if (error.message?.includes('Session expired') ||
          error.message?.includes('expired token') ||
          error.message?.includes('Invalid token') ||
          error.message?.includes('Unauthorized') ||
          error.message?.includes('Please login again') ||
          error.message?.includes('Authentication failed') ||
          error.message?.includes('Token refresh failed')) {
        if (__DEV__) {
          console.warn('Skipping crash report for session/auth error (handled by auth system):', error.message);
        }
        // Just add a breadcrumb for debugging, don't send crash report
        this.addBreadcrumb({
          category: 'auth',
          message: 'Session/Auth error occurred',
          level: 'warning',
          data: { errorMessage: error.message },
        });
        return;
      }

      // Rate limiting: Check if we've exceeded the crash report limit
      if (!this.canSendCrashReport()) {
        if (__DEV__) {
          console.warn('Crash report rate limit exceeded, skipping report:', error.message);
        }
        // Still add breadcrumb for debugging
        this.addBreadcrumb({
          category: 'error',
          message: 'Crash report rate limited',
          level: 'warning',
          data: { errorMessage: error.message },
        });
        return;
      }

      const errorLog: ErrorLog = {
        id: this.generateErrorId(),
        message: error.message,
        stack: error.stack,
        componentStack,
        timestamp: new Date().toISOString(),
        userId: this.userId,
        appVersion: this.getAppVersion(),
        platform: Platform.OS,
        deviceInfo: await this.getDeviceInfo(),
        breadcrumbs: [...this.breadcrumbs],
      };

      // Store locally first
      await this.storeErrorLocally(errorLog);

      // Send to remote service (implement based on your choice)
      await this.sendToRemoteService(errorLog);

      if (__DEV__) {
        console.log('Error recorded successfully');
      }
    } catch (recordError) {
      // CRITICAL: Don't use logger.error here as it can cause infinite loops
      if (__DEV__) {
        console.warn('Failed to record error:', recordError);
      }
      // Don't throw or call logger to prevent infinite error loops
    }
  }

  /**
   * Check if we can send a crash report based on rate limiting
   */
  private canSendCrashReport(): boolean {
    const now = Date.now();

    // Reset counter if window has passed
    if (now - this.lastCrashReportReset > this.crashReportWindow) {
      this.crashReportCount = 0;
      this.lastCrashReportReset = now;
    }

    // Check if we're under the limit
    if (this.crashReportCount >= this.maxCrashReportsPerWindow) {
      return false;
    }

    // Increment counter and allow
    this.crashReportCount++;
    return true;
  }

  async recordNonFatalError(message: string, data?: Record<string, any>) {
    this.addBreadcrumb({
      category: 'error',
      message,
      level: 'error',
      data,
    });

    // For non-fatal errors, just log and add breadcrumb
    logger.warn('Non-fatal error recorded', { message, data }, 'crash');
  }

  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private getAppVersion(): string {
    return packageJson.version || '1.0.0';
  }

  private async getDeviceInfo() {
    try {
      const [
        model,
        brand,
        deviceId,
        systemName,
        systemVersion,
        buildNumber,
        bundleId,
        isEmulator,
        totalMemory,
        usedMemory,
        freeDiskStorage,
        totalDiskCapacity,
      ] = await Promise.all([
        DeviceInfo.getModel(),
        DeviceInfo.getBrand(),
        DeviceInfo.getDeviceId(),
        DeviceInfo.getSystemName(),
        DeviceInfo.getSystemVersion(),
        DeviceInfo.getBuildNumber(),
        DeviceInfo.getBundleId(),
        DeviceInfo.isEmulator(),
        DeviceInfo.getTotalMemory(),
        DeviceInfo.getUsedMemory(),
        DeviceInfo.getFreeDiskStorage(),
        DeviceInfo.getTotalDiskCapacity(),
      ]);

      return {
        model,
        brand,
        deviceId,
        systemName,
        systemVersion,
        buildNumber,
        bundleId,
        isEmulator,
        totalMemory,
        usedMemory,
        freeDiskStorage,
        totalDiskCapacity,
        osVersion: Platform.Version.toString(),
      };
    } catch (error) {
      logger.warn('Failed to get detailed device info, using fallback', error, 'crash');
      return {
        model: Platform.OS === 'ios' ? 'iPhone' : 'Android',
        osVersion: Platform.Version.toString(),
      };
    }
  }

  private async storeErrorLocally(errorLog: ErrorLog) {
    try {
      const key = `error_log_${errorLog.id}`;
      await AsyncStorage.setItem(key, JSON.stringify(errorLog));
    } catch (error) {
      // CRITICAL: Don't use logger.error here as it can cause infinite loops
      // Just log to console in development
      if (__DEV__) {
        console.warn('Failed to store error locally:', error);
      }
      // Don't throw or call logger to prevent infinite error loops
    }
  }

  private async sendToRemoteService(errorLog: ErrorLog) {
    try {
      if (__DEV__) {
        logger.info('Would send error to remote service in production', {
          id: errorLog.id,
          message: errorLog.message,
          timestamp: errorLog.timestamp,
        }, 'crash');
        return;
      }

      // Send to custom crash reporting API endpoint
      const response = await apiService.post('/crash-reports', {
        errorId: errorLog.id,
        message: errorLog.message,
        stack: errorLog.stack,
        componentStack: errorLog.componentStack,
        timestamp: errorLog.timestamp,
        userId: errorLog.userId,
        appVersion: errorLog.appVersion,
        platform: errorLog.platform,
        deviceInfo: errorLog.deviceInfo,
        breadcrumbs: errorLog.breadcrumbs.slice(-10), // Send only last 10 breadcrumbs to reduce payload
      }, {
        skipAuth: false, // Require auth for crash reports
        timeout: 10000,
        retries: 2,
      });

      if (response.status === 200 || response.data?.status === 'success') {
        logger.info('Error sent to remote service successfully', {
          id: errorLog.id,
        }, 'crash');

        // Remove from local storage after successful upload
        await AsyncStorage.removeItem(`error_log_${errorLog.id}`);
      } else {
        throw new Error(`Failed to send error: ${response.data?.message || 'Unknown error'}`);
      }
    } catch (error) {
      // CRITICAL: Don't use logger.error here as it can cause infinite loops
      if (__DEV__) {
        console.warn('Failed to send error to remote service:', error);
      }
      // Don't throw here to avoid infinite loops

      // Mark error for retry later
      try {
        const retryKey = `error_retry_${errorLog.id}`;
        const retryData = {
          errorId: errorLog.id,
          retryCount: 1,
          lastAttempt: new Date().toISOString(),
        };
        await AsyncStorage.setItem(retryKey, JSON.stringify(retryData));
      } catch (retryError) {
        if (__DEV__) {
          console.warn('Failed to mark error for retry:', retryError);
        }
      }
    }
  }

  // Navigation breadcrumbs
  recordNavigation(screenName: string, params?: Record<string, any>) {
    this.addBreadcrumb({
      category: 'navigation',
      message: `Navigated to ${screenName}`,
      level: 'info',
      data: { screenName, params },
    });
  }

  // API call breadcrumbs
  recordApiCall(method: string, url: string, status?: number, duration?: number) {
    this.addBreadcrumb({
      category: 'api',
      message: `${method} ${url}`,
      level: status && status >= 400 ? 'error' : 'info',
      data: { method, url, status, duration },
    });
  }

  // User action breadcrumbs
  recordUserAction(action: string, data?: Record<string, any>) {
    this.addBreadcrumb({
      category: 'user',
      message: action,
      level: 'info',
      data,
    });
  }

  // Get stored errors for debugging
  async getStoredErrors(): Promise<ErrorLog[]> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const errorKeys = keys.filter(key => key.startsWith('error_log_'));

      const errors: ErrorLog[] = [];
      for (const key of errorKeys) {
        try {
          const errorData = await AsyncStorage.getItem(key);
          if (errorData) {
            errors.push(JSON.parse(errorData));
          }
        } catch (parseError) {
          if (__DEV__) {
            console.warn('Failed to parse stored error:', { key });
          }
        }
      }

      return errors.sort((a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );
    } catch (error) {
      if (__DEV__) {
        console.warn('Failed to get stored errors:', error);
      }
      return [];
    }
  }

  // Emergency method to clear all stored errors (use when infinite loop occurs)
  async clearAllStoredErrors(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const errorKeys = keys.filter(key =>
        key.startsWith('error_log_') ||
        key.startsWith('error_retry_')
      );

      if (errorKeys.length > 0) {
        await AsyncStorage.multiRemove(errorKeys);
        if (__DEV__) {
          console.log(`Cleared ${errorKeys.length} stored error logs`);
        }
      }
    } catch (error) {
      if (__DEV__) {
        console.warn('Failed to clear stored errors:', error);
      }
    }
  }

  // Clear old error logs
  async clearOldErrors(olderThanDays: number = 7) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const errors = await this.getStoredErrors();
      const keysToDelete: string[] = [];

      for (const error of errors) {
        if (new Date(error.timestamp) < cutoffDate) {
          keysToDelete.push(`error_log_${error.id}`);
        }
      }

      if (keysToDelete.length > 0) {
        await AsyncStorage.multiRemove(keysToDelete);
        logger.info('Cleared old error logs', { count: keysToDelete.length }, 'crash');
      }
    } catch (error) {
      logger.error('Failed to clear old errors', error, 'crash');
    }
  }

  // Retry failed error uploads
  async retryFailedUploads() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const retryKeys = keys.filter(key => key.startsWith('error_retry_'));

      for (const retryKey of retryKeys) {
        try {
          const retryDataStr = await AsyncStorage.getItem(retryKey);
          if (!retryDataStr) continue;

          const retryData = JSON.parse(retryDataStr);
          const errorLogStr = await AsyncStorage.getItem(`error_log_${retryData.errorId}`);

          if (!errorLogStr) {
            // Error log no longer exists, remove retry marker
            await AsyncStorage.removeItem(retryKey);
            continue;
          }

          const errorLog: ErrorLog = JSON.parse(errorLogStr);

          // Only retry if less than 3 attempts and not older than 24 hours
          if (retryData.retryCount < 3 &&
              new Date().getTime() - new Date(retryData.lastAttempt).getTime() < 24 * 60 * 60 * 1000) {

            await this.sendToRemoteService(errorLog);
            await AsyncStorage.removeItem(retryKey);
          } else {
            // Too many retries or too old, remove both retry marker and error log
            await AsyncStorage.multiRemove([retryKey, `error_log_${retryData.errorId}`]);
          }
        } catch (retryError) {
          logger.warn('Failed to retry error upload', { retryKey }, 'crash');
        }
      }
    } catch (error) {
      logger.error('Failed to retry failed uploads', error, 'crash');
    }
  }

  // Performance monitoring
  recordPerformanceMetric(metric: string, value: number, unit: string = 'ms') {
    this.addBreadcrumb({
      category: 'performance',
      message: `${metric}: ${value}${unit}`,
      level: value > 1000 ? 'warning' : 'info',
      data: { metric, value, unit },
    });
  }

  // Memory warning
  recordMemoryWarning(usedMemory: number, totalMemory: number) {
    const memoryUsagePercent = (usedMemory / totalMemory) * 100;

    this.addBreadcrumb({
      category: 'memory',
      message: `Memory warning: ${memoryUsagePercent.toFixed(1)}% used`,
      level: memoryUsagePercent > 90 ? 'error' : 'warning',
      data: {
        usedMemory,
        totalMemory,
        usagePercent: memoryUsagePercent
      },
    });
  }

  // Network status changes
  recordNetworkChange(isConnected: boolean, connectionType?: string) {
    this.addBreadcrumb({
      category: 'network',
      message: `Network ${isConnected ? 'connected' : 'disconnected'}${connectionType ? ` (${connectionType})` : ''}`,
      level: isConnected ? 'info' : 'warning',
      data: { isConnected, connectionType },
    });
  }

  // App state changes
  recordAppStateChange(state: string) {
    this.addBreadcrumb({
      category: 'app',
      message: `App state changed to ${state}`,
      level: 'info',
      data: { state },
    });
  }

  // Initialize periodic cleanup and retry
  startPeriodicMaintenance() {
    // Clean old errors every 24 hours
    setInterval(() => {
      this.clearOldErrors(7);
    }, 24 * 60 * 60 * 1000);

    // Retry failed uploads every hour
    setInterval(() => {
      this.retryFailedUploads();
    }, 60 * 60 * 1000);

    // Initial cleanup and retry
    setTimeout(() => {
      this.clearOldErrors(7);
      this.retryFailedUploads();
    }, 5000); // Wait 5 seconds after app start
  }
}

export const crashReporting = new CrashReportingService();
export default crashReporting;