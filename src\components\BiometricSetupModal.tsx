import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Platform,
  Alert,
  Image,
  Dimensions,
} from 'react-native';
import Modal from 'react-native-modal';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  runOnJS,
  withSpring
} from 'react-native-reanimated';
import { useTheme } from './ThemeContext';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ApiService from '../services/apiService';
import logger from '../services/productionLogger';
import {
  CheckIcon,
  FingerprintIcon,
  FaceIdIcon
} from './icons';
import LinearGradient from 'react-native-linear-gradient';

const { height } = Dimensions.get('window');

// Helper function for biometric logging
const logBiometric = (message: string, data?: any) => {
  logger.info(`[Biometric] ${message}`, data);
};

// Helper function for error logging
const logError = (message: string, data?: any) => {
  logger.error(`[BiometricSetup] ${message}`, data);
};

// Import biometric libraries
let ReactNativeBiometrics: any;
let TouchID: any;

try {
  ReactNativeBiometrics = require('react-native-biometrics').default;
} catch (e) {
  logBiometric('ReactNativeBiometrics not available', { error: e });
  ReactNativeBiometrics = null;
}

try {
  TouchID = require('react-native-touch-id');
} catch (e) {
  logBiometric('TouchID not available', { error: e });
  TouchID = null;
}

interface BiometricSetupModalProps {
  visible: boolean;
  onComplete: (success: boolean) => void;
  userData?: any;
}

interface BiometricCapability {
  available: boolean;
  type: 'fingerprint' | 'face' | 'none';
  error?: string;
}

const BiometricSetupModal: React.FC<BiometricSetupModalProps> = ({
  visible,
  onComplete,
}) => {
  const { theme, isDark } = useTheme();
  const [biometricCapability, setBiometricCapability] = useState<BiometricCapability>({
    available: false,
    type: 'none',
  });
  const [loading, setLoading] = useState(false);
  const [setupComplete, setSetupComplete] = useState(false);
  const [checkingCapability, setCheckingCapability] = useState(true);

  // Gesture handler for drag to dismiss
  const translateY = useSharedValue(0);

  const panGesture = Gesture.Pan()
    .onUpdate((event) => {
      // Only allow downward dragging
      if (event.translationY > 0) {
        translateY.value = event.translationY;
      }
    })
    .onEnd((event) => {
      if (event.translationY > 100) {
        // Dismiss modal if dragged down more than 100px
        runOnJS(handleSkip)();
      } else {
        // Spring back to original position
        translateY.value = withSpring(0);
      }
    });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  useEffect(() => {
    if (visible) {
      checkBiometricCapability();
      translateY.value = 0; // Reset position when modal opens
    }
  }, [visible]);

  const checkBiometricCapability = async () => {
    setCheckingCapability(true);
    
    try {
      // Try react-native-biometrics first (more reliable)
      if (ReactNativeBiometrics) {
        const rnBiometrics = new ReactNativeBiometrics();
        const { available, biometryType } = await rnBiometrics.isSensorAvailable();
        
        if (available) {
          let type: 'fingerprint' | 'face' = 'fingerprint';
          if (biometryType === 'FaceID' || biometryType === 'Face') {
            type = 'face';
          }
          
          setBiometricCapability({
            available: true,
            type,
          });
          setCheckingCapability(false);
          return;
        }
      }

      // Fallback to react-native-touch-id
      if (TouchID) {
        if (Platform.OS === 'ios') {
          const biometryType = await TouchID.isSupported();
          if (biometryType) {
            setBiometricCapability({
              available: true,
              type: biometryType === 'FaceID' ? 'face' : 'fingerprint',
            });
            setCheckingCapability(false);
            return;
          }
        } else {
          const isSupported = await TouchID.isSupported();
          if (isSupported) {
            setBiometricCapability({
              available: true,
              type: 'fingerprint',
            });
            setCheckingCapability(false);
            return;
          }
        }
      }

      // No biometric support found - auto skip
      logBiometric('No biometric support found, auto-skipping', { platform: Platform.OS });
      setBiometricCapability({
        available: false,
        type: 'none',
        error: 'Biometric authentication not available on this device',
      });
      
      // Auto-skip for unsupported devices
      setTimeout(() => {
        handleSkip();
      }, 500);
      
    } catch (error) {
      logError('Biometric check error', { error, platform: Platform.OS });
      setBiometricCapability({
        available: false,
        type: 'none',
        error: 'Unable to check biometric capability',
      });
      
      // Auto-skip on error
      setTimeout(() => {
        handleSkip();
      }, 500);
    } finally {
      setCheckingCapability(false);
    }
  };

  const handleEnableBiometric = async () => {
    if (!biometricCapability.available) {
      handleSkip();
      return;
    }

    setLoading(true);

    try {
      // Test biometric authentication
      let authSuccess = false;

      // Try react-native-biometrics first
      if (ReactNativeBiometrics) {
        const rnBiometrics = new ReactNativeBiometrics();
        const { success } = await rnBiometrics.simplePrompt({
          promptMessage: 'Enable biometric authentication for Vendy',
          cancelButtonText: 'Cancel',
        });
        authSuccess = success;
      } 
      // Fallback to TouchID
      else if (TouchID) {
        const biometricOptions = {
          title: 'Enable Biometric Authentication',
          subtitle: 'Use your biometric to secure your Vendy account',
          description: 'Place your finger on the sensor or look at the camera',
          fallbackLabel: 'Use PIN instead',
          cancelLabel: 'Cancel',
        };
        await TouchID.authenticate('Enable biometric authentication for Vendy', biometricOptions);
        authSuccess = true;
      } else {
        throw new Error('Biometric authentication not available');
      }

      if (!authSuccess) {
        throw new Error('Biometric authentication failed');
      }

      // If authentication successful, save to backend
      const response = await ApiService.post('/setup/biometric', {
        enabled: true,
        biometricType: biometricCapability.type,
        deviceInfo: {
          platform: Platform.OS,
          version: Platform.Version,
          model: Platform.OS === 'ios' ? 'iPhone' : 'Android Device',
        },
      });

      if (response.data.status === 'success') {
        setSetupComplete(true);
        
        // Store biometric enabled status locally
        try {
          await AsyncStorage.setItem('biometricEnabled', 'true');
        } catch (error) {
          logError('Error saving biometric enabled status', { error });
        }
        
        // Animate success and close modal
        setTimeout(() => {
          onComplete(true);
        }, 2000);
      } else {
        throw new Error(response.data.message || 'Failed to enable biometric authentication');
      }
    } catch (error: any) {
      logError('Biometric setup error', { error, biometricType: biometricCapability.type });
      
      if (error.name === 'UserCancel' || error.message === 'User canceled the operation') {
        // User cancelled, treat as skip
        handleSkip();
        return;
      }
      
      Alert.alert(
        'Setup Failed',
        error?.response?.data?.message || error?.message || 'Failed to enable biometric authentication. You can set this up later in settings.',
        [{ text: 'OK', onPress: () => handleSkip() }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSkip = async () => {
    try {
      setLoading(true);
      
      // Save that biometric is disabled
      await ApiService.post('/setup/biometric', {
        enabled: false,
      });

      // Store biometric disabled status locally
      try {
        await AsyncStorage.setItem('biometricEnabled', 'false');
      } catch (error) {
        logError('Error saving biometric disabled status', { error });
      }

      onComplete(false);
    } catch (error) {
      logError('Skip biometric error', { error });
      // Even if API call fails, continue
      onComplete(false);
    } finally {
      setLoading(false);
    }
  };



  const getBiometricTitle = () => {
    switch (biometricCapability.type) {
      case 'face':
        return 'Enable Face ID';
      case 'fingerprint':
        return 'Enable Fingerprint';
      default:
        return 'Enable Biometric';
    }
  };

  const getBiometricDescription = () => {
    switch (biometricCapability.type) {
      case 'face':
        return 'Use Face ID for quick and secure access';
      case 'fingerprint':
        return 'Use your fingerprint for quick access';
      default:
        return 'Use biometric authentication to secure your account';
    }
  };

  const styles = StyleSheet.create({
    modal: {
      justifyContent: 'flex-end',
      margin: 0,
    },
    modalContent: {
      borderTopLeftRadius: 24,
      borderTopRightRadius: 24,
      overflow: 'hidden',
      maxHeight: height * 0.55, // Reduced from 0.7 to 0.55
      position: 'relative',
    },
    dragHandleArea: {
      width: '100%',
      height: 24,
      alignItems: 'center',
      justifyContent: 'center',
      position: 'absolute',
      top: 0,
      zIndex: 10,
    },
    dragHandle: {
      width: 40,
      height: 4,
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.3)',
      borderRadius: 2,
      marginTop: 8,
    },
    gradientBackground: {
      paddingHorizontal: 24,
      paddingTop: 32, // Increased to account for drag handle
      paddingBottom: 32,
      minHeight: 320, // Reduced from 400
    },
    imageContainer: {
      alignItems: 'center',
      marginBottom: 20,
    },
    biometricImage: {
      width: 100, // Reduced from 120
      height: 100, // Reduced from 120
      opacity: 0.8,
    },

    textContainer: {
      alignItems: 'center',
      marginBottom: 24, // Reduced from 32
    },
    title: {
      fontSize: 22, // Reduced from 24
      fontWeight: '600', // Reduced from 700
      color: theme.colors.text,
      marginBottom: 8, // Reduced from 12
      textAlign: 'center',
    },
    description: {
      fontSize: 15, // Reduced from 16
      color: theme.colors.muted,
      textAlign: 'center',
      lineHeight: 22, // Reduced from 24
      paddingHorizontal: 12, // Reduced from 16
    },
    buttonContainer: {
      flexDirection: 'row',
      gap: 12,
      marginTop: 8,
    },
    button: {
      flex: 1,
      height: 48, // Fixed height for equal buttons
    },
    enableButton: {
      borderRadius: 16,
      overflow: 'hidden',
      height: 48,
      // Remove any extra styling that might affect size
    },
    enableButtonGradient: {
      flex: 1,
      paddingVertical: 14,
      paddingHorizontal: 12,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 16,
      height: 48,
    },
    enableButtonText: {
      color: '#FFFFFF',
      fontSize: 13, // Slightly smaller to fit better
      fontWeight: '600',
      textAlign: 'center',
      textShadowColor: 'rgba(0, 0, 0, 0.2)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 1,
    },
    skipButton: {
      paddingVertical: 14,
      paddingHorizontal: 12,
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: 16,
      borderWidth: 1.5,
      borderColor: isDark ? 'rgba(255, 255, 255, 0.25)' : 'rgba(0, 0, 0, 0.2)',
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.05)',
      height: 48,
    },
    skipButtonText: {
      color: theme.colors.muted,
      fontSize: 13, // Match enable button
      fontWeight: '600',
      textAlign: 'center',
    },
    successContainer: {
      alignItems: 'center',
    },
    successIcon: {
      marginBottom: 24,
    },
    successTitle: {
      fontSize: 24,
      fontWeight: '700',
      color: theme.colors.text,
      marginBottom: 12,
      textAlign: 'center',
    },
    successDescription: {
      fontSize: 16,
      color: theme.colors.muted,
      textAlign: 'center',
      lineHeight: 24,
    },
    loadingText: {
      color: theme.colors.text,
      fontSize: 16,
      textAlign: 'center',
    },
  });

  return (
    <Modal
      isVisible={visible}
      style={styles.modal}
      backdropColor="rgba(0, 0, 0, 0.3)"
      backdropOpacity={1}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      animationInTiming={600}
      animationOutTiming={400}
      backdropTransitionInTiming={600}
      backdropTransitionOutTiming={400}
      useNativeDriverForBackdrop
      hideModalContentWhileAnimating
      onBackdropPress={() => handleSkip()}
    >
      <GestureDetector gesture={panGesture}>
        <Animated.View style={[styles.modalContent, animatedStyle]}>
          {/* Drag Handle Area */}
          <TouchableOpacity
            style={styles.dragHandleArea}
            onPress={() => handleSkip()}
            activeOpacity={0.7}
          >
            <View style={styles.dragHandle} />
          </TouchableOpacity>

        <LinearGradient
          colors={isDark
            ? ['rgba(255, 255, 255, 0.15)', 'rgba(30, 30, 30, 0.95)', 'rgba(20, 20, 20, 0.98)']
            : ['rgba(255, 255, 255, 0.8)', 'rgba(255, 255, 255, 0.95)', 'rgba(240, 240, 240, 0.98)']
          }
          style={styles.gradientBackground}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
          locations={[0, 0.05, 1]}
        >
          {checkingCapability ? (
            <View style={styles.successContainer}>
              <Text style={styles.loadingText}>Checking biometric availability...</Text>
            </View>
          ) : setupComplete ? (
            <View style={styles.successContainer}>
              <View style={styles.successIcon}>
                <CheckIcon size={64} color="#34C759" />
              </View>
              <Text style={styles.successTitle}>Biometric Enabled!</Text>
              <Text style={styles.successDescription}>
                Your account is now secured with biometric authentication
              </Text>
            </View>
          ) : biometricCapability.available ? (
            <>
              {/* Biometric Icon */}
              <View style={styles.imageContainer}>
                {biometricCapability.type === 'face' ? (
                  <FaceIdIcon size={80} color={theme.colors.primary} />
                ) : (
                  <FingerprintIcon size={80} color={theme.colors.primary} />
                )}
              </View>

              {/* Title and Description */}
              <View style={styles.textContainer}>
                <Text style={styles.title}>{getBiometricTitle()}</Text>
                <Text style={styles.description}>{getBiometricDescription()}</Text>
              </View>

              {/* Buttons Grid - Side by Side (Skip first, Enable second) */}
              <View style={styles.buttonContainer}>
                <TouchableOpacity
                  onPress={handleSkip}
                  disabled={loading}
                  style={[styles.button, styles.skipButton]}
                >
                  <Text style={styles.skipButtonText}>Skip for now</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  onPress={handleEnableBiometric}
                  disabled={loading || biometricCapability.type === 'none'}
                  style={[styles.button, styles.enableButton]}
                >
                  <LinearGradient
                    colors={isDark
                      ? ['#8B5CF6', '#6366F1']
                      : ['#6366F1', '#8B5CF6']
                    }
                    style={styles.enableButtonGradient}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                  >
                    <Text style={styles.enableButtonText}>
                      {loading ? 'Setting up...' : `Enable ${biometricCapability.type === 'face' ? 'Face ID' : 'Fingerprint'}`}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </>
          ) : null}
        </LinearGradient>
        </Animated.View>
      </GestureDetector>
    </Modal>
  );
};

export default BiometricSetupModal;
