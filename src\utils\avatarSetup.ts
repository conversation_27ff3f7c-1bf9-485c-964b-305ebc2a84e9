/**
 * Avatar Setup Utility
 * Automatically generates and sets avatars for users based on their name and gender
 * Integrates with the setup flow for seamless user experience
 */

import { userService } from '../services/userService';
import logger from '../services/productionLogger';

export interface AutoAvatarSetupOptions {
  firstName?: string;
  lastName?: string;
  email?: string;
  name?: string;
  skipIfExists?: boolean;
}

export interface AvatarSetupResult {
  success: boolean;
  avatarPath?: string;
  detectedGender?: 'male' | 'female' | 'unisex';
  confidence?: 'high' | 'medium' | 'low';
  error?: string;
}

/**
 * Automatically generate and set avatar for user based on their name
 * This function is called during the name setup process
 */
export async function autoSetupUserAvatar(options: AutoAvatarSetupOptions): Promise<AvatarSetupResult> {
  try {
    logger.info('Starting automatic avatar setup', {
      hasFirstName: !!options.firstName,
      hasEmail: !!options.email
    }, 'avatar_setup');

    // Simple avatar generation
    const avatarUrl = await userService.generateAndSetAvatar({
      firstName: options.firstName,
      email: options.email
    });

    logger.info('Avatar setup completed', { avatarUrl }, 'avatar_setup');

    return {
      success: true,
      avatarPath: avatarUrl,
      detectedGender: 'unisex',
      confidence: 'medium'
    };

  } catch (err) {
    logger.error('Avatar setup failed', {
      errorMessage: String(err)
    }, 'avatar_setup');

    return {
      success: false,
      error: 'Failed to setup avatar'
    };
  }
}

/**
 * Generate avatar options for user selection (used in avatar selection modal)
 */
export async function generateAvatarOptionsForUser(options: AutoAvatarSetupOptions): Promise<string[]> {
  try {
    logger.info('Generating avatar options for user selection', {
      hasFirstName: !!options.firstName,
      hasEmail: !!options.email
    }, 'avatar_setup');

    const avatarOptions = await userService.getAvatarOptions({
      firstName: options.firstName,
      lastName: options.lastName,
      email: options.email,
      name: options.name
    });

    logger.info('Avatar options generated for user selection', {
      optionsCount: avatarOptions.length
    }, 'avatar_setup');

    return avatarOptions;

  } catch (error: any) {
    logger.error('Failed to generate avatar options for user', {
      error: error instanceof Error ? error.message : (typeof error === 'string' ? error : JSON.stringify(error)),
      errorStack: error instanceof Error ? error.stack : undefined,
      options
    }, 'avatar_setup');

    // Fallback to simple online avatars
    try {
      const seed = (options.firstName || options.name || options.email?.split('@')[0] || 'default').replace(/[^a-zA-Z0-9]/g, '').toLowerCase();

      const fallbackOptions = [
        `https://api.dicebear.com/7.x/personas/svg?seed=${seed}1&size=200&backgroundColor=b6e3f4,c0aede,d1d4f9&mood=happy`,
        `https://api.dicebear.com/7.x/personas/svg?seed=${seed}2&size=200&backgroundColor=b6e3f4,c0aede,d1d4f9&mood=surprised`,
        `https://api.dicebear.com/7.x/avataaars/svg?seed=${seed}&size=200&backgroundColor=b6e3f4,c0aede,d1d4f9`,
        `https://api.dicebear.com/7.x/adventurer/svg?seed=${seed}&size=200&backgroundColor=b6e3f4,c0aede,d1d4f9`
      ];

      logger.info('Using online avatar fallback options', {
        optionsCount: fallbackOptions.length
      }, 'avatar_setup');

      return fallbackOptions;
    } catch (fallbackError) {
      logger.error('Online avatar fallback failed', {
        error: fallbackError instanceof Error ? fallbackError.message : JSON.stringify(fallbackError)
      }, 'avatar_setup');

      // Ultimate fallback - return simple online avatars
      return [
        'https://api.dicebear.com/7.x/personas/svg?seed=default1&size=200&backgroundColor=b6e3f4,c0aede,d1d4f9',
        'https://api.dicebear.com/7.x/personas/svg?seed=default2&size=200&backgroundColor=b6e3f4,c0aede,d1d4f9',
        'https://api.dicebear.com/7.x/personas/svg?seed=default3&size=200&backgroundColor=b6e3f4,c0aede,d1d4f9'
      ];
    }
  }
}

/**
 * Get gender information for a given name (useful for UI feedback)
 */
export function getGenderInfoForName(firstName: string): {
  gender: 'male' | 'female' | 'unisex';
  confidence: 'high' | 'medium' | 'low';
  source: 'database' | 'pattern' | 'fallback';
} {
  try {
    const genderResult = detectGender(firstName);
    
    logger.debug('Gender detected for name', {
      firstName,
      gender: genderResult.gender,
      confidence: genderResult.confidence,
      source: genderResult.source
    }, 'avatar_setup');

    return genderResult;
  } catch (error) {
    logger.error('Failed to detect gender for name', { firstName, error }, 'avatar_setup');
    
    return {
      gender: 'unisex',
      confidence: 'low',
      source: 'fallback'
    };
  }
}

/**
 * Check if an avatar path is a local avatar
 */
export function isLocalAvatarPath(avatarPath: string): boolean {
  return LocalAvatarService.isLocalAvatar(avatarPath);
}

/**
 * Get the gender from a local avatar path
 */
export function getGenderFromAvatarPath(avatarPath: string): 'male' | 'female' | null {
  return LocalAvatarService.getGenderFromAvatarPath(avatarPath);
}

/**
 * Get avatar require for React Native Image component
 */
export function getAvatarRequire(avatarPath: string): any {
  return LocalAvatarService.getAvatarRequire(avatarPath);
}

export default {
  autoSetupUserAvatar,
  generateAvatarOptionsForUser,
  getGenderInfoForName,
  isLocalAvatarPath,
  getGenderFromAvatarPath,
  getAvatarRequire
};
