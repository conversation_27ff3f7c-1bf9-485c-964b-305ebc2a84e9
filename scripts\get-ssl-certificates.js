#!/usr/bin/env node

/**
 * SSL Certificate Hash Generator
 * This script helps generate SSL certificate hashes for certificate pinning
 */

const https = require('https');
const crypto = require('crypto');

// Domains to check
const domains = [
  'funny-poems-slide.loca.lt',
  'api.vendy.com',
  'staging-api.vendy.com',
  'supabase.co'
];

/**
 * Get SSL certificate information for a domain
 */
function getCertificateInfo(hostname) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: hostname,
      port: 443,
      method: 'GET',
      rejectUnauthorized: false, // We want to get the cert even if it's invalid
    };

    const req = https.request(options, (res) => {
      const cert = res.socket.getPeerCertificate();
      
      if (!cert || Object.keys(cert).length === 0) {
        reject(new Error('No certificate found'));
        return;
      }

      // Get the public key and calculate SHA256 hash
      const publicKeyDer = cert.pubkey;
      const publicKeyHash = crypto.createHash('sha256').update(publicKeyDer).digest('base64');
      
      // Get certificate fingerprint
      const certDer = cert.raw;
      const certHash = crypto.createHash('sha256').update(certDer).digest('hex').toUpperCase();
      
      resolve({
        hostname: hostname,
        subject: cert.subject,
        issuer: cert.issuer,
        validFrom: cert.valid_from,
        validTo: cert.valid_to,
        fingerprint: cert.fingerprint,
        sha256Fingerprint: certHash.match(/.{2}/g).join(':'),
        publicKeyHash: publicKeyHash,
        serialNumber: cert.serialNumber,
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

/**
 * Main function to get certificates for all domains
 */
async function main() {
  console.log('🔐 SSL Certificate Hash Generator for Vendy App');
  console.log('================================================\n');

  for (const domain of domains) {
    try {
      console.log(`🔍 Checking ${domain}...`);
      const certInfo = await getCertificateInfo(domain);
      
      console.log(`✅ Certificate found for ${domain}`);
      console.log(`   Subject: ${certInfo.subject.CN || 'N/A'}`);
      console.log(`   Issuer: ${certInfo.issuer.CN || 'N/A'}`);
      console.log(`   Valid: ${certInfo.validFrom} - ${certInfo.validTo}`);
      console.log(`   SHA256 Fingerprint: ${certInfo.sha256Fingerprint}`);
      console.log(`   Public Key Hash (for pinning): ${certInfo.publicKeyHash}`);
      console.log('');
      
    } catch (error) {
      console.log(`❌ Failed to get certificate for ${domain}: ${error.message}`);
      console.log('');
    }
  }

  console.log('📋 How to use these hashes:');
  console.log('1. Copy the "Public Key Hash" values');
  console.log('2. Update src/config/environment.ts certificateHashes');
  console.log('3. Update src/services/sslPinningService.ts pins array');
  console.log('4. Test the SSL pinning in your app');
  console.log('');
  console.log('⚠️  Important: Certificate hashes change when certificates are renewed!');
  console.log('   Always update your app before certificates expire.');
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { getCertificateInfo };
