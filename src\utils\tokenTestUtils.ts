import logger from '../services/productionLogger';
import { tokenManager } from '../services/tokenManager';
import { apiService } from '../services/apiService';

/**
 * Token Management Test Utilities
 * For debugging and testing token refresh functionality
 */
export class TokenTestUtils {
  /**
   * Test token refresh functionality
   */
  static async testTokenRefresh(): Promise<{
    success: boolean;
    message: string;
    details: any;
  }> {
    try {
      logger.info('🧪 [TOKEN-TEST] Starting token refresh test', null, 'token_test');

      // Check if we can refresh
      const canRefresh = await tokenManager.canRefreshToken();
      if (!canRefresh) {
        return {
          success: false,
          message: 'Cannot refresh token - no refresh token or max attempts exceeded',
          details: {
            refreshAttempts: tokenManager.getRefreshAttempts(),
            isRefreshInProgress: tokenManager.isRefreshInProgress()
          }
        };
      }

      // Attempt refresh
      const refreshResult = await tokenManager.refreshToken();
      
      if (refreshResult) {
        logger.info('✅ [TOKEN-TEST] Token refresh test successful', null, 'token_test');
        return {
          success: true,
          message: 'Token refresh successful',
          details: {
            refreshAttempts: tokenManager.getRefreshAttempts(),
            isRefreshInProgress: tokenManager.isRefreshInProgress()
          }
        };
      } else {
        logger.warn('❌ [TOKEN-TEST] Token refresh test failed', null, 'token_test');
        return {
          success: false,
          message: 'Token refresh failed',
          details: {
            refreshAttempts: tokenManager.getRefreshAttempts(),
            isRefreshInProgress: tokenManager.isRefreshInProgress()
          }
        };
      }

    } catch (error) {
      logger.error('💥 [TOKEN-TEST] Token refresh test error', error, 'token_test');
      return {
        success: false,
        message: `Token refresh test error: ${error.message}`,
        details: { error: error.message }
      };
    }
  }

  /**
   * Test API request with automatic token refresh
   */
  static async testApiRequestWithRefresh(): Promise<{
    success: boolean;
    message: string;
    details: any;
  }> {
    try {
      logger.info('🧪 [TOKEN-TEST] Testing API request with automatic refresh', null, 'token_test');

      // Make a request that requires authentication
      const response = await apiService.get('/auth/token-check');
      
      if (response.status === 200) {
        logger.info('✅ [TOKEN-TEST] API request successful', null, 'token_test');
        return {
          success: true,
          message: 'API request successful',
          details: {
            status: response.status,
            data: response.data
          }
        };
      } else {
        logger.warn('❌ [TOKEN-TEST] API request failed', null, 'token_test');
        return {
          success: false,
          message: 'API request failed',
          details: {
            status: response.status,
            data: response.data
          }
        };
      }

    } catch (error: any) {
      logger.error('💥 [TOKEN-TEST] API request test error', error, 'token_test');
      return {
        success: false,
        message: `API request test error: ${error.message}`,
        details: { 
          error: error.message,
          status: error.status
        }
      };
    }
  }

  /**
   * Get current token manager status
   */
  static getTokenManagerStatus(): {
    refreshAttempts: number;
    isRefreshInProgress: boolean;
    canRefreshToken: Promise<boolean>;
  } {
    return {
      refreshAttempts: tokenManager.getRefreshAttempts(),
      isRefreshInProgress: tokenManager.isRefreshInProgress(),
      canRefreshToken: tokenManager.canRefreshToken()
    };
  }

  /**
   * Force reset token manager state (emergency use only)
   */
  static forceResetTokenManager(): void {
    logger.warn('🚨 [TOKEN-TEST] Force resetting token manager state', null, 'token_test');
    tokenManager.forceResetRefreshState();
  }

  /**
   * Clear all tokens (for testing logout scenarios)
   */
  static async clearAllTokens(): Promise<void> {
    logger.info('🧹 [TOKEN-TEST] Clearing all tokens', null, 'token_test');
    await tokenManager.clearAllTokens();
  }

  /**
   * Simulate token expiration scenario
   */
  static async simulateTokenExpiration(): Promise<{
    success: boolean;
    message: string;
    details: any;
  }> {
    try {
      logger.info('🧪 [TOKEN-TEST] Simulating token expiration scenario', null, 'token_test');

      // Set an invalid token to simulate expiration
      apiService.setAuthToken('invalid_token_for_testing');

      // Try to make an API request
      const response = await apiService.get('/auth/token-check');
      
      // If we get here, the token refresh worked
      logger.info('✅ [TOKEN-TEST] Token expiration simulation successful - refresh worked', null, 'token_test');
      return {
        success: true,
        message: 'Token expiration simulation successful - automatic refresh worked',
        details: {
          status: response.status,
          refreshAttempts: tokenManager.getRefreshAttempts()
        }
      };

    } catch (error: any) {
      // Check if this is expected (no refresh token available)
      if (error.status === 401 && error.message.includes('Session expired')) {
        logger.info('✅ [TOKEN-TEST] Token expiration simulation successful - properly handled', null, 'token_test');
        return {
          success: true,
          message: 'Token expiration simulation successful - properly handled session expiration',
          details: {
            error: error.message,
            status: error.status
          }
        };
      }

      logger.error('💥 [TOKEN-TEST] Token expiration simulation error', error, 'token_test');
      return {
        success: false,
        message: `Token expiration simulation error: ${error.message}`,
        details: { 
          error: error.message,
          status: error.status
        }
      };
    }
  }

  /**
   * Run comprehensive token management tests
   */
  static async runComprehensiveTests(): Promise<{
    success: boolean;
    message: string;
    testResults: any[];
  }> {
    logger.info('🧪 [TOKEN-TEST] Running comprehensive token management tests', null, 'token_test');

    const testResults = [];

    // Test 1: Token Manager Status
    const statusTest = {
      name: 'Token Manager Status',
      result: this.getTokenManagerStatus()
    };
    testResults.push(statusTest);

    // Test 2: Token Refresh
    const refreshTest = {
      name: 'Token Refresh Test',
      result: await this.testTokenRefresh()
    };
    testResults.push(refreshTest);

    // Test 3: API Request with Refresh
    const apiTest = {
      name: 'API Request with Refresh Test',
      result: await this.testApiRequestWithRefresh()
    };
    testResults.push(apiTest);

    // Test 4: Token Expiration Simulation
    const expirationTest = {
      name: 'Token Expiration Simulation',
      result: await this.simulateTokenExpiration()
    };
    testResults.push(expirationTest);

    const successfulTests = testResults.filter(test => test.result.success !== false).length;
    const totalTests = testResults.length;

    const overallSuccess = successfulTests === totalTests;

    logger.info(`🧪 [TOKEN-TEST] Comprehensive tests completed: ${successfulTests}/${totalTests} passed`, {
      overallSuccess,
      testResults
    }, 'token_test');

    return {
      success: overallSuccess,
      message: `Comprehensive token tests completed: ${successfulTests}/${totalTests} passed`,
      testResults
    };
  }
}

// Export for easy access in development
export default TokenTestUtils;

// Global access for debugging (development only)
if (__DEV__) {
  (global as any).TokenTestUtils = TokenTestUtils;
  (global as any).tokenManager = tokenManager;
}
