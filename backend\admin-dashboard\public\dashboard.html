<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vendy Admin Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #f8fafc;
            color: #334155;
        }

        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar */
        .sidebar {
            width: 260px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .sidebar-menu {
            flex: 1;
            list-style: none;
            padding: 20px 0;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .sidebar-menu a:hover,
        .sidebar-menu li.active a {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .sidebar-menu i {
            width: 20px;
            margin-right: 12px;
        }

        .sidebar-footer {
            padding: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logout-btn {
            width: 100%;
            padding: 12px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .logout-btn i {
            margin-right: 8px;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .content-header h1 {
            font-size: 2rem;
            font-weight: 600;
            color: #1e293b;
        }

        .header-actions {
            display: flex;
            gap: 10px;
        }

        /* Buttons */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-outline {
            background: transparent;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-outline:hover {
            background: #667eea;
            color: white;
        }

        .btn-icon {
            padding: 8px;
            background: transparent;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            color: #64748b;
            transition: all 0.3s ease;
        }

        .btn-icon:hover {
            background: #f1f5f9;
            color: #334155;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }

        .stat-content h3 {
            font-size: 2rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 5px;
        }

        .stat-content p {
            color: #64748b;
            font-weight: 500;
        }

        /* Content Sections */
        .content-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .section-header {
            padding: 25px;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
        }

        /* Tables */
        .table-container {
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th,
        .data-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        .data-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #475569;
        }

        .data-table tr:hover {
            background: #f8fafc;
        }

        .loading-row,
        .no-data {
            text-align: center;
            color: #64748b;
            font-style: italic;
        }

        /* Badges */
        .platform-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .platform-android {
            background: #dcfce7;
            color: #166534;
        }

        .platform-ios {
            background: #dbeafe;
            color: #1e40af;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-inactive {
            background: #fee2e2;
            color: #dc2626;
        }

        .mandatory-badge {
            padding: 2px 6px;
            background: #fef3c7;
            color: #d97706;
            border-radius: 4px;
            font-size: 0.7rem;
            font-weight: 600;
            margin-left: 8px;
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            padding: 25px;
        }

        .action-card {
            text-align: center;
            padding: 30px 20px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .action-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
        }

        .action-card i {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 15px;
        }

        .action-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
        }

        .action-card p {
            color: #64748b;
            margin-bottom: 20px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .dashboard-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
            }

            .main-content {
                padding: 20px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .content-header {
                flex-direction: column;
                gap: 15px;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-mobile-alt"></i> Vendy Admin</h2>
            </div>
            <ul class="sidebar-menu">
                <li class="active">
                    <a href="/dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li>
                    <a href="/ota-updates">
                        <i class="fas fa-download"></i>
                        <span>OTA Updates</span>
                    </a>
                </li>
                <li>
                    <a href="/analytics">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analytics</span>
                    </a>
                </li>
                <li>
                    <a href="/settings">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>
            </ul>
            <div class="sidebar-footer">
                <button onclick="logout()" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <header class="content-header">
                <h1>Dashboard Overview</h1>
                <div class="header-actions">
                    <button class="btn btn-primary" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </header>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalUpdates">-</h3>
                        <p>Total Updates</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="activeUpdates">-</h3>
                        <p>Active Updates</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="platforms">-</h3>
                        <p>Platforms</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-hdd"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalSize">-</h3>
                        <p>Total Size</p>
                    </div>
                </div>
            </div>

            <!-- Recent Updates -->
            <div class="content-section">
                <div class="section-header">
                    <h2>Recent Updates</h2>
                    <a href="/ota-updates" class="btn btn-outline">View All</a>
                </div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Version</th>
                                <th>Platform</th>
                                <th>Status</th>
                                <th>Size</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="recentUpdatesTable">
                            <tr>
                                <td colspan="6" class="loading-row">
                                    <i class="fas fa-spinner fa-spin"></i> Loading...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="content-section">
                <div class="section-header">
                    <h2>Quick Actions</h2>
                </div>
                <div class="quick-actions">
                    <div class="action-card">
                        <i class="fas fa-plus"></i>
                        <h3>Deploy Update</h3>
                        <p>Upload and deploy a new OTA update</p>
                        <a href="/ota-updates" class="btn btn-primary">Deploy Now</a>
                    </div>
                    <div class="action-card">
                        <i class="fas fa-chart-line"></i>
                        <h3>View Analytics</h3>
                        <p>Check update deployment statistics</p>
                        <a href="/analytics" class="btn btn-outline">View Analytics</a>
                    </div>
                    <div class="action-card">
                        <i class="fas fa-cog"></i>
                        <h3>System Settings</h3>
                        <p>Configure OTA update settings</p>
                        <a href="/settings" class="btn btn-outline">Settings</a>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="/js/dashboard.js"></script>
    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboardData();
            
            // Auto-refresh every 30 seconds
            setInterval(loadDashboardData, 30000);
        });

        async function loadDashboardData() {
            try {
                const response = await fetch('/api/admin/ota/updates');
                if (!response.ok) {
                    if (response.status === 401) {
                        window.location.href = '/login';
                        return;
                    }
                    throw new Error('Failed to load data');
                }

                const data = await response.json();
                if (data.success) {
                    updateStats(data.data);
                    updateRecentUpdatesTable(data.data.updates);
                }
            } catch (error) {
                console.error('Error loading dashboard data:', error);
                showError('Failed to load dashboard data');
            }
        }

        function updateStats(data) {
            document.getElementById('totalUpdates').textContent = data.updates.length;
            document.getElementById('activeUpdates').textContent = 
                data.updates.filter(u => u.isActive).length;
            document.getElementById('platforms').textContent = 
                [...new Set(data.updates.map(u => u.platform))].length;
            
            const totalSize = data.updates.reduce((sum, u) => sum + (u.size || 0), 0);
            document.getElementById('totalSize').textContent = formatBytes(totalSize);
        }

        function updateRecentUpdatesTable(updates) {
            const tbody = document.getElementById('recentUpdatesTable');
            const recentUpdates = updates
                .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
                .slice(0, 5);

            if (recentUpdates.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="no-data">No updates found</td></tr>';
                return;
            }

            tbody.innerHTML = recentUpdates.map(update => `
                <tr>
                    <td>
                        <strong>${update.version}</strong>
                        <small>(${update.buildNumber})</small>
                    </td>
                    <td>
                        <span class="platform-badge platform-${update.platform}">
                            ${update.platform}
                        </span>
                    </td>
                    <td>
                        <span class="status-badge ${update.isActive ? 'status-active' : 'status-inactive'}">
                            ${update.isActive ? 'Active' : 'Inactive'}
                        </span>
                        ${update.isMandatory ? '<span class="mandatory-badge">Mandatory</span>' : ''}
                    </td>
                    <td>${formatBytes(update.size)}</td>
                    <td>${formatDate(update.createdAt)}</td>
                    <td>
                        <button class="btn-icon" onclick="toggleUpdate('${update.id}', ${!update.isActive})" 
                                title="${update.isActive ? 'Deactivate' : 'Activate'}">
                            <i class="fas fa-${update.isActive ? 'pause' : 'play'}"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        async function toggleUpdate(updateId, activate) {
            try {
                const response = await fetch(`/api/admin/ota/updates/${updateId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ isActive: activate })
                });

                if (response.ok) {
                    showSuccess(`Update ${activate ? 'activated' : 'deactivated'} successfully`);
                    loadDashboardData();
                } else {
                    throw new Error('Failed to update');
                }
            } catch (error) {
                showError('Failed to update status');
            }
        }

        function refreshData() {
            loadDashboardData();
            showSuccess('Data refreshed');
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                fetch('/logout', { method: 'POST' })
                    .then(() => {
                        window.location.href = '/login';
                    });
            }
        }
    </script>
</body>
</html>
