/**
 * Rate Limiting Middleware
 * 
 * Provides configurable rate limiting for API endpoints
 * to prevent abuse and ensure fair usage.
 */

const rateLimit = require('express-rate-limit');
const logger = require('../utils/logger');

/**
 * Create a rate limiter with custom configuration
 * @param {Object} options - Rate limiting options
 * @param {number} options.windowMs - Time window in milliseconds
 * @param {number} options.max - Maximum number of requests per window
 * @param {string} options.message - Custom error message
 * @param {boolean} options.skipSuccessfulRequests - Skip successful requests in count
 * @param {boolean} options.skipFailedRequests - Skip failed requests in count
 * @returns {Function} Express middleware function
 */
const rateLimiter = (options = {}) => {
    const {
        windowMs = 15 * 60 * 1000, // 15 minutes default
        max = 100, // 100 requests per window default
        message = 'Too many requests from this IP, please try again later.',
        skipSuccessfulRequests = false,
        skipFailedRequests = false,
        standardHeaders = true,
        legacyHeaders = false
    } = options;

    return rateLimit({
        windowMs,
        max,
        message: {
            success: false,
            message,
            retryAfter: Math.ceil(windowMs / 1000)
        },
        standardHeaders,
        legacyHeaders,
        skipSuccessfulRequests,
        skipFailedRequests,
        handler: (req, res) => {
            logger.warn('🚫 [RATE_LIMIT] Rate limit exceeded', {
                ip: req.ip,
                method: req.method,
                url: req.originalUrl,
                userAgent: req.get('User-Agent'),
                userId: req.user?.id || 'anonymous'
            });

            res.status(429).json({
                success: false,
                message,
                retryAfter: Math.ceil(windowMs / 1000)
            });
        },
        // onLimitReached is deprecated in express-rate-limit v7
        // Rate limit logging is now handled in the handler function above
    });
};

/**
 * Strict rate limiter for sensitive operations
 */
const strictRateLimiter = rateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 requests per window
    message: 'Too many attempts for this sensitive operation, please try again later.'
});

/**
 * Authentication rate limiter
 */
const authRateLimiter = rateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // 10 auth attempts per window
    message: 'Too many authentication attempts, please try again later.',
    skipSuccessfulRequests: true // Don't count successful logins
});

/**
 * OTP rate limiter
 */
const otpRateLimiter = rateLimiter({
    windowMs: 60 * 1000, // 1 minute
    max: 1, // 1 OTP request per minute
    message: 'Please wait before requesting another OTP.'
});

/**
 * API rate limiter for general endpoints
 */
const apiRateLimiter = rateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000, // 1000 requests per window
    message: 'API rate limit exceeded, please try again later.'
});

/**
 * AI operations rate limiter
 */
const aiRateLimiter = rateLimiter({
    windowMs: 60 * 1000, // 1 minute
    max: 10, // 10 AI requests per minute
    message: 'Too many AI requests, please try again later.'
});

module.exports = {
    rateLimiter,
    strictRateLimiter,
    authRateLimiter,
    otpRateLimiter,
    apiRateLimiter,
    aiRateLimiter
};