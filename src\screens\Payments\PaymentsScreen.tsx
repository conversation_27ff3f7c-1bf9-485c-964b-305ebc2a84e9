import React, { useState, useCallback, useMemo, memo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Alert,
  Platform,
} from 'react-native';
import type { BottomTabScreenProps } from '@react-navigation/bottom-tabs';
import type { BottomTabParamList } from '../../types/navigation';
import { useTheme } from '../../components/ThemeContext';
import { PaymentCardIcon, PlusIcon, ArrowRightIcon } from '../../components/icons';
import logger from '../../services/productionLogger';
import crashReporting from '../../services/crashReportingService';

type Props = BottomTabScreenProps<BottomTabParamList, 'PaymentsTab'>;

interface PaymentMethod {
  id: string;
  type: 'card' | 'bank' | 'wallet';
  name: string;
  details: string;
  isDefault: boolean;
  isActive: boolean;
}

interface QuickAction {
  id: string;
  title: string;
  subtitle: string;
  icon: React.ComponentType<{ size?: number; color?: string }>;
  onPress: () => void;
}

const PaymentsScreen: React.FC<Props> = memo(({ navigation }) => {
  const { theme, isDark } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([
    {
      id: '1',
      type: 'card',
      name: 'Visa •••• 4242',
      details: 'Expires 12/25',
      isDefault: true,
      isActive: true,
    },
    {
      id: '2',
      type: 'bank',
      name: 'GTBank Account',
      details: '•••• 1234',
      isDefault: false,
      isActive: true,
    },
  ]);

  // Memoized handlers for performance
  const handleAddPaymentMethod = useCallback(() => {
    logger.userAction('add_payment_method_tapped', { source: 'payments_screen' });
    crashReporting.recordUserAction('add_payment_method', { source: 'payments_screen' });
    
    Alert.alert(
      'Add Payment Method',
      'Choose how you want to add a payment method',
      [
        { text: 'Add Card', onPress: () => handleAddCard() },
        { text: 'Link Bank Account', onPress: () => handleLinkBank() },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  }, []);

  const handleAddCard = useCallback(() => {
    logger.userAction('add_card_selected', { source: 'payments_screen' });
    // TODO: Navigate to add card screen
    Alert.alert('Add Card', 'Card addition feature coming soon!');
  }, []);

  const handleLinkBank = useCallback(() => {
    logger.userAction('link_bank_selected', { source: 'payments_screen' });
    // TODO: Navigate to bank linking screen
    Alert.alert('Link Bank', 'Bank linking feature coming soon!');
  }, []);

  const handlePaymentMethodPress = useCallback((method: PaymentMethod) => {
    logger.userAction('payment_method_selected', { 
      methodId: method.id, 
      methodType: method.type,
      source: 'payments_screen' 
    });
    
    Alert.alert(
      method.name,
      'What would you like to do?',
      [
        { text: 'Set as Default', onPress: () => handleSetDefault(method.id) },
        { text: 'Remove', onPress: () => handleRemoveMethod(method.id), style: 'destructive' },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  }, []);

  const handleSetDefault = useCallback((methodId: string) => {
    setPaymentMethods(prev => 
      prev.map(method => ({
        ...method,
        isDefault: method.id === methodId,
      }))
    );
    logger.userAction('payment_method_set_default', { methodId });
  }, []);

  const handleRemoveMethod = useCallback((methodId: string) => {
    setPaymentMethods(prev => prev.filter(method => method.id !== methodId));
    logger.userAction('payment_method_removed', { methodId });
  }, []);

  // Memoized quick actions
  const quickActions = useMemo<QuickAction[]>(() => [
    {
      id: 'send_money',
      title: 'Send Money',
      subtitle: 'Transfer to friends & family',
      icon: ArrowRightIcon,
      onPress: () => {
        logger.userAction('send_money_tapped', { source: 'payments_screen' });
        Alert.alert('Send Money', 'Send money feature coming soon!');
      },
    },
    {
      id: 'request_money',
      title: 'Request Money',
      subtitle: 'Ask for payment',
      icon: PlusIcon,
      onPress: () => {
        logger.userAction('request_money_tapped', { source: 'payments_screen' });
        Alert.alert('Request Money', 'Request money feature coming soon!');
      },
    },
  ], []);

  // Memoized styles for performance
  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      paddingHorizontal: 20,
      paddingVertical: 16,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    section: {
      paddingHorizontal: 20,
      paddingVertical: 16,
    },
    sectionTitle: {
      fontSize: 18,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: 12,
    },
    quickActionsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    quickActionCard: {
      flex: 1,
      backgroundColor: theme.colors.card,
      borderRadius: 12,
      padding: 16,
      marginHorizontal: 4,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    quickActionTitle: {
      fontSize: 14,
      fontWeight: '600',
      color: theme.colors.text,
      marginTop: 8,
      textAlign: 'center',
    },
    quickActionSubtitle: {
      fontSize: 12,
      color: theme.colors.muted,
      marginTop: 4,
      textAlign: 'center',
    },
    paymentMethodCard: {
      backgroundColor: theme.colors.card,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    paymentMethodInfo: {
      flex: 1,
      marginLeft: 12,
    },
    paymentMethodName: {
      fontSize: 16,
      fontWeight: '600',
      color: theme.colors.text,
    },
    paymentMethodDetails: {
      fontSize: 14,
      color: theme.colors.muted,
      marginTop: 2,
    },
    defaultBadge: {
      backgroundColor: theme.colors.primary,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 12,
      marginTop: 4,
    },
    defaultBadgeText: {
      fontSize: 10,
      color: '#FFFFFF',
      fontWeight: '600',
    },
    addButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 12,
      padding: 16,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      marginTop: 8,
    },
    addButtonText: {
      color: '#FFFFFF',
      fontSize: 16,
      fontWeight: '600',
      marginLeft: 8,
    },
  }), [theme, isDark]);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Payments</Text>
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsContainer}>
            {quickActions.map((action) => (
              <TouchableOpacity
                key={action.id}
                style={styles.quickActionCard}
                onPress={action.onPress}
                activeOpacity={0.7}
              >
                <action.icon size={24} color={theme.colors.primary} />
                <Text style={styles.quickActionTitle}>{action.title}</Text>
                <Text style={styles.quickActionSubtitle}>{action.subtitle}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Payment Methods */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Payment Methods</Text>
          
          {paymentMethods.map((method) => (
            <TouchableOpacity
              key={method.id}
              style={styles.paymentMethodCard}
              onPress={() => handlePaymentMethodPress(method)}
              activeOpacity={0.7}
            >
              <PaymentCardIcon size={24} color={theme.colors.primary} />
              <View style={styles.paymentMethodInfo}>
                <Text style={styles.paymentMethodName}>{method.name}</Text>
                <Text style={styles.paymentMethodDetails}>{method.details}</Text>
                {method.isDefault && (
                  <View style={styles.defaultBadge}>
                    <Text style={styles.defaultBadgeText}>DEFAULT</Text>
                  </View>
                )}
              </View>
              <ArrowRightIcon size={20} color={theme.colors.muted} />
            </TouchableOpacity>
          ))}

          <TouchableOpacity
            style={styles.addButton}
            onPress={handleAddPaymentMethod}
            activeOpacity={0.8}
          >
            <PlusIcon size={20} color="#FFFFFF" />
            <Text style={styles.addButtonText}>Add Payment Method</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
});

PaymentsScreen.displayName = 'PaymentsScreen';

export default PaymentsScreen;
