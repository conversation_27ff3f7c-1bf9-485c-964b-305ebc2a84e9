import CryptoJS from 'crypto-js';
import { Platform } from 'react-native';
import { ENV_CONFIG, SIGNING_CONFIG } from '../config/environment';
import productionLogger from './productionLogger';

interface SignedRequest {
  headers: Record<string, string>;
  timestamp: number;
  nonce: string;
  signature: string;
}

class RequestSigningService {
  private readonly signingKey: string;

  constructor() {
    // In production, this should come from a secure configuration
    // For now, we'll derive it from app and device information
    this.signingKey = this.generateSigningKey();
  }

  private generateSigningKey(): string {
    const appData = `VendyApp-v1.0.0-${Platform.OS}-${Platform.Version}`;
    return CryptoJS.SHA256(appData).toString();
  }

  /**
   * Generate a cryptographically secure nonce
   */
  private generateNonce(): string {
    const timestamp = Date.now().toString();
    const random = CryptoJS.lib.WordArray.random(16).toString();
    return CryptoJS.SHA256(timestamp + random).toString().substring(0, 16);
  }

  /**
   * Create HMAC signature for request
   */
  private createSignature(
    method: string,
    path: string,
    body: string,
    timestamp: number,
    nonce: string
  ): string {
    const message = [
      method.toUpperCase(),
      path,
      body || '',
      timestamp.toString(),
      nonce,
      SIGNING_CONFIG.issuer,
      SIGNING_CONFIG.audience,
    ].join('|');

    return CryptoJS.HmacSHA256(message, this.signingKey).toString();
  }

  /**
   * Sign an API request
   */
  signRequest(
    method: string,
    url: string,
    body?: string,
    additionalHeaders?: Record<string, string>
  ): SignedRequest {
    if (!ENV_CONFIG.ENABLE_REQUEST_SIGNING) {
      return {
        headers: additionalHeaders || {},
        timestamp: Date.now(),
        nonce: '',
        signature: '',
      };
    }

    const timestamp = Date.now();
    const nonce = this.generateNonce();
    
    // Extract path from URL
    const urlObj = new URL(url);
    const path = urlObj.pathname + urlObj.search;
    
    const signature = this.createSignature(
      method,
      path,
      body || '',
      timestamp,
      nonce
    );

    const signedHeaders: Record<string, string> = {
      ...additionalHeaders,
      'X-Timestamp': timestamp.toString(),
      'X-Nonce': nonce,
      'X-Signature': signature,
      'X-Signature-Algorithm': SIGNING_CONFIG.algorithm,
      'X-Issuer': SIGNING_CONFIG.issuer,
      'X-Audience': SIGNING_CONFIG.audience,
    };

    return {
      headers: signedHeaders,
      timestamp,
      nonce,
      signature,
    };
  }

  /**
   * Verify request signature (for testing purposes)
   */
  verifySignature(
    method: string,
    path: string,
    body: string,
    timestamp: number,
    nonce: string,
    signature: string
  ): boolean {
    if (!ENV_CONFIG.ENABLE_REQUEST_SIGNING) {
      return true;
    }

    // Check timestamp tolerance (prevent replay attacks)
    const now = Date.now();
    const clockTolerance = SIGNING_CONFIG.clockTolerance * 1000; // Convert to milliseconds
    
    if (Math.abs(now - timestamp) > clockTolerance) {
productionLogger.warn('SIGNING', 'Request timestamp outside tolerance', { timestamp, now, clockTolerance });
      return false;
    }

    const expectedSignature = this.createSignature(method, path, body, timestamp, nonce);
    return signature === expectedSignature;
  }

  /**
   * Generate device fingerprint for additional security
   */
  generateDeviceFingerprint(): string {
    const deviceInfo = {
      platform: Platform.OS,
      version: Platform.Version,
      // Add more device-specific data as needed
      userAgent: 'VendyMobileApp/1.0.0',
      language: 'en', // Could get from device locale
    };

    const fingerprintData = Object.values(deviceInfo).join('|');
    return CryptoJS.SHA256(fingerprintData).toString().substring(0, 32);
  }

  /**
   * Create integrity hash for sensitive data
   */
  createIntegrityHash(data: any): string {
    const dataString = typeof data === 'string' ? data : JSON.stringify(data);
    return CryptoJS.SHA256(dataString).toString();
  }

  /**
   * Verify integrity hash
   */
  verifyIntegrityHash(data: any, expectedHash: string): boolean {
    const actualHash = this.createIntegrityHash(data);
    return actualHash === expectedHash;
  }
}

export const requestSigningService = new RequestSigningService();
export default requestSigningService;
