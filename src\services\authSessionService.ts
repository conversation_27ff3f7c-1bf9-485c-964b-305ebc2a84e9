import { GoogleSignin } from '@react-native-google-signin/google-signin';
import AsyncStorage from '@react-native-async-storage/async-storage';
import googleAuthService from './googleAuthService';
import googleAccountService from './googleAccountService';
import logger from './productionLogger';
import secureStorage from './secureStorageService';
import ApiService from './apiService';

interface AuthSession {
  isAuthenticated: boolean;
  user?: any;
  token?: string;
  refreshToken?: string;
  expiresAt?: number;
  setupStatus?: {
    hasPinSetup: boolean;
    hasProfileSetup: boolean;
    hasBiometricSetup: boolean;
    setupComplete: boolean;
  };
}

interface UserSetupState {
  hasPinSetup: boolean;
  hasProfileSetup: boolean;
  hasBiometricSetup: boolean;
  setupComplete: boolean;
  firstName?: string;
  email?: string;
  userId?: string;
  lastUpdated: number;
}

class AuthSessionService {
  private readonly SESSION_KEY = 'auth_session';
  private readonly TOKEN_KEY = 'auth_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private readonly USER_SETUP_KEY = 'user_setup_state';
  private readonly SETUP_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Check if user has an existing valid session on app startup
   */
  async checkExistingSession(): Promise<AuthSession> {
    try {
      logger.info('🔍 [AUTH-SESSION] Checking for existing authentication session...', null, 'auth');

      // Get stored tokens
      const storedTokens = await this.getStoredTokens();
      if (!storedTokens.accessToken) {
        logger.info('❌ [AUTH-SESSION] No stored access token found', null, 'auth');
        return { isAuthenticated: false };
      }

      logger.info('✅ [AUTH-SESSION] Found stored access token, validating...', null, 'auth');

      // Validate token with backend first
      const tokenValidationResult = await this.validateTokenWithBackend(storedTokens.accessToken);
      if (!tokenValidationResult.isValid) {
        logger.warn('❌ [AUTH-SESSION] Token validation failed', {
          reason: tokenValidationResult.reason,
          isBlacklisted: tokenValidationResult.isBlacklisted
        }, 'auth');

        // If token is blacklisted, force reset authentication completely
        if (tokenValidationResult.isBlacklisted) {
          logger.warn('🚫 [AUTH-SESSION] Token is blacklisted - forcing complete authentication reset', null, 'auth');
          await this.forceResetAuthentication();
          return { isAuthenticated: false };
        }

        // Try to refresh token using centralized TokenManager (only if not blacklisted)
        if (storedTokens.refreshToken) {
          logger.info('🔄 [AUTH-SESSION] Attempting token refresh via TokenManager', null, 'auth');

          const { tokenManager } = await import('./tokenManager');
          const refreshed = await tokenManager.refreshToken();

          if (refreshed) {
            logger.info('✅ [AUTH-SESSION] Token refresh successful', null, 'auth');

            // Get the new tokens from storage
            const newTokens = await this.getStoredTokens();
            if (newTokens.accessToken) {
              storedTokens.accessToken = newTokens.accessToken;
              storedTokens.refreshToken = newTokens.refreshToken;

              // Validate the new token
              const newTokenValidation = await this.validateTokenWithBackend(storedTokens.accessToken);
              if (!newTokenValidation.isValid) {
                logger.error('❌ [AUTH-SESSION] Refreshed token is also invalid - clearing tokens', {
                  reason: newTokenValidation.reason
                }, 'auth');
                await this.clearStoredTokens();
                return { isAuthenticated: false };
              }
            } else {
              logger.error('❌ [AUTH-SESSION] No new access token found after refresh', null, 'auth');
              await this.clearStoredTokens();
              return { isAuthenticated: false };
            }
          } else {
            logger.error('❌ [AUTH-SESSION] Token refresh failed via TokenManager', null, 'auth');
            // TokenManager already handles token clearing on failure
            return { isAuthenticated: false };
          }
        } else {
          logger.error('❌ [AUTH-SESSION] No refresh token available for recovery', null, 'auth');
          await this.clearStoredTokens();
          return { isAuthenticated: false };
        }
      }

      logger.info('✅ [AUTH-SESSION] Token is valid, fetching setup status...', null, 'auth');

      // Set the token for API calls
      const { default: ApiService } = await import('./apiService');
      ApiService.setAuthToken(storedTokens.accessToken);

      // Get setup status from backend
      let setupStatus = null;
      let userInfo = null;

      try {
        const { setupService } = await import('./setupService');
        const setupResponse = await setupService.getSetupStatus();
        setupStatus = setupResponse.setupStatus;
        userInfo = setupResponse.user;

        logger.info('✅ [AUTH-SESSION] Setup status retrieved', {
          hasProfileSetup: setupStatus?.hasProfileSetup,
          hasPinSetup: setupStatus?.hasPinSetup,
          setupComplete: setupStatus?.setupComplete,
          userFirstName: userInfo?.firstName
        }, 'auth');

        // Cache the setup status
        await this.cacheSetupStatus(setupStatus, userInfo?.id);

      } catch (setupError) {
        logger.error('❌ [AUTH-SESSION] Failed to fetch setup status', setupError, 'auth');

        // For newly verified users, create default setup status
        setupStatus = {
          hasProfileSetup: false,
          hasPinSetup: false,
          hasBiometricSetup: false,
          isEmailVerified: true,
          isPhoneVerified: false,
          setupComplete: false,
        };

        userInfo = {
          id: '',
          firstName: '',
          lastName: '',
          email: '',
          phoneNumber: '',
          isEmailVerified: true,
          isPhoneVerified: false,
          picture: '',
        };
      }

      return {
        isAuthenticated: true,
        token: storedTokens.accessToken,
        refreshToken: storedTokens.refreshToken,
        setupStatus,
        user: userInfo,
        expiresAt: Date.now() + (24 * 60 * 60 * 1000),
      };

    } catch (error) {
      logger.error('❌ [AUTH-SESSION] Session check failed', error, 'auth');
      return { isAuthenticated: false };
    }
  }

  /**
   * Store authentication session with enhanced security
   */
  async storeSession(session: AuthSession): Promise<void> {
    try {
      // Store session data in AsyncStorage
      await AsyncStorage.setItem(this.SESSION_KEY, JSON.stringify(session));

      // Store tokens securely using secure storage service
      if (session.token) {
        await secureStorage.storeAuthTokens(
          session.token,
          session.refreshToken || ''
        );
        // Also store in AsyncStorage for backward compatibility
        await AsyncStorage.setItem(this.TOKEN_KEY, session.token);
        if (session.refreshToken) {
          await AsyncStorage.setItem(this.REFRESH_TOKEN_KEY, session.refreshToken);
        }
      }

      // Cache setup status if available
      if (session.setupStatus) {
        await this.cacheSetupStatus(session.setupStatus, session.user?.id);
      }

      logger.info('Session stored successfully', null, 'auth');
    } catch (error) {
      logger.error('Error storing session:', error, 'auth');
    }
  }

  /**
   * Get stored authentication tokens with improved error handling
   */
  async getStoredTokens(): Promise<{ accessToken?: string; refreshToken?: string }> {
    try {
      // Try secure storage first
      const secureTokens = await secureStorage.getAuthTokens();
      if (secureTokens.accessToken && secureTokens.refreshToken) {
        logger.debug('Tokens retrieved from secure storage', null, 'auth');
        return {
          accessToken: secureTokens.accessToken,
          refreshToken: secureTokens.refreshToken,
        };
      }

      // If secure storage didn't have complete tokens, try legacy AsyncStorage
      try {
        const accessToken = await AsyncStorage.getItem(this.TOKEN_KEY);
        const refreshToken = await AsyncStorage.getItem(this.REFRESH_TOKEN_KEY);

        if (accessToken && refreshToken) {
          logger.debug('Tokens retrieved from legacy AsyncStorage', null, 'auth');
          return {
            accessToken,
            refreshToken,
          };
        }
      } catch (asyncStorageError) {
        logger.debug('Legacy AsyncStorage token retrieval failed', asyncStorageError, 'auth');
      }

      // No tokens found anywhere
      logger.debug('No authentication tokens found in any storage location', null, 'auth');
      return {};
    } catch (error) {
      // Convert error to string for consistent checking
      const errorMessage = error instanceof Error ? error.message : String(error);

      // Only log as error if it's an unexpected error, not just "no tokens found"
      if (errorMessage &&
          !errorMessage.includes('not found') &&
          !errorMessage.includes('No such key') &&
          !errorMessage.includes('UserCancel') &&
          !errorMessage.includes('UserFallback')) {
        logger.error('Unexpected error getting stored tokens:', {
          error: errorMessage,
          type: typeof error,
          stack: error instanceof Error ? error.stack : undefined
        }, 'auth');
      } else {
        logger.debug('No stored tokens available (expected on first launch)', { reason: errorMessage }, 'auth');
      }
      return {};
    }
  }

  /**
   * Validate token with backend using a more reliable endpoint
   */
  async validateTokenWithBackend(token: string): Promise<{
    isValid: boolean;
    isBlacklisted?: boolean;
    reason?: string;
    accountLocked?: boolean;
  }> {
    try {
      logger.debug('🔍 [AUTH-SESSION] Validating token with backend...', null, 'auth');

      // Set the token temporarily for the validation request
      const originalToken = ApiService.getAuthToken();
      ApiService.setAuthToken(token);

      try {
        // First try the dedicated token check endpoint if available
        const response = await ApiService.get('/auth/token-check', {}, {
          timeout: 10000, // 10 second timeout
          retries: 1
        });

        // Restore original token
        if (originalToken) {
          ApiService.setAuthToken(originalToken);
        }

        // CRITICAL: Accept both success (200) and locked account (423) as valid tokens
        const isValid = response.status === 200 || response.data?.status === 'success';

        // Check if account is locked but token is valid
        if (response.data?.user?.isLocked) {
          logger.info('✅ [AUTH-SESSION] Token valid for locked account - preserving auth state', {
            userId: response.data.user.id,
            lockUntil: response.data.user.lockUntil
          }, 'auth');
          return { isValid: true, accountLocked: true };
        }

        logger.debug(`✅ [AUTH-SESSION] Token validation result: ${isValid}`, null, 'auth');
        return { isValid };

      } catch (tokenCheckError: any) {
        // CRITICAL: Handle 423 (Account Locked) as valid token
        if (tokenCheckError.status === 423 && tokenCheckError.response?.code === 'ACCOUNT_LOCKED') {
          logger.info('✅ [AUTH-SESSION] Token valid for locked account (423) - preserving auth state', {
            userId: tokenCheckError.response?.data?.user?.id,
            lockUntil: tokenCheckError.response?.data?.user?.lockUntil
          }, 'auth');

          // Restore original token
          if (originalToken) {
            ApiService.setAuthToken(originalToken);
          }

          return { isValid: true, accountLocked: true }; // Token is valid even if account is locked
        }

        // Check for blacklisted token (401 with specific error message)
        if (tokenCheckError.status === 401) {
          const errorMessage = tokenCheckError.response?.data?.message || tokenCheckError.message || '';
          const responseText = JSON.stringify(tokenCheckError.response || {}).toLowerCase();

          if (errorMessage.toLowerCase().includes('blacklisted') ||
              errorMessage.toLowerCase().includes('revoked') ||
              errorMessage.toLowerCase().includes('token has been revoked') ||
              responseText.includes('blacklisted') ||
              responseText.includes('revoked')) {
            logger.warn('🚫 [AUTH-SESSION] Token is blacklisted/revoked', {
              status: tokenCheckError.status,
              message: errorMessage,
              endpoint: '/auth/token-check'
            }, 'auth');

            // Restore original token
            if (originalToken) {
              ApiService.setAuthToken(originalToken);
            }

            return {
              isValid: false,
              isBlacklisted: true,
              reason: 'Token has been blacklisted/revoked'
            };
          }
        }

        logger.debug('Token check endpoint failed, trying setup status endpoint', tokenCheckError, 'auth');

        // Fallback to setup status endpoint
        try {
          const response = await ApiService.get('/setup/status', {}, {
            timeout: 10000,
            retries: 1
          });

          // Restore original token
          if (originalToken) {
            ApiService.setAuthToken(originalToken);
          }

          const isValid = response.status === 200 || response.data?.status === 'success';
          logger.debug(`✅ [AUTH-SESSION] Token validation via setup status: ${isValid}`, null, 'auth');
          return { isValid };

        } catch (setupError: any) {
          logger.debug('Setup status endpoint also failed', setupError, 'auth');

          // Restore original token
          if (originalToken) {
            ApiService.setAuthToken(originalToken);
          }

          // Check if setup endpoint also indicates blacklisted token
          if (setupError.status === 401) {
            const errorMessage = setupError.response?.data?.message || setupError.message || '';
            const responseText = JSON.stringify(setupError.response || {}).toLowerCase();

            if (errorMessage.toLowerCase().includes('blacklisted') ||
                errorMessage.toLowerCase().includes('revoked') ||
                errorMessage.toLowerCase().includes('token has been revoked') ||
                responseText.includes('blacklisted') ||
                responseText.includes('revoked')) {
              return {
                isValid: false,
                isBlacklisted: true,
                reason: 'Token has been blacklisted/revoked (via setup endpoint)'
              };
            }
          }

          // If both token-check and setup endpoints fail with 401, likely blacklisted
          if (tokenCheckError.status === 401 && setupError.status === 401) {
            logger.warn('🚫 [AUTH-SESSION] Both validation endpoints failed with 401 - likely blacklisted token', {
              tokenCheckStatus: tokenCheckError.status,
              setupStatus: setupError.status
            }, 'auth');
            return {
              isValid: false,
              isBlacklisted: true,
              reason: 'Both validation endpoints failed with 401 - likely blacklisted'
            };
          }

          return { isValid: false, reason: 'All validation endpoints failed' };
        }
      }
    } catch (error: any) {
      logger.warn('❌ [AUTH-SESSION] Token validation failed completely', error, 'auth');
      return { isValid: false, reason: 'Complete validation failure' };
    }
  }

  /**
   * Get stored auth token (legacy method for compatibility)
   */
  async getStoredToken(): Promise<string | null> {
    try {
      const tokens = await this.getStoredTokens();
      return tokens.accessToken || null;
    } catch (error) {
      logger.error('Error getting stored token:', error, 'auth');
      return null;
    }
  }

  /**
   * Debug method to check authentication state
   */
  async debugAuthState(): Promise<void> {
    try {
      logger.info('🔍 [DEBUG] Checking authentication state...', null, 'auth');

      const tokens = await this.getStoredTokens();
      logger.info('🔍 [DEBUG] Stored tokens:', {
        hasAccessToken: !!tokens.accessToken,
        hasRefreshToken: !!tokens.refreshToken,
        accessTokenLength: tokens.accessToken?.length || 0
      }, 'auth');

      if (tokens.accessToken) {
        const validationResult = await this.validateTokenWithBackend(tokens.accessToken);
        logger.info('🔍 [DEBUG] Token validation result:', {
          isValid: validationResult.isValid,
          isBlacklisted: validationResult.isBlacklisted,
          reason: validationResult.reason
        }, 'auth');

        if (validationResult.isValid) {
          try {
            const { setupService } = await import('./setupService');
            const setupStatus = await setupService.getComprehensiveSetupStatus();
            logger.info('🔍 [DEBUG] Setup status:', setupStatus, 'auth');
          } catch (setupError) {
            logger.error('🔍 [DEBUG] Setup status error:', setupError, 'auth');
          }
        }
      }
    } catch (error) {
      logger.error('🔍 [DEBUG] Auth state check failed:', error, 'auth');
    }
  }

  /**
   * Debug method to manually force reset authentication
   * Can be called from console for testing
   */
  async debugForceReset(): Promise<void> {
    try {
      logger.warn('🔧 [DEBUG] Manually forcing authentication reset...', null, 'auth');
      await this.forceResetAuthentication();
      logger.info('✅ [DEBUG] Manual authentication reset completed', null, 'auth');
    } catch (error) {
      logger.error('❌ [DEBUG] Manual authentication reset failed:', error, 'auth');
    }
  }

  /**
   * Get stored session (legacy method for compatibility)
   */
  async getStoredSession(): Promise<AuthSession | null> {
    try {
      const sessionData = await AsyncStorage.getItem(this.SESSION_KEY);
      if (sessionData) {
        return JSON.parse(sessionData);
      }
      return null;
    } catch (error) {
      logger.error('Error getting stored session:', error, 'auth');
      return null;
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshAccessToken(refreshToken: string): Promise<{
    success: boolean;
    accessToken?: string;
    refreshToken?: string;
    error?: string;
  }> {
    try {
      logger.info('Attempting to refresh access token', null, 'auth');

      const response = await ApiService.post('/auth/refresh-token', {
        refreshToken: refreshToken,
      }, {
        skipAuth: true, // Don't use auth for refresh endpoint
        timeout: 10000,
        retries: 1 // Only retry once for refresh
      });

      if (response.data?.accessToken) {
        // Store new tokens
        await secureStorage.storeAuthTokens(
          response.data.accessToken,
          response.data.refreshToken || refreshToken
        );

        // Update AsyncStorage as well
        await AsyncStorage.setItem(this.TOKEN_KEY, response.data.accessToken);
        if (response.data.refreshToken) {
          await AsyncStorage.setItem(this.REFRESH_TOKEN_KEY, response.data.refreshToken);
        }

        logger.info('Token refresh successful', null, 'auth');
        return {
          success: true,
          accessToken: response.data.accessToken,
          refreshToken: response.data.refreshToken || refreshToken,
        };
      }

      logger.warn('Token refresh failed: No access token in response', null, 'auth');
      return { success: false, error: 'No access token in response' };
    } catch (error: any) {
      logger.error('Token refresh failed', error, 'auth');

      // Check for specific error types
      if (error.status === 401) {
        // Refresh token is invalid/expired/blacklisted
        logger.warn('Refresh token is invalid or expired, clearing tokens', null, 'auth');
        await this.clearStoredTokens();
        return { success: false, error: 'Refresh token invalid or expired' };
      }

      if (error.status === 403) {
        // Refresh token is blacklisted/revoked
        logger.warn('Refresh token is blacklisted or revoked, clearing tokens', null, 'auth');
        await this.clearStoredTokens();
        return { success: false, error: 'Refresh token blacklisted or revoked' };
      }

      return { success: false, error: error.message || 'Unknown refresh error' };
    }
  }

  /**
   * Get cached setup status to avoid unnecessary API calls
   */
  async getCachedSetupStatus(): Promise<Pick<UserSetupState, 'hasPinSetup' | 'hasProfileSetup' | 'hasBiometricSetup' | 'setupComplete'> | undefined> {
    try {
      const cachedData = await AsyncStorage.getItem(this.USER_SETUP_KEY);
      if (cachedData) {
        const setupState: UserSetupState = JSON.parse(cachedData);

        // Check if cache is still valid (5 minutes)
        if (Date.now() - setupState.lastUpdated < this.SETUP_CACHE_DURATION) {
          logger.debug('Using cached setup status', null, 'auth');
          return {
            hasPinSetup: setupState.hasPinSetup,
            hasProfileSetup: setupState.hasProfileSetup,
            hasBiometricSetup: setupState.hasBiometricSetup,
            setupComplete: setupState.setupComplete,
          };
        } else {
          logger.debug('Setup status cache expired', null, 'auth');
        }
      }

      // Cache miss or expired - fetch from API
      return await this.fetchAndCacheSetupStatus();
    } catch (error) {
      logger.error('Error getting cached setup status', error, 'auth');
      return undefined;
    }
  }

  /**
   * Fetch setup status from API and cache it
   */
  async fetchAndCacheSetupStatus(): Promise<Pick<UserSetupState, 'hasPinSetup' | 'hasProfileSetup' | 'hasBiometricSetup' | 'setupComplete'> | undefined> {
    try {
      logger.debug('Fetching setup status from API', null, 'auth');
      const response = await ApiService.get('/setup/status');

      if (response.data?.setupStatus) {
        const setupStatus = response.data.setupStatus;

        // Cache the setup status
        await this.cacheSetupStatus(setupStatus, response.data.user?.id);

        return setupStatus;
      }
    } catch (error) {
      logger.warn('Failed to fetch setup status from API', error, 'auth');
    }

    return undefined;
  }

  /**
   * Cache setup status for faster subsequent checks
   */
  async cacheSetupStatus(setupStatus: any, userId?: string): Promise<void> {
    try {
      const setupState: UserSetupState = {
        hasPinSetup: setupStatus.hasPinSetup || false,
        hasProfileSetup: setupStatus.hasProfileSetup || false,
        hasBiometricSetup: setupStatus.hasBiometricSetup || false,
        setupComplete: setupStatus.setupComplete || false,
        firstName: setupStatus.firstName,
        email: setupStatus.email,
        userId: userId,
        lastUpdated: Date.now(),
      };

      await AsyncStorage.setItem(this.USER_SETUP_KEY, JSON.stringify(setupState));
      logger.debug('Setup status cached successfully', null, 'auth');
    } catch (error) {
      logger.error('Error caching setup status', error, 'auth');
    }
  }

  /**
   * Clear all stored tokens and session data
   */
  async clearStoredTokens(): Promise<void> {
    try {
      logger.warn('🧹 [AUTH-SESSION] Clearing all stored tokens and session data', null, 'auth');

      // Clear secure storage
      await secureStorage.clearAuthTokens();

      // Clear AsyncStorage
      await AsyncStorage.removeItem(this.TOKEN_KEY);
      await AsyncStorage.removeItem(this.REFRESH_TOKEN_KEY);
      await AsyncStorage.removeItem(this.SESSION_KEY);
      await AsyncStorage.removeItem(this.USER_SETUP_KEY);

      // Clear API service token
      ApiService.setAuthToken(null);

      // Clear token manager state
      const { tokenManager } = await import('./tokenManager');
      await tokenManager.clearAllTokens();

      logger.info('✅ [AUTH-SESSION] All tokens and session data cleared successfully', null, 'auth');
    } catch (error) {
      logger.error('❌ [AUTH-SESSION] Error clearing stored tokens', error, 'auth');
    }
  }

  /**
   * Force clear all authentication data and reset to unauthenticated state
   * Use this when tokens are blacklisted or corrupted
   */
  async forceResetAuthentication(): Promise<void> {
    try {
      logger.warn('🔄 [AUTH-SESSION] Force resetting authentication state', null, 'auth');

      // Clear all tokens and session data
      await this.clearStoredTokens();

      // Sign out from Google
      try {
        await GoogleSignin.signOut();
      } catch (googleError) {
        logger.warn('Google sign out failed during force reset', googleError, 'auth');
      }

      // Clear cached accounts
      try {
        await googleAccountService.clearCache();
      } catch (cacheError) {
        logger.warn('Cache clear failed during force reset', cacheError, 'auth');
      }

      logger.info('✅ [AUTH-SESSION] Authentication state force reset completed', null, 'auth');
    } catch (error) {
      logger.error('❌ [AUTH-SESSION] Error during force authentication reset', error, 'auth');
    }
  }

  /**
   * Clear authentication session (enhanced version)
   */
  async clearSession(): Promise<void> {
    await this.clearStoredTokens();
  }

  /**
   * Determine the appropriate navigation destination for authenticated users
   */
  getNavigationDestination(setupStatus?: UserSetupState, userInfo?: any): {
    screen: string;
    params?: any;
  } {
    logger.info('🔍 [AUTH-SESSION] Determining navigation destination', {
      hasSetupStatus: !!setupStatus,
      hasProfileSetup: setupStatus?.hasProfileSetup,
      hasPinSetup: setupStatus?.hasPinSetup,
      userFirstName: userInfo?.firstName
    }, 'navigation');

    if (!setupStatus) {
      logger.info('➡️ [AUTH-SESSION] No setup status, routing to Startup', null, 'navigation');
      return { screen: 'Startup' };
    }

    // Check if user has completed name setup
    const hasName = userInfo?.firstName && userInfo.firstName.trim() !== '';
    const hasProfileSetup = setupStatus.hasProfileSetup || hasName;

    if (!hasProfileSetup) {
      logger.info('➡️ [AUTH-SESSION] Profile not setup, routing to NameSetup', null, 'navigation');
      return {
        screen: 'NameSetup',
        params: { userData: userInfo }
      };
    }

    // Check PIN setup
    if (!setupStatus.hasPinSetup) {
      logger.info('➡️ [AUTH-SESSION] PIN not setup, routing to PinSetup', null, 'navigation');
      return {
        screen: 'PinSetup',
        params: { userData: userInfo }
      };
    }

    // User has both profile and PIN setup - require PIN verification
    logger.info('➡️ [AUTH-SESSION] Setup complete, routing to PinVerification', null, 'navigation');
    return {
      screen: 'PinVerification',
      params: { user: userInfo }
    };
  }

  /**
   * Sign out and clear all session data
   */
  async signOut(): Promise<void> {
    try {
      // Sign out from Google
      await GoogleSignin.signOut();

      // Clear cached accounts
      await googleAccountService.clearCache();

      // Clear session
      await this.clearSession();

      logger.info('✅ Complete sign out successful', 'auth');
    } catch (error) {
      logger.error('Error during sign out:', error, 'auth');
      // Still clear local data even if Google sign out fails
      await this.clearSession();
    }
  }

  /**
   * Check if user is currently signed in with enhanced token validation
   */
  async isSignedIn(): Promise<boolean> {
    try {
      // Check stored tokens first
      const tokens = await this.getStoredTokens();
      if (tokens.accessToken) {
        const validationResult = await this.validateTokenWithBackend(tokens.accessToken);
        if (validationResult.isValid) {
          return true;
        }
      }

      // Fallback to Google session check
      const currentUser = GoogleSignin.getCurrentUser();
      return currentUser !== null;
    } catch (error) {
      logger.error('Error checking sign-in status:', error, 'auth');
      return false;
    }
  }
}

const authSessionService = new AuthSessionService();

// Make debug methods available globally for testing
if (__DEV__) {
  (global as any).debugAuthState = () => authSessionService.debugAuthState();
  (global as any).debugForceReset = () => authSessionService.debugForceReset();
}

export default authSessionService;