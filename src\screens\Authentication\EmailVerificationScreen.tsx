"use client"

import { useState, useRef, useEffect, memo, useCallback, useMemo } from "react"
import { View, Text, StyleSheet, TouchableOpacity, StatusBar, SafeAreaView, TextInput, Animated, Alert, AppState, ImageBackground } from "react-native"
import ReactNativeHapticFeedback from "react-native-haptic-feedback"
import GlassyBox from "../../components/GlassyBox"
import Clipboard from '@react-native-clipboard/clipboard'
import type { NativeStackScreenProps } from "@react-navigation/native-stack"
import type { RootStackParamList } from "../../types/navigation"
import { useTheme } from "../../components/ThemeContext"
import ApiService from "../../services/apiService"
import secureStorage from "../../services/secureStorageService"
import { navigateAfterAuth, createUserDataFromAuth } from "../../utils/setupNavigation"
import Svg, { Path } from "react-native-svg"
import { registerFcmTokenWithBackend, listenForFcmTokenRefresh } from '../../services/notificationService'
import firebaseInitService from '../../services/firebaseInitService'
import logger from '../../services/productionLogger'

type Props = NativeStackScreenProps<RootStackParamList, "EmailVerification">

/**
 * PERFORMANCE OPTIMIZATIONS IMPLEMENTED:
 * 1. Input Responsiveness: Reduced delays, optimized focus transitions, non-blocking haptic feedback
 * 2. Error Feedback: Immediate visual feedback with shake animations and better error states
 * 3. API Performance: Parallel processing of token storage and setup API calls
 * 4. Background Operations: Firebase initialization runs in background without blocking navigation
 * 5. Reduced Navigation Delays: Cut navigation delays from 1000ms to 500ms
 * 6. Optimized Clipboard Monitoring: Reduced polling frequency and improved cleanup
 * 7. Performance Monitoring: Added timing logs for API calls and parallel operations
 */
const EmailVerificationScreen = ({ navigation, route }: Props) => {
  const { theme, isDark } = useTheme()
  const [code, setCode] = useState(["", "", "", "", "", ""])
  const [timer, setTimer] = useState(60)
  const [canResend, setCanResend] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isResending, setIsResending] = useState(false)
  const [isBackButtonPressed, setIsBackButtonPressed] = useState(false)
  const [isResendPressed, setIsResendPressed] = useState(false)
  const [verificationStatus, setVerificationStatus] = useState<'idle' | 'success' | 'error' | 'verifying'>('idle')
  const [showToast, setShowToast] = useState(false)
  const [toastMessage, setToastMessage] = useState('')
  const [errorShakeAnim] = useState(new Animated.Value(0))
  const bounceAnim = useRef(new Animated.Value(1)).current
  const toastAnim = useRef(new Animated.Value(300)).current
  const inputRefs = useRef<TextInput[]>([])
  const lastClipboardContent = useRef<string>('')

  // Get email from route params (passed from EmailInputScreen)
  const email = route.params?.email || "<EMAIL>"

  // Background Firebase initialization function
  const initializeFirebaseInBackground = useCallback(async (userId: string) => {
    try {
      logger.info('Initializing Firebase for FCM token registration in background', null, 'notification');

      // Ensure Firebase is initialized before registering FCM token
      const isFirebaseReady = await firebaseInitService.initialize();
      if (isFirebaseReady) {
        logger.info('Firebase ready, registering FCM token in background', null, 'notification');
        await registerFcmTokenWithBackend(userId);
        listenForFcmTokenRefresh(userId);
        logger.info('FCM token registration completed in background', null, 'notification');
      } else {
        logger.warn('Firebase not ready, skipping FCM token registration', {
          possibleCauses: [
            'iOS without GoogleService-Info.plist',
            'Firebase configuration issues',
            'Running in emulator/development mode',
            'Google Services configuration missing or invalid'
          ]
        }, 'notification');
      }
    } catch (fcmError) {
      logger.error('Background FCM token registration failed', fcmError, 'notification');

      // More detailed error logging with proper type safety
      if (fcmError && typeof fcmError === 'object') {
        const errorObj = fcmError as any;
        logger.error('FCM error details', {
          message: errorObj.message || 'Unknown FCM error',
          code: errorObj.code || 'No error code',
          name: errorObj.name || 'Unknown error type'
        }, 'notification');
      }

      // Don't block user flow for FCM token issues
      logger.warn('Continuing user flow despite FCM error', {
        impact: 'notifications may not work until Firebase is properly configured'
      }, 'notification');
    }
  }, [])

  useEffect(() => {
    const interval = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          setCanResend(true)
          clearInterval(interval)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [])

  // DISABLED: Clipboard monitoring for debugging
  useEffect(() => {
    // Clipboard monitoring completely disabled for debugging
    console.log('🚫 [DEBUG] Clipboard monitoring is DISABLED');
    return () => {
      console.log('🚫 [DEBUG] Clipboard monitoring cleanup (was disabled)');
    };
  }, []);

  // ORIGINAL CODE (DISABLED FOR DEBUGGING)
  // const originalClipboardEffect = () => {
  //   let isActive = true;
  //
  //   const checkClipboard = async () => {
  //     // Skip if component is unmounted or verification in progress
  //     if (!isActive || isLoading || verificationStatus !== 'idle') {
  //       return;
  //     }
  //
  //     try {
  //       const clipboardContent = await Clipboard.getString()
  //
  //       // Only process if clipboard content has changed and is not empty
  //       if (clipboardContent && clipboardContent !== lastClipboardContent.current) {
  //         lastClipboardContent.current = clipboardContent
  //
  //         // Debug logging to help identify false positives
  //         if (__DEV__) {
  //           console.log('🔍 [OTP_CLIPBOARD_DEBUG] Checking clipboard content:', clipboardContent.substring(0, 100));
  //         }
  //
  //         // MUCH MORE RESTRICTIVE 6-digit OTP pattern to avoid false positives
  //         // Only match 6-digit codes that are clearly OTP codes with context
  //         const otpPatterns = [
  //           // Pattern 1: 6 digits with explicit OTP context words (must have context)
  //           /(?:code|otp|verification|verify|pin|token)[\s\W:]*(\d{6})(?!\d)/i,
  //           // Pattern 2: 6 digits after colon or dash with whitespace (common in SMS)
  //           /[:]\s+(\d{6})(?!\d)/,
  //           // Pattern 3: 6 digits in parentheses or brackets (common in emails)
  //           /[\(\[\{]\s*(\d{6})\s*[\)\]\}]/,
  //           // Pattern 4: 6 digits at start of message (common SMS format)
  //           /^(\d{6})\s+(?:is|code|otp|verification)/i,
  //           // Pattern 5: 6 digits with "Your" prefix (common format)
  //           /your[\s\W]+(?:code|otp|verification|pin)[\s\W:]*(\d{6})(?!\d)/i
  //         ];
  //
  //         let otpMatch = null;
  //         let matchedPatternIndex = -1;
  //         for (let i = 0; i < otpPatterns.length; i++) {
  //           otpMatch = clipboardContent.match(otpPatterns[i]);
  //           if (otpMatch) {
  //             matchedPatternIndex = i;
  //             break;
  //           }
  //         }
  //
  //         if (otpMatch && isActive) {
  //           // Extract the OTP (might be in capture group 1 for some patterns)
  //           const otp = otpMatch[1] || otpMatch[0];
  //
  //           // Additional validation: ensure it's exactly 6 digits
  //           if (/^\d{6}$/.test(otp)) {
  //             if (__DEV__) {
  //               console.log('📧 [OTP_CLIPBOARD_DEBUG] OTP detected:', otp);
  //               console.log('📧 [OTP_CLIPBOARD_DEBUG] Pattern matched:', matchedPatternIndex);
  //             }
  //
  //             logger.userAction('OTP_AUTO_FILLED_FROM_CLIPBOARD', { otp: '***REDACTED***' });
  //
  //             // Auto-fill the code immediately
  //             const otpArray = otp.split('')
  //             setCode(otpArray)
  //
  //             // Auto-submit with reduced delay for faster UX
  //             setTimeout(() => {
  //               if (isActive) {
  //                 handleVerify(otp)
  //               }
  //             }, 200)
  //           } else if (__DEV__) {
  //             console.log('❌ [OTP_CLIPBOARD_DEBUG] Invalid OTP format:', otp);
  //           }
  //         } else if (__DEV__ && clipboardContent) {
  //           console.log('❌ [OTP_CLIPBOARD_DEBUG] No OTP detected in clipboard');
  //         }
  //       }
  //     } catch (error) {
  //       logger.warn('Clipboard read error', error, 'ui');
  //     }
  //   }
  //
  //   // Check clipboard when app becomes active (user returns from email app)
  //   const handleAppStateChange = (nextAppState: string) => {
  //     if (nextAppState === 'active' && isActive) {
  //       // Reduced delay for faster response
  //       setTimeout(checkClipboard, 100)
  //     }
  //   }
  //
  //   // Initial check
  //   checkClipboard()
  //
  //   // Listen for app state changes
  //   const subscription = AppState.addEventListener('change', handleAppStateChange)
  //
  //   // Reduced polling frequency from 2s to 3s to save battery
  //   const interval = setInterval(checkClipboard, 3000)
  //
  //   return () => {
  //     isActive = false;
  //     subscription?.remove()
  //     clearInterval(interval)
  //   }
  // }

  const handleCodeChange = useCallback((text: string, index: number) => {
    // Prevent changes while verification is in progress
    if (isLoading) {
      return;
    }

    // Only allow numeric characters
    if (!/^\d*$/.test(text)) {
      return;
    }

    // Reset verification status when user starts typing again
    if (verificationStatus !== 'idle') {
      setVerificationStatus('idle')
    }

    const newCode = [...code]
    newCode[index] = text

    setCode(newCode)

    // Trigger haptic feedback on input (non-blocking)
    if (text) {
      // Use setTimeout to make haptic feedback non-blocking
      setTimeout(() => {
        ReactNativeHapticFeedback.trigger("impactMedium", {
          enableVibrateFallback: true,
          ignoreAndroidSystemSettings: true,
        })
      }, 0)
    }

    // Auto-focus next input immediately (no delay)
    if (text && index < 5) {
      // Use requestAnimationFrame for smoother focus transition
      requestAnimationFrame(() => {
        inputRefs.current[index + 1]?.focus()
      })
    }

    // Auto-submit when code is complete (reduced delay)
    if (text && index === 5) {
      const completeCode = newCode.join("")
      if (completeCode.length === 6) {
        // Reduced delay from 300ms to 100ms for faster submission
        setTimeout(() => {
          handleVerify(completeCode)
        }, 100)
      }
    }
  }, [code, isLoading, verificationStatus])

  const handleKeyPress = (key: string, index: number) => {
    if (key === "Backspace" && !code[index] && index > 0) {
      inputRefs.current[index - 1]?.focus()
    }
  }

  // Function to show immediate error feedback
  const showErrorFeedback = useCallback((message: string) => {
    setVerificationStatus('error')
    setToastMessage(message)
    setShowToast(true)

    // Shake animation for immediate visual feedback
    Animated.sequence([
      Animated.timing(errorShakeAnim, {
        toValue: 10,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.timing(errorShakeAnim, {
        toValue: -10,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.timing(errorShakeAnim, {
        toValue: 10,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.timing(errorShakeAnim, {
        toValue: 0,
        duration: 50,
        useNativeDriver: true,
      }),
    ]).start()

    // Toast slide-in animation
    Animated.spring(toastAnim, {
      toValue: 0,
      useNativeDriver: true,
      friction: 4,
    }).start()

    // Auto-hide toast and reset after 3 seconds
    setTimeout(() => {
      Animated.timing(toastAnim, {
        toValue: 300,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        setShowToast(false)
        setVerificationStatus('idle')
        // Clear the code and focus first input for retry
        setCode(["", "", "", "", "", ""])
        inputRefs.current[0]?.focus()
      })
    }, 3000)
  }, [errorShakeAnim, toastAnim])

  const handleVerify = async (providedCode?: string) => {
    const verificationCode = providedCode || code.join("")
    if (verificationCode.length === 6 && !isLoading) {
      setIsLoading(true)
      setVerificationStatus('verifying') // Show verifying state immediately

      // PERFORMANCE MONITORING: Track verification start time
      const verificationStartTime = Date.now();

      try {
        logger.info('Starting email OTP verification', { email }, 'auth');
        const response = await ApiService.verifyEmailOTP(email, verificationCode)

        // PERFORMANCE MONITORING: Log API response time
        const apiResponseTime = Date.now() - verificationStartTime;
        logger.info('Email verification API response time', { responseTime: apiResponseTime }, 'performance');

        if (response?.data) {
          setVerificationStatus('success')
          logger.info('Email verification successful', null, 'auth');
          
          // The response structure is: { data: { status: 'success', data: { user: {...}, tokens: {...} } } }
          const responseData = response.data.data; // Note the nested .data.data
          logger.info('Email verification response received', { hasUser: !!responseData.user, hasTokens: !!responseData.tokens }, 'auth');
          
          // PERFORMANCE OPTIMIZATION: Start token storage and setup API calls in parallel
          const tokenStoragePromise = responseData.tokens?.accessToken
            ? (async () => {
                try {
                  logger.info('Storing authentication tokens securely', null, 'auth');
                  await secureStorage.storeAuthTokens(
                    responseData.tokens.accessToken,
                    responseData.tokens.refreshToken
                  );
                  logger.security('AUTH_TOKENS_STORED', {
                    hasAccessToken: !!responseData.tokens.accessToken,
                    hasRefreshToken: !!responseData.tokens.refreshToken,
                    email: email ? email.slice(0, 3) + '***@' + email.split('@')[1] : 'unknown'
                  });
                  logger.info('Authentication tokens stored successfully in secure storage', null, 'auth');
                  return true;
                } catch (tokenError) {
                  logger.error('Failed to store authentication tokens securely', tokenError, 'auth');
                  throw new Error('Authentication storage failed');
                }
              })()
            : Promise.resolve(true);

          // Get user data from response
          const userFromResponse = responseData.user;
          logger.info('User data received from verification', { hasId: !!userFromResponse?.id, email: userFromResponse?.email }, 'auth');
          
          // Check if this is an existing user (has ID and other data)
          const isExistingUser = !!(userFromResponse && userFromResponse.id);
          logger.info('User type determined', { isExistingUser }, 'auth');
          
          if (isExistingUser) {
            // For existing users, run token storage and setup API calls in parallel
            logger.info('Existing user detected, starting parallel operations', null, 'auth');

            try {
              // PERFORMANCE MONITORING: Track parallel operations time
              const parallelOpsStartTime = Date.now();

              // PERFORMANCE OPTIMIZATION: Run token storage and setup API in parallel
              const [, setupResponse] = await Promise.all([
                tokenStoragePromise, // Wait for token storage to complete
                (async () => {
                  const { setupService } = await import('../../services/setupService')
                  return await setupService.getSetupStatus()
                })()
              ]);

              // PERFORMANCE MONITORING: Log parallel operations time
              const parallelOpsTime = Date.now() - parallelOpsStartTime;
              logger.info('Parallel operations completed', {
                parallelOpsTime,
                hasPinSetup: setupResponse.setupStatus?.hasPinSetup,
                hasBiometricSetup: setupResponse.setupStatus?.hasBiometricSetup,
                hasProfileSetup: setupResponse.setupStatus?.hasProfileSetup,
                setupComplete: setupResponse.setupStatus?.setupComplete
              }, 'performance');

              // SECURITY FIRST: Always require PIN verification for existing users
              // Even if setup is complete, user must verify PIN before accessing main app
              logger.security('EXISTING_USER_PIN_VERIFICATION_REQUIRED', { email });
              
              // Create comprehensive user data from setup response
              const userData = {
                id: setupResponse.user?.id || '',
                email: setupResponse.user?.email || email,
                phoneNumber: setupResponse.user?.phoneNumber,
                firstName: setupResponse.user?.firstName,
                lastName: setupResponse.user?.lastName,
                authMethod: 'email' as const,
                isNewUser: false,
                // Include setup status for navigation decisions
                hasPinSetup: setupResponse.setupStatus?.hasPinSetup,
                hasBiometricSetup: setupResponse.setupStatus?.hasBiometricSetup,
                hasProfileSetup: setupResponse.setupStatus?.hasProfileSetup,
                setupComplete: setupResponse.setupStatus?.setupComplete,
              }

              logger.info('User data created for existing user', { 
                hasFirstName: !!userData.firstName, 
                hasPinSetup: userData.hasPinSetup,
                setupComplete: userData.setupComplete 
              }, 'auth');

              // PERFORMANCE OPTIMIZATION: Start Firebase operations in background (non-blocking)
              if (userFromResponse?.id) {
                // Run Firebase operations in background without blocking navigation
                initializeFirebaseInBackground(userFromResponse.id);
              }

              // Check if user has first name set, if not, go to name setup first
              if (!setupResponse.user?.firstName || setupResponse.user?.firstName.trim() === '') {
                logger.userAction('NAVIGATE_TO_NAME_SETUP', { reason: 'missing_first_name' });
                // Reduced delay from 1000ms to 500ms for faster navigation
                setTimeout(() => {
                  navigation.navigate('NameSetup', { userData: userData })
                }, 500)
              } else if (setupResponse.setupStatus?.hasPinSetup) {
                // User has PIN - ALWAYS go to PIN verification for security
                logger.security('NAVIGATE_TO_PIN_VERIFICATION', { reason: 'existing_user_security_check' });
                setTimeout(() => {
                  navigation.navigate('PinVerification', { user: userData })
                }, 500)
              } else {
                // User doesn't have PIN - go to PIN setup first
                logger.userAction('NAVIGATE_TO_PIN_SETUP', { reason: 'no_pin_configured' });
                setTimeout(() => {
                  navigation.navigate('PinSetup', { userData: userData })
                }, 500)
              }
              
            } catch (setupError) {
              logger.error('Setup status API failed, using fallback', setupError, 'auth');

              // PERFORMANCE OPTIMIZATION: Still wait for token storage in fallback
              await tokenStoragePromise;

              // Start Firebase in background for fallback too
              if (userFromResponse?.id) {
                initializeFirebaseInBackground(userFromResponse.id);
              }

              // Fallback: use verification response data and go to PIN verification
              const userData = createUserDataFromAuth({
                data: {
                  user: userFromResponse,
                  isNewUser: false,
                  userId: userFromResponse.id
                }
              }, 'email')

              logger.info('Fallback user data created', { hasId: !!userData.id }, 'auth');

              // Reduced delay for faster fallback navigation
              setTimeout(async () => {
                logger.security('FALLBACK_PIN_VERIFICATION', { reason: 'setup_api_failed' });
                navigation.navigate('PinVerification', { user: userData })
              }, 500)
            }
          } else {
            // New user - create basic user data
            logger.info('New user detected, creating user data', null, 'auth');

            // PERFORMANCE OPTIMIZATION: Wait for token storage for new users too
            await tokenStoragePromise;

            // Start Firebase in background for new users
            if (userFromResponse?.id) {
              initializeFirebaseInBackground(userFromResponse.id);
            }

            const userData = createUserDataFromAuth({
              data: {
                user: userFromResponse || { email },
                isNewUser: true,
                userId: userFromResponse?.id
              }
            }, 'email')

            logger.info('New user data created', { email: userData.email, isNewUser: userData.isNewUser }, 'auth');

            // Reduced delay for faster new user navigation
            setTimeout(async () => {
              logger.userAction('NAVIGATE_NEW_USER_SETUP', null);
              await navigateAfterAuth(navigation, userData, false) // false = new user, go through setup
            }, 500)
          }

          // Firebase operations are now handled in background for better performance

        } else {
          throw new Error('Invalid response format')
        }
      } catch (error) {
        logger.error('Email verification failed', error, 'auth');

        // Determine specific error message
        let errorMessage = 'Please check your code and try again';
        if (error instanceof Error && error.message) {
          if (error.message.toLowerCase().includes('expired')) {
            errorMessage = 'This verification code has expired. Please request a new one.';
          } else if (error.message.toLowerCase().includes('invalid')) {
            errorMessage = 'The verification code is invalid. Please try again.';
          } else if (error.message.toLowerCase().includes('network') || error.message.toLowerCase().includes('timeout')) {
            errorMessage = 'Network error. Please check your connection and try again.';
          }
        }

        // Use the new error feedback function for immediate visual response
        showErrorFeedback(errorMessage)
      } finally {
        setIsLoading(false)
      }
    }
  }

  const handleResend = async () => {
    if (canResend && !isResending) {
      setIsResending(true)

      try {
        logger.info('Resending OTP to email', { email }, 'auth');
        const response = await ApiService.sendEmailOTP(email, 'verification')

        if (response.status === 'success') {
          logger.info('OTP resent successfully', null, 'auth');
          setTimer(60)
          setCanResend(false)
          setCode(["", "", "", "", "", ""])
          inputRefs.current[0]?.focus()
          Alert.alert('Success', 'Verification code sent successfully')
        } else {
          logger.error('Failed to resend OTP', { message: response.message }, 'auth');
          Alert.alert('Error', response.message || 'Failed to resend code')
        }
      } catch (error) {
        logger.error('Error resending OTP', error, 'auth');
        const errorMessage = error instanceof Error ? error.message : 'Failed to resend code. Please try again.'
        Alert.alert('Error', errorMessage)
      } finally {
        setIsResending(false)
      }
    }
  }

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      paddingTop: 60,
      paddingHorizontal: 20,
      paddingBottom: 20,
    },
    backButton: {
      width: 44,
      height: 44,
      justifyContent: 'center',
      alignItems: 'center',
    },
    backButtonPressed: {
      transform: [{ scale: 0.95 }],
    },
    content: {
      paddingHorizontal: 24,
      paddingTop: 20,
      alignItems: "center",
    },
    title: {
      fontSize: 24,
      fontWeight: "600",
      color: theme.colors.text,
      marginBottom: 8,
      textAlign: "center",
    },
    subtitle: {
      fontSize: 15,
      color: theme.colors.muted,
      lineHeight: 22,
      marginBottom: 8,
      textAlign: "center",
      maxWidth: 280,
    },
    emailAddress: {
      fontSize: 15,
      color: theme.colors.text,
      fontWeight: "600",
      marginBottom: 40,
      textAlign: "center",
    },
    codeContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      width: "100%",
      maxWidth: 300,
      marginBottom: 32,
    },
    codeInput: {
      width: 40,
      height: 50,
      textAlign: "center",
      fontSize: 18,
      fontWeight: "600",
      color: theme.colors.text,
      borderBottomWidth: 2,
      borderBottomColor: isDark ? 'rgba(255, 255, 255, 0.3)' : 'rgba(0, 0, 0, 0.3)',
    },
    codeInputFilled: {
      borderBottomColor: '#8000FF', // Neon purple
      borderBottomWidth: 3, // Make the line slightly thicker when active
      // Removed all shadow properties to eliminate box glow
    },
    codeInputVerifying: {
      borderBottomColor: '#F59E0B', // Orange color for verifying state
      borderBottomWidth: 3,
      // Removed all shadow properties to eliminate box glow
    },
    codeInputSuccess: {
      borderBottomColor: '#10B981', // Green color for success
      borderBottomWidth: 3,
      // Removed all shadow properties to eliminate box glow
    },
    codeInputError: {
      borderBottomColor: '#EF4444', // Red color for error
      borderBottomWidth: 3,
      // Removed all shadow properties to eliminate box glow
    },
    resendContainer: {
      alignItems: "center",
      marginBottom: 40,
    },
    resendButton: {
      padding: 12,
    },
    resendButtonText: {
      fontSize: 14,
      color: canResend ? theme.colors.primary : theme.colors.muted,
      fontWeight: "500",
      textAlign: "center",
    },
    glassyResendButton: {
      borderRadius: 20,
      padding: 2, // Small padding to ensure the glass effect surrounds the button
    },
  })

  return (
    <View style={styles.container}>
      <StatusBar barStyle={isDark ? "light-content" : "dark-content"} backgroundColor="transparent" translucent={true} />
      <ImageBackground
        source={isDark ? require("../../../assets/images/bg.jpeg") : require("../../../assets/images/bg-white.jpeg")}
        style={StyleSheet.absoluteFillObject}
        resizeMode="cover"
      />

      {/* Header */}
      {/* Header */}
      <SafeAreaView style={styles.header}>
        <TouchableOpacity 
          style={[
            styles.backButton,
            isBackButtonPressed && styles.backButtonPressed
          ]}
          onPress={() => navigation.goBack()}
          onPressIn={() => setIsBackButtonPressed(true)}
          onPressOut={() => setIsBackButtonPressed(false)}
          activeOpacity={0.8}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Animated.View 
            style={{ 
              transform: [
                { translateX: isBackButtonPressed ? -1 : 0 }
              ]
            }}
          >
            <BackArrowIcon 
              size={18} 
              color={theme.colors.text} 
            />
          </Animated.View>
        </TouchableOpacity>
      </SafeAreaView>

      {/* Content */}
      <SafeAreaView style={styles.content}>
        <Text style={styles.title}>Enter verification code</Text>
        <Text style={styles.subtitle}>
          We sent a 6-digit code to
        </Text>
        <Text style={styles.emailAddress}>{email}</Text>

        {/* Code Input */}
        <Animated.View
          style={[
            styles.codeContainer,
            { transform: [{ translateX: errorShakeAnim }] }
          ]}
        >
          {code.map((digit, index) => (
            <TextInput
              key={index}
              ref={(ref) => {
                if (ref) inputRefs.current[index] = ref
              }}
              style={[
                styles.codeInput,
                digit && verificationStatus === 'idle' && styles.codeInputFilled,
                verificationStatus === 'verifying' && styles.codeInputVerifying,
                verificationStatus === 'success' && styles.codeInputSuccess,
                verificationStatus === 'error' && styles.codeInputError
              ]}
              value={digit}
              onChangeText={(text) => handleCodeChange(text, index)}
              onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
              keyboardType="numeric"
              maxLength={1}
              autoFocus={index === 0}
              editable={!isLoading}
            />
          ))}
        </Animated.View>

        {/* Resend */}
        <View style={styles.resendContainer}>
          <GlassyBox style={styles.glassyResendButton}>
            <TouchableOpacity
              style={styles.resendButton}
              onPress={handleResend}
              disabled={!canResend}
              activeOpacity={0.7}
              onPressIn={() => {
                setIsResendPressed(true);
                Animated.spring(bounceAnim, {
                  toValue: 0.85,
                  useNativeDriver: true,
                  friction: 3,
                }).start();
              }}
              onPressOut={() => {
                setIsResendPressed(false);
                Animated.spring(bounceAnim, {
                  toValue: 1,
                  useNativeDriver: true,
                  friction: 3,
                }).start();
              }}
            >
              <Animated.View
                style={{
                  transform: [{ scale: bounceAnim }]
                }}
              >
                <Text style={[styles.resendButtonText, { color: canResend ? '#8000FF' : theme.colors.muted }]}>
                  {isResending ? "Sending..." : canResend ? "Resend" : `Resend in ${timer}s`}
                </Text>
              </Animated.View>
            </TouchableOpacity>
          </GlassyBox>
        </View>

        {/* Toast Notification */}
        {showToast && (
          <Animated.View
            style={{
              position: 'absolute',
              top: 50,
              right: 0,
              backgroundColor: '#EF4444', // Red color for error
              paddingVertical: 10,
              paddingHorizontal: 20,
              borderRadius: 8,
              marginHorizontal: 20,
              alignItems: 'center',
              justifyContent: 'center',
              transform: [{ translateX: toastAnim }],
              zIndex: 1000,
            }}
          >
            <Text style={{ color: '#FFFFFF', fontSize: 14, fontWeight: '500' }}>
              {toastMessage}
            </Text>
          </Animated.View>
        )}
      </SafeAreaView>
    </View>
  )
}

const BackArrowIcon = ({ color, size }: { color: string, size: number }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path
      d="M20 11.5H9.41L14.46 6.46C14.85 6.07 14.85 5.44 14.46 5.05C14.27 4.86 14.02 4.76 13.76 4.76C13.5 4.76 13.25 4.86 13.05 5.05L6.46 11.64C6.07 12.03 6.07 12.66 6.46 13.05L13.05 19.64C13.44 20.03 14.07 20.03 14.46 19.64C14.85 19.25 14.85 18.62 14.46 18.23L9.41 13.18H20C20.55 13.18 21 12.73 21 12.18C21 11.63 20.55 11.18 20 11.18V11.5Z"
      fill={color}
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
)

export default EmailVerificationScreen
