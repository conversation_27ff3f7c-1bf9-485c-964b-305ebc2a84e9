import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Image,
  ScrollView,
  RefreshControl,
  Animated,
  Alert,
  Dimensions,
} from 'react-native';

import type { NativeStackScreenProps } from '@react-navigation/native-stack';
import type { RootStackParamList } from '../../types/navigation';
import { useTheme } from '../../components/ThemeContext';
import { userService } from '../../services/userService';
import { UserProfile } from '../../types/user';
import {
  EyeIcon,
  EyeOffIcon,
} from '../../components/icons';
import { SafeAreaView as RNSafeAreaView } from 'react-native-safe-area-context';
import Clipboard from '@react-native-clipboard/clipboard';
import ClipboardPhoneModal from '../../components/ClipboardPhoneModal';
import MoreProductsModal from '../../components/MoreProductsModal';
import LocalAvatarImage from '../../components/LocalAvatarImage';
import logger from '../../services/productionLogger';
import SimpleBalanceCard from '../../components/SimpleBalanceCard';
import LinearGradient from 'react-native-linear-gradient';
import Svg, { Defs, ClipPath, Path, Rect, G, Mask, LinearGradient as SvgLinearGradient, Stop } from 'react-native-svg';
import crashReporting from '../../services/crashReportingService';

const { width: screenWidth } = Dimensions.get('window');

// Notification Bell Icon
const NotificationIcon = ({ size = 24, color = '#8E8E93' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M12 2C13.1 2 14 2.9 14 4C14 4.74 13.6 5.39 13 5.73V7C13 10.76 16.05 13.81 19.81 13.81H20V16H4V13.81H4.19C7.95 13.81 11 10.76 11 7V5.73C10.4 5.39 10 4.74 10 4C10 2.9 10.9 2 12 2ZM10 20C10 21.1 10.9 22 12 22S14 21.1 14 20" fill={color}/>
  </Svg>
);

// Modern Balance Card with Gradient
const ModernBalanceCard = React.memo(({
  balance,
  userName,
  userPhone,
  balanceVisible,
  onToggleVisibility,
  loading,
  theme,
  isDark
}: {
  balance: number;
  userName: string;
  userPhone: string;
  balanceVisible: boolean;
  onToggleVisibility: () => void;
  loading: boolean;
  theme: any;
  isDark: boolean;
}) => {
  const cardWidth = screenWidth - 40;
  const cardHeight = 180; // Increased height for new layout

  // Create styles for this component
  const styles = createStyles(theme, isDark);

  return (
    <View style={[styles.balanceCard, { width: cardWidth, height: cardHeight }]}>
      <Svg
        width={cardWidth}
        height={cardHeight}
        style={StyleSheet.absoluteFillObject}
        viewBox={`0 0 ${cardWidth} ${cardHeight}`}
      >
        <Defs>
          <SvgLinearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <Stop offset="0%" stopColor={theme.colors.primary} stopOpacity="1" />
            <Stop offset="50%" stopColor={theme.colors.accent} stopOpacity="1" />
            <Stop offset="100%" stopColor={isDark ? '#C084FC' : '#A855F7'} stopOpacity="1" />
          </SvgLinearGradient>
          {/* Clippath for button cutout */}
          <ClipPath id="cardClip">
            <Path d={`
              M 20 0
              L ${cardWidth - 20} 0
              Q ${cardWidth} 0 ${cardWidth} 20
              L ${cardWidth} ${cardHeight - 50}
              L ${cardWidth - 100} ${cardHeight - 50}
              Q ${cardWidth - 120} ${cardHeight - 50} ${cardWidth - 120} ${cardHeight - 30}
              Q ${cardWidth - 120} ${cardHeight - 10} ${cardWidth - 100} ${cardHeight - 10}
              L ${cardWidth} ${cardHeight - 10}
              L ${cardWidth} ${cardHeight - 20}
              Q ${cardWidth} ${cardHeight} ${cardWidth - 20} ${cardHeight}
              L 20 ${cardHeight}
              Q 0 ${cardHeight} 0 ${cardHeight - 20}
              L 0 20
              Q 0 0 20 0
              Z
            `} />
          </ClipPath>
        </Defs>
        <Rect
          width="100%"
          height="100%"
          fill="url(#cardGradient)"
          clipPath="url(#cardClip)"
        />
      </Svg>

      <View style={styles.newBalanceCardContent}>
        {/* Top Section - Balance and Eye Icon */}
        <View style={styles.newBalanceHeader}>
          <View style={styles.newBalanceAmount}>
            {loading ? (
              <BalanceSkeleton isDark={isDark} />
            ) : (
              <>
                <Text style={styles.newBalanceText}>
                  {balanceVisible ? new Intl.NumberFormat('en-NG', {
                    style: 'currency',
                    currency: 'NGN',
                    minimumFractionDigits: 2,
                  }).format(balance) : '₦••••'}
                </Text>
                <Text style={styles.balanceChangeText}>+3.50% from last month</Text>
              </>
            )}
          </View>
          <TouchableOpacity onPress={onToggleVisibility} style={styles.eyeButton}>
            {balanceVisible ?
              <EyeIcon size={20} color="#FFFFFF" /> :
              <EyeOffIcon size={20} color="#FFFFFF" />
            }
          </TouchableOpacity>
        </View>

        {/* Bottom Section - Card Details Only */}
        <View style={styles.newBottomSection}>
          <View style={styles.newCardDetails}>
            <View style={styles.newCardDetailItem}>
              <Text style={styles.newCardDetailLabel}>Number</Text>
              <Text style={styles.newCardDetailValue}>•••• 1214</Text>
            </View>
            <View style={styles.newCardDetailItem}>
              <Text style={styles.newCardDetailLabel}>Exp</Text>
              <Text style={styles.newCardDetailValue}>02/15</Text>
            </View>
          </View>
        </View>

        {/* Decorative circles */}
        <View style={styles.decorativeCircle1} />
        <View style={styles.decorativeCircle2} />
      </View>

      {/* Add Money Button - Positioned in the cutout */}
      <TouchableOpacity style={[styles.clipPathAddMoneyButton, {
        position: 'absolute',
        right: 20,
        bottom: 15,
        backgroundColor: '#FFFFFF',
        paddingHorizontal: 20,
        paddingVertical: 8,
        borderRadius: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      }]}>
        <Text style={[styles.newAddMoneyButtonText, {
          color: '#000000',
          fontSize: 14,
          fontWeight: '600',
        }]}>Add money</Text>
      </TouchableOpacity>
    </View>
  );
});

// Profile and Balance Skeleton Components - Theme-aware
const ProfileSkeleton = React.memo(({ isDark = false }: { isDark?: boolean }) => {
  const [shimmerAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    const shimmerAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(shimmerAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(shimmerAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    shimmerAnimation.start();
    return () => shimmerAnimation.stop();
  }, [shimmerAnim]);

  const shimmerOpacity = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <Animated.View
      style={[
        {
          width: 40,
          height: 40,
          borderRadius: 20,
          backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : '#E5E5E7',
        },
        { opacity: shimmerOpacity }
      ]}
    />
  );
});

const BalanceSkeleton = React.memo(({ isDark = false }: { isDark?: boolean }) => {
  const [shimmerAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    const shimmerAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(shimmerAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(shimmerAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    shimmerAnimation.start();
    return () => shimmerAnimation.stop();
  }, [shimmerAnim]);

  const shimmerOpacity = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <Animated.View
      style={[
        {
          width: 120,
          height: 28,
          borderRadius: 8,
          backgroundColor: isDark ? 'rgba(255, 255, 255, 0.15)' : 'rgba(255, 255, 255, 0.3)',
        },
        { opacity: shimmerOpacity }
      ]}
    />
  );
});

// Quick Action Button Component
const QuickActionButton = React.memo(({
  icon,
  label,
  onPress,
  style,
  theme,
  isDark
}: {
  icon: React.ReactNode;
  label: string;
  onPress: () => void;
  style?: any;
  theme: any;
  isDark: boolean;
}) => {
  const styles = createStyles(theme, isDark);

  return (
    <TouchableOpacity
      style={[styles.quickActionButton, style]}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.quickActionIcon}>
        {icon}
      </View>
      <Text style={styles.quickActionLabel}>{label}</Text>
    </TouchableOpacity>
  );
});

// Service Icons from assets/svg folder
const SendMoneyIcon = ({ size = 24, color = '#8B5CF6' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M12 2L7 7H10V14H14V7H17L12 2Z" fill={color} />
    <Path d="M19 15V18H5V15H3V18C3 19.1 3.9 20 5 20H19C20.1 20 21 19.1 21 18V15H19Z" fill={color} />
  </Svg>
);

const AirtimeIcon = ({ size = 24, color = '#8B5CF6' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M6.62 10.79C8.06 13.62 10.38 15.94 13.21 17.38L15.41 15.18C15.69 14.9 16.08 14.82 16.43 14.93C17.55 15.3 18.75 15.5 20 15.5C20.55 15.5 21 15.95 21 16.5V20C21 20.55 20.55 21 20 21C10.61 21 3 13.39 3 4C3 3.45 3.45 3 4 3H7.5C8.05 3 8.5 3.45 8.5 4C8.5 5.25 8.7 6.45 9.07 7.57C9.18 7.92 9.1 8.31 8.82 8.59L6.62 10.79Z" fill={color} />
  </Svg>
);

const BillsIcon = ({ size = 24, color = '#8B5CF6' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.89 22 5.99 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z" fill={color} />
  </Svg>
);

const DataIcon = ({ size = 24, color = '#8B5CF6' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22 22 17.52 22 12 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z" fill={color} />
  </Svg>
);

// Transaction Item Component - Optimized with memoization
const TransactionItem = React.memo(({
  icon,
  title,
  subtitle,
  amount,
  status,
  theme,
  isDark
}: {
  icon: React.ReactNode;
  title: string;
  subtitle: string;
  amount: string;
  status: 'sent' | 'received';
  theme: any;
  isDark: boolean;
}) => {
  // Memoize styles to prevent recreation on every render
  const styles = useMemo(() => createStyles(theme, isDark), [theme, isDark]);

  // Memoize amount color calculation
  const amountColor = useMemo(() =>
    status === 'received' ? theme.colors.success : theme.colors.text,
    [status, theme.colors.success, theme.colors.text]
  );

  return (
    <View style={styles.transactionItem}>
      <View style={styles.transactionIcon}>
        {icon}
      </View>
      <View style={styles.transactionContent}>
        <Text style={styles.transactionTitle}>{title}</Text>
        <Text style={styles.transactionSubtitle}>{subtitle}</Text>
      </View>
      <Text style={[
        styles.transactionAmount,
        { color: amountColor }
      ]}>
        {status === 'received' ? '+' : ''}{amount}
      </Text>
    </View>
  );
});

// Styles definition - moved outside component to be accessible by all components
// Note: This will be wrapped in a function to access theme colors
const createStyles = (theme: any, isDark: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: isDark ? '#0A0A0B' : theme.colors.card, // Deeper dark background
  },
  safeArea: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 100, // Proper padding for bottom tab bar
  },
  // Modern Header Section - Enhanced for dark theme
  headerSection: {
    backgroundColor: isDark ? '#0A0A0B' : theme.colors.card,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    borderBottomWidth: isDark ? 1 : 0,
    borderBottomColor: isDark ? 'rgba(255, 255, 255, 0.05)' : 'transparent',
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    marginBottom: 24,
  },
  // Currency Toggle
  currencyToggle: {
    flexDirection: 'row',
    backgroundColor: isDark ? theme.colors.surface : '#E5E5E7',
    borderRadius: 20,
    padding: 4,
    marginBottom: 20,
    alignSelf: 'center',
  },
  currencyOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    minWidth: 60,
    alignItems: 'center',
  },
  currencyOptionActive: {
    backgroundColor: theme.colors.background,
  },
  currencyText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.muted,
  },
  currencyTextActive: {
    color: theme.colors.text,
  },
  // Vendy Logo
  logoSection: {
    alignItems: 'center',
    flex: 1,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logoText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: '700',
    fontFamily: 'System',
  },
  logoAccent: {
    color: theme.colors.primary, // Use theme primary color
    fontSize: 24,
    fontWeight: '700',
    fontFamily: 'System',
  },
  logoSplit: {
    fontSize: 24,
    fontWeight: '700',
    fontFamily: 'System',
    color: '#FFFFFF',
  },
  // Profile and Notification
  profileButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.success,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileInitials: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  profileImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Modern Balance Card - Proper spacing like reference
  balanceCard: {
    marginHorizontal: 20,
    marginBottom: 24,
    borderRadius: 16,
    overflow: 'hidden',
    position: 'relative',
  },
  balanceCardContent: {
    padding: 20,
    position: 'relative',
    zIndex: 1,
  },
  // New clean card styles
  balanceCardContainer: {
    marginHorizontal: 20,
    marginBottom: 24,
    position: 'relative',
  },
  cleanBalanceCard: {
    borderRadius: 16,
    overflow: 'hidden',
    position: 'relative',
  },
  gradientBackground: {
    flex: 1,
    borderRadius: 16,
  },
  externalAddMoneyButton: {
    position: 'absolute',
    right: 20,
    bottom: 20,
    backgroundColor: isDark ? '#FFFFFF' : '#000000',
    paddingHorizontal: 18,
    paddingVertical: 10,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.4,
    shadowRadius: 8,
    elevation: 8,
    zIndex: 10,
  },
  balanceCardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  balanceAmount: {
    flex: 1,
  },
  balanceText: {
    color: '#FFFFFF',
    fontSize: 32,
    fontWeight: '700',
    lineHeight: 38,
    letterSpacing: 0.5,
  },
  eyeButton: {
    padding: 4,
    marginLeft: 12,
  },
  userInfo: {
    marginTop: 8,
  },
  userNameText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 4,
    letterSpacing: 0.2,
  },
  userPhoneText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    fontWeight: '400',
    letterSpacing: 0.1,
  },

  // New Balance Card Styles
  newBalanceCardContent: {
    padding: 24,
    position: 'relative',
    zIndex: 1,
    flex: 1,
    justifyContent: 'space-between',
  },
  newBalanceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  newBalanceAmount: {
    flex: 1,
  },
  newBalanceText: {
    color: '#FFFFFF',
    fontSize: 36,
    fontWeight: '700',
    lineHeight: 42,
    letterSpacing: 0.5,
    marginBottom: 4,
  },
  balanceChangeText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    fontWeight: '500',
    letterSpacing: 0.2,
  },
  newBottomSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    marginTop: 16,
  },
  newCardDetails: {
    flexDirection: 'row',
    gap: 24,
  },
  newCardDetailItem: {
    alignItems: 'flex-start',
  },
  newCardDetailLabel: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
    letterSpacing: 0.3,
  },
  newCardDetailValue: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  newAddMoneyButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  newAddMoneyButtonText: {
    color: isDark ? '#000000' : '#FFFFFF', // Black text on white button (dark theme), white text on black button (light theme)
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  clipPathAddMoneyButton: {
    // Styles are now inline for better control
  },
  decorativeCircle1: {
    position: 'absolute',
    top: -30,
    right: -30,
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  decorativeCircle2: {
    position: 'absolute',
    bottom: -20,
    right: 20,
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  // Quick Action Buttons - In container box like reference
  quickActionsSection: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around', // Changed from space-between to space-around for better spacing
    backgroundColor: isDark ? 'rgba(255, 255, 255, 0.05)' : '#FFFFFF',
    borderRadius: 16,
    paddingVertical: 24, // Increased vertical padding
    paddingHorizontal: 16, // Reduced horizontal padding to give more room for spacing
    shadowColor: isDark ? 'transparent' : '#000',
    shadowOffset: isDark ? { width: 0, height: 0 } : { width: 0, height: 2 },
    shadowOpacity: isDark ? 0 : 0.08,
    shadowRadius: isDark ? 0 : 8,
    elevation: isDark ? 0 : 3,
  },
  quickActionButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 12, // Reduced from 16
    marginHorizontal: 3, // Reduced from 4
  },
  quickActionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: isDark ? 'rgba(139, 92, 246, 0.15)' : '#F5F5F5', // Purple tint in dark mode
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 6,
    borderWidth: isDark ? 1 : 0,
    borderColor: isDark ? 'rgba(139, 92, 246, 0.2)' : 'transparent',
  },
  quickActionLabel: {
    fontSize: 11,
    fontWeight: '600',
    color: isDark ? 'rgba(255, 255, 255, 0.9)' : theme.colors.text,
    textAlign: 'center',
    letterSpacing: 0.1,
  },
  // Recent Transactions - Proper spacing like reference
  transactionsSection: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: isDark ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
    letterSpacing: 0.2,
  },
  seeAllButton: {
    backgroundColor: isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.05)',
    paddingHorizontal: 14,
    paddingVertical: 7,
    borderRadius: 20, // Much more rounded - almost pill-shaped
  },
  seeAllButtonText: {
    fontSize: 13,
    fontWeight: '500',
    color: theme.colors.primary,
    opacity: 0.8,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    backgroundColor: isDark ? 'rgba(255, 255, 255, 0.05)' : '#FFFFFF',
    borderRadius: 12,
    marginBottom: 8,
    paddingHorizontal: 16,
    borderWidth: isDark ? 1 : 0,
    borderColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'transparent',
    shadowColor: isDark ? 'transparent' : '#000',
    shadowOffset: isDark ? { width: 0, height: 0 } : { width: 0, height: 1 },
    shadowOpacity: isDark ? 0 : 0.05,
    shadowRadius: isDark ? 0 : 2,
    elevation: isDark ? 0 : 1,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    borderWidth: isDark ? 1 : 0,
    borderColor: isDark ? 'rgba(255, 255, 255, 0.05)' : 'transparent',
  },
  transactionContent: {
    flex: 1,
    paddingRight: 8,
  },
  transactionTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: theme.colors.text,
    marginBottom: 2,
    letterSpacing: 0.1,
  },
  transactionSubtitle: {
    fontSize: 13,
    fontWeight: '400',
    color: isDark ? 'rgba(255, 255, 255, 0.6)' : theme.colors.muted,
    letterSpacing: 0.1,
  },
  transactionAmount: {
    fontSize: 15,
    fontWeight: '700',
    color: theme.colors.text,
    letterSpacing: 0.2,
  },
  // Loading and Error States
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorCard: {
    backgroundColor: isDark ? 'rgba(255, 59, 48, 0.1)' : 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
    marginHorizontal: 20,
    borderWidth: 1,
    borderColor: isDark ? 'rgba(255, 59, 48, 0.2)' : 'rgba(239, 68, 68, 0.1)',
  },
  errorText: {
    color: isDark ? '#FF453A' : theme.colors.error,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 22,
    letterSpacing: 0.1,
  },
  retryButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 16,
    shadowColor: theme.colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '700',
  },

  // OTA Test Banner Styles
  otaTestBanner: {
    backgroundColor: '#4CAF50',
    marginHorizontal: 16,
    marginTop: 10,
    marginBottom: 15,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  otaTestText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 4,
  },
  otaTestSubtext: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
    opacity: 0.9,
  },
});

type Props = NativeStackScreenProps<RootStackParamList, 'Home'>;

const CustomHomeScreen = ({ navigation }: Props) => {
  const { theme, isDark } = useTheme();
  const [userData, setUserData] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [balanceVisible, setBalanceVisible] = useState(true);

  // Create styles with theme
  const styles = useMemo(() => createStyles(theme, isDark), [theme, isDark]);

  // Performance optimizations - memoize expensive calculations

  const userInitials = useMemo(() => {
    if (!userData) return 'U';
    return `${userData.firstName?.charAt(0) || ''}${userData.lastName?.charAt(0) || ''}`.toUpperCase();
  }, [userData?.firstName, userData?.lastName]);

  const [refreshing, setRefreshing] = useState(false);
  const [clipboardPhone, setClipboardPhone] = useState<string | null>(null);
  const [showClipboardModal, setShowClipboardModal] = useState(false);
  const [clipboardChecked, setClipboardChecked] = useState(false);
  const [showMoreProductsModal, setShowMoreProductsModal] = useState(false);
  const [helpButtonPresses, setHelpButtonPresses] = useState(0);
  const [errorCount, setErrorCount] = useState(0);

  // Animation values for enhanced UI
  const fadeAnim = useMemo(() => new Animated.Value(0), []);
  const slideUpAnim = useMemo(() => new Animated.Value(50), []);
  const scaleAnim = useMemo(() => new Animated.Value(0.95), []);

  // Load user data from new API endpoint
  useEffect(() => {
    // Add a small delay to ensure app is fully loaded
    const timer = setTimeout(() => {
      loadUserData();
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Reset error count every 5 minutes to allow fresh attempts
  useEffect(() => {
    const errorResetTimer = setInterval(() => {
      setErrorCount(0);
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(errorResetTimer);
  }, []);

  const loadUserData = async () => {
    try {
      logger.info('Loading user data from new API', null, 'home');
      setError(null);
      setLoading(true);
      // Reset error count on successful attempt
      setErrorCount(0);

      // Check authentication status first
      const { apiService } = await import('../../services/apiService');
      const accessToken = await apiService.getAccessToken();
      console.log('🔐 [HOME] Auth check - Has token:', !!accessToken);

      if (!accessToken) {
        console.log('❌ [HOME] No access token found - user not logged in');
        setError('Please log in to view your profile');
        // Set fallback data for UI
        setUserData({
          id: 'guest',
          firstName: 'Guest',
          lastName: 'User',
          email: '',
          balance: 0,
          picture: undefined,
          avatar: undefined,
          isEmailVerified: false,
          isPhoneVerified: false,
        });
        return;
      }

      // Add timeout to the entire operation
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('User data loading timeout')), 15000); // 15 second timeout
      });

      const loadOperation = async () => {
        // Use the new user service to get profile data
        console.log('🔄 [HOME] Attempting to load user profile...');
        try {
          const profileResponse = await userService.getUserProfile();
          console.log('📥 [HOME] Profile response:', JSON.stringify(profileResponse, null, 2));

          // Safe property access with proper null checks
          if (profileResponse && typeof profileResponse === 'object') {
            // Check if response has user property
            if (profileResponse.user && typeof profileResponse.user === 'object') {
              const user = profileResponse.user;
              console.log('✅ [HOME] User data loaded:', {
                id: user.id,
                firstName: user.firstName,
                balance: user.balance,
                hasPicture: !!(user.picture || user.avatar)
              });
              setUserData(user);
            } else {
              console.log('❌ [HOME] No user property in response:', profileResponse);
              throw new Error('Invalid response structure: missing user data');
            }
          } else {
            console.log('❌ [HOME] Invalid response format:', profileResponse);
            throw new Error('Invalid response format from API');
          }
        } catch (apiError: any) {
          // Safe error property access
          const errorDetails = {
            message: apiError?.message || 'Unknown error',
            status: apiError?.status || 'No status',
            response: apiError?.response || 'No response',
            stack: apiError?.stack || 'No stack trace',
            name: apiError?.name || 'Unknown error type'
          };
          console.log('❌ [HOME] API Error Details:', errorDetails);
          throw apiError;
        }
      };

      // Race between the operation and timeout
      await Promise.race([loadOperation(), timeoutPromise]);

    } catch (error: any) {
      // Increment error count and prevent excessive logging
      const newErrorCount = errorCount + 1;
      setErrorCount(newErrorCount);

      // Only log first 5 errors to prevent database overflow
      if (newErrorCount <= 5) {
        try {
          if (logger && typeof logger.error === 'function') {
            logger.error('Error loading user data', error, 'home');
          } else {
            console.error('❌ [HOME] Logger not available, error:', error);
          }
        } catch (logError) {
          console.error('❌ [HOME] Failed to log error:', logError);
        }
      } else {
        // Just console log after 5 errors to prevent database issues
        console.error('❌ [HOME] Error (not logged to prevent overflow):', error?.message || 'Unknown error');
      }

      // Safe property access for error handling
      const errorMessage = error?.message || 'Unknown error';
      const errorStatus = error?.status;

      // Provide more specific error messages
      let displayMessage = 'Failed to load user data';
      if (errorMessage === 'User data loading timeout') {
        displayMessage = 'Loading is taking longer than expected. Please check your connection.';
      } else if (errorMessage.includes('timeout')) {
        displayMessage = 'Connection timeout. Please check your internet connection.';
      } else if (errorMessage.includes('Network')) {
        displayMessage = 'Network error. Please check your connection.';
      } else if (errorStatus === 401) {
        displayMessage = 'Session expired. Please login again.';
      } else if (errorStatus && errorStatus >= 500) {
        displayMessage = 'Server is temporarily unavailable. Please try again later.';
      } else if (errorMessage) {
        displayMessage = errorMessage;
      }

      setError(displayMessage);

      // Fallback to default data so the UI doesn't break
      setUserData({
        id: 'unknown',
        firstName: 'User',
        lastName: '',
        email: '',
        isEmailVerified: false,
        isPhoneVerified: false,
        balance: 1250.50, // Mock balance for demo
      });
    } finally {
      setLoading(false);
    }
  };

  // Initialize animations on mount
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideUpAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, slideUpAnim, scaleAnim]);

  const handleServicePress = useCallback((service: string) => {
    logger.userAction('SERVICE_PRESSED', { service });
    // Navigate to respective service screens
    switch (service) {
      case 'buy-data':
        // navigation.navigate('BuyData');
        break;
      case 'buy-airtime':
        navigation.navigate('Airtime' as never);
        break;
      case 'pay-bills':
        // navigation.navigate('PayBills');
        break;
      case 'cable-tv':
        // navigation.navigate('CableTV');
        break;
      case 'transfer':
        // navigation.navigate('Transfer');
        break;
      case 'see-more':
        setShowMoreProductsModal(true);
        break;
      default:
        logger.warn('Unknown service selected', { service }, 'home');
    }
  }, [navigation]);

  // Clipboard phone detection on mount (only once per session)
  useEffect(() => {
    if (clipboardChecked) return;
    const checkClipboardForPhone = async () => {
      try {
        const content = await Clipboard.getString();

        // Debug logging to help identify false positives
        if (__DEV__ && content) {
          console.log('🔍 [CLIPBOARD_DEBUG] Checking clipboard content:', content.substring(0, 100));
        }

        // More precise Nigerian phone patterns to avoid false positives
        // Pattern 1: +234 followed by 10 digits (international format)
        const intlMatch = content.match(/\+234[789]\d{9}(?!\d)/);
        // Pattern 2: 234 followed by 10 digits (without +)
        const countryMatch = content.match(/(?<!\d)234[789]\d{9}(?!\d)/);
        // Pattern 3: 0 followed by 10 digits starting with 7, 8, or 9 (local format)
        const localMatch = content.match(/(?<!\d)0[789]\d{9}(?!\d)/);

        const phoneMatch = intlMatch || countryMatch || localMatch;
        if (phoneMatch) {
          if (__DEV__) {
            console.log('📱 [CLIPBOARD_DEBUG] Phone detected:', phoneMatch[0]);
            console.log('📱 [CLIPBOARD_DEBUG] Match type:', intlMatch ? 'international' : countryMatch ? 'country' : 'local');
          }
          setClipboardPhone(phoneMatch[0]);
          setShowClipboardModal(true);
        } else if (__DEV__ && content) {
          console.log('❌ [CLIPBOARD_DEBUG] No phone number detected in clipboard');
        }
      } catch (e) {
        // Ignore errors
      } finally {
        setClipboardChecked(true);
      }
    };
    checkClipboardForPhone();
  }, [clipboardChecked]);

  const handleIgnoreClipboard = () => {
    setShowClipboardModal(false);
    setClipboardPhone(null);
  };

  const handleUseClipboard = (_phone: string) => {
    setShowClipboardModal(false);
    setClipboardPhone(null);
    navigation.navigate('Airtime'); // Remove params for now, as Airtime expects no params
  };

  // Add pull-to-refresh functionality
  const onRefresh = async () => {
    setRefreshing(true);
    await loadUserData();
    setRefreshing(false);
  };

  // Emergency function to clear error loops and database
  const clearErrorLoop = async () => {
    try {
      // Clear crash reports
      await crashReporting.clearAllStoredErrors();

      // Clear AsyncStorage cache if available
      try {
        const AsyncStorage = require('@react-native-async-storage/async-storage').default;
        const keys = await AsyncStorage.getAllKeys();
        const cacheKeys = keys.filter((key: string) =>
          key.startsWith('cache_') ||
          key.startsWith('api_cache_') ||
          key.startsWith('user_cache_')
        );
        if (cacheKeys.length > 0) {
          await AsyncStorage.multiRemove(cacheKeys);
        }
      } catch (cacheError) {
        console.log('Cache clearing failed:', cacheError);
      }

      Alert.alert(
        'Database Cleared',
        'All stored error logs and cache have been cleared. The app should work better now.',
        [{ text: 'OK', onPress: () => loadUserData() }]
      );
    } catch (error) {
      Alert.alert(
        'Clear Failed',
        'Failed to clear database. Please restart the app.',
        [{ text: 'OK' }]
      );
    }
  };

  // Test backend connectivity
  const testBackendConnection = async () => {
    try {
      const { apiService } = await import('../../services/apiService');
      console.log('🔄 [HOME] Testing backend connection...');

      // Try the user profile endpoint with auth
      const response = await apiService.get('/user/profile', {}, {
        timeout: 5000,
        skipAuth: false // Use auth for real test
      });

      console.log('✅ [HOME] Backend connection successful:', response);
      Alert.alert('Backend Status', 'Backend is accessible and user profile endpoint works!');
    } catch (error: any) {
      console.log('❌ [HOME] Backend connection failed:', {
        message: error.message,
        status: error.status,
        response: error.response
      });
      Alert.alert(
        'Backend Status',
        `Backend connection failed: ${error.message || 'Unknown error'}\n\nStatus: ${error.status || 'No status'}\n\nMake sure your backend server is running on http://*************:8000`
      );
    }
  };

  // Handle help button press (secret combination to clear errors)
  const handleHelpPress = () => {
    const newCount = helpButtonPresses + 1;
    setHelpButtonPresses(newCount);

    if (newCount >= 5) {
      // Reset counter and show emergency options
      setHelpButtonPresses(0);
      Alert.alert(
        'Emergency Options',
        'Choose an option:',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Test Backend', onPress: testBackendConnection },
          { text: 'Clear Error Logs', onPress: clearErrorLoop, style: 'destructive' }
        ]
      );
    }

    // Reset counter after 3 seconds
    setTimeout(() => setHelpButtonPresses(0), 3000);
  };

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle={isDark ? "light-content" : "dark-content"}
        backgroundColor={theme.colors.card}
        translucent={false}
      />

      <RNSafeAreaView style={styles.safeArea}>
        {/* Show loading or main content */}

        {/* Show error state */}
        {error && !loading && (
          <View style={styles.content}>
            <View style={styles.errorCard}>
              <Text style={styles.errorText}>{error}</Text>
              <TouchableOpacity style={styles.retryButton} onPress={loadUserData}>
                <Text style={styles.retryButtonText}>Retry</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        {/* Show main content (always visible, with skeletons when loading) */}
        {!error && (
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                tintColor={theme.colors.primary}
                colors={[theme.colors.primary]}
              />
            }
            showsVerticalScrollIndicator={false}
          >
          {/* OTA TEST BANNER - This will be visible after OTA update! */}
          <View style={styles.otaTestBanner}>
            <Text style={styles.otaTestText}>🚀 OTA UPDATE SUCCESS! 🎉</Text>
            <Text style={styles.otaTestSubtext}>This banner proves your OTA update worked!</Text>
          </View>

          {/* OTA TEST BANNER - This will be visible after OTA update! */}
          <View style={styles.otaTestBanner}>
            <Text style={styles.otaTestText}>🚀 OTA UPDATE SUCCESS! 🎉</Text>
            <Text style={styles.otaTestSubtext}>This banner proves your OTA update worked!</Text>
          </View>

          {/* Modern Header */}
          <View style={styles.headerSection}>
            <View style={styles.headerTop}>
              {loading ? (
                <ProfileSkeleton isDark={isDark} />
              ) : (
                <TouchableOpacity style={styles.profileButton} onPress={handleHelpPress}>
                  {userData?.picture || userData?.avatar ? (
                    <LocalAvatarImage
                      avatarPath={userData.picture || userData.avatar || ''}
                      style={styles.profileImage}
                    />
                  ) : (
                    <Text style={styles.profileInitials}>
                      {userInitials}
                    </Text>
                  )}
                </TouchableOpacity>
              )}
            </View>

            {/* Currency Toggle */}
            <View style={styles.currencyToggle}>
              <TouchableOpacity style={[styles.currencyOption, styles.currencyOptionActive]}>
                <Text style={[styles.currencyText, styles.currencyTextActive]}>Naira</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.currencyOption}>
                <Text style={styles.currencyText}>Dollar</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Simple Balance Card */}
          <SimpleBalanceCard
            balance={userData?.balance || 0}
            balanceVisible={balanceVisible}
            onToggleVisibility={() => setBalanceVisible(!balanceVisible)}
            loading={loading}
            theme={theme}
            isDark={isDark}
          />

          {/* Quick Actions */}
          <View style={styles.quickActionsSection}>
            <View style={styles.quickActionsGrid}>
              <QuickActionButton
                icon={<SendMoneyIcon size={24} color={theme.colors.primary} />}
                label="Send Money"
                onPress={() => handleServicePress('transfer')}
                theme={theme}
                isDark={isDark}
              />
              <QuickActionButton
                icon={<AirtimeIcon size={24} color={theme.colors.primary} />}
                label="Buy Airtime"
                onPress={() => handleServicePress('buy-airtime')}
                theme={theme}
                isDark={isDark}
              />
              <QuickActionButton
                icon={<BillsIcon size={24} color={theme.colors.primary} />}
                label="Pay Bills"
                onPress={() => handleServicePress('pay-bills')}
                theme={theme}
                isDark={isDark}
              />
              <QuickActionButton
                icon={<DataIcon size={24} color={theme.colors.primary} />}
                label="Buy Data"
                onPress={() => handleServicePress('buy-data')}
                theme={theme}
                isDark={isDark}
              />
            </View>
          </View>


          {/* Recent Transactions */}
          <View style={styles.transactionsSection}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Recent transactions</Text>
              <TouchableOpacity style={styles.seeAllButton}>
                <Text style={styles.seeAllButtonText}>See all</Text>
              </TouchableOpacity>
            </View>

            <TransactionItem
              icon={<View style={{ width: 8, height: 8, borderRadius: 4, backgroundColor: theme.colors.error }} />}
              title="Spotify subscription"
              subtitle="Jul 14 • 7:30 PM"
              amount="₦15"
              status="sent"
              theme={theme}
              isDark={isDark}
            />

            <TransactionItem
              icon={<View style={{ width: 8, height: 8, borderRadius: 4, backgroundColor: theme.colors.success }} />}
              title="Divinefortune Nnweremizu"
              subtitle="Jul 14 • 7:30 PM"
              amount="₦1500"
              status="received"
              theme={theme}
              isDark={isDark}
            />

            <TransactionItem
              icon={<View style={{ width: 8, height: 8, borderRadius: 4, backgroundColor: theme.colors.success }} />}
              title="Divinefortune Nnweremizu"
              subtitle="Jul 14 • 7:30 PM"
              amount="₦2500"
              status="received"
              theme={theme}
              isDark={isDark}
            />
          </View>





          </ScrollView>
        )}

        {/* Modals - always available regardless of loading state */}
        <ClipboardPhoneModal
          visible={showClipboardModal}
          phoneNumber={clipboardPhone || ''}
          onIgnore={handleIgnoreClipboard}
          onUse={handleUseClipboard}
        />
        <MoreProductsModal
          visible={showMoreProductsModal}
          onClose={() => setShowMoreProductsModal(false)}
          onSuggest={() => {
            setShowMoreProductsModal(false);
            // Optionally, navigate to a suggestion screen or show a toast
          }}
        />
      </RNSafeAreaView>
    </View>
  );
};

export default CustomHomeScreen;