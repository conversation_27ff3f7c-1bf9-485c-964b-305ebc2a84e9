import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface DataIconProps {
  size?: number;
  color?: string;
}

const DataIcon: React.FC<DataIconProps> = ({ 
  size = 24, 
  color = '#000000' 
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M2 17H22V19H2V17ZM1.15 12.95L4 15.8L9.85 9.95L13.85 13.95L22.85 4.95L21.45 3.55L13.85 11.15L9.85 7.15L2.55 14.45L1.15 12.95Z"
        fill={color}
      />
    </Svg>
  );
};

export default DataIcon;