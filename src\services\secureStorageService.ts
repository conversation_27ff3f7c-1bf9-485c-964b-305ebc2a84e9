import * as Keychain from 'react-native-keychain';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';
import CryptoJS from 'crypto-js';
import { ENV_CONFIG } from '../config/environment';
import logger from './productionLogger';

interface SecureStorageOptions {
  accessGroup?: string;
  touchID?: boolean;
  showModal?: boolean;
  kLocalizedFallbackTitle?: string;
  accessControl?: Keychain.ACCESS_CONTROL;
  authenticationType?: Keychain.AUTHENTICATION_TYPE;
  requireAuthentication?: boolean;
}

interface EncryptedData {
  data: string;
  iv: string;
  timestamp: number;
  version: string;
}

/**
 * Secure Storage Service for Production
 * Handles secure storage of sensitive data with encryption and biometric protection
 *
 * SECURITY LEVELS:
 * - LOW: Device passcode only (for tokens - no biometric prompts for better UX)
 * - MEDIUM: Biometric OR device passcode (for PINs - user choice)
 * - HIGH: Biometric required (for sensitive operations only)
 */
class SecureStorageService {
  private readonly serviceName = 'VendyApp';
  private readonly encryptionKey: string;
  private readonly cache = new Map<string, { data: any; timestamp: number }>();
  private readonly cacheTimeout = 30000; // 30 seconds in-memory cache
  
  // Different security levels for different types of data
  private readonly lowSecurityOptions = {
    service: this.serviceName,
    // NO authentication prompts - accessible when device is unlocked
    // This is the most permissive setting for better UX
    accessGroup: Platform.OS === 'ios' ? 'group.com.vendy.app' : undefined,
    // Don't set accessControl or authenticationType for maximum accessibility
  };

  private readonly mediumSecurityOptions = {
    service: this.serviceName,
    // Biometric OR device passcode (user choice)
    accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_ANY_OR_DEVICE_PASSCODE,
    authenticationType: Keychain.AUTHENTICATION_TYPE.DEVICE_PASSCODE_OR_BIOMETRICS,
    accessGroup: Platform.OS === 'ios' ? 'group.com.vendy.app' : undefined,
  };

  private readonly highSecurityOptions = {
    service: this.serviceName,
    // Biometric required (for sensitive operations)
    accessControl: Keychain.ACCESS_CONTROL.BIOMETRY_CURRENT_SET,
    authenticationType: Keychain.AUTHENTICATION_TYPE.BIOMETRICS,
    accessGroup: Platform.OS === 'ios' ? 'group.com.vendy.app' : undefined,
  };

  // Default to low security for better UX
  private readonly defaultOptions = this.lowSecurityOptions;

  constructor() {
    // Generate or retrieve encryption key for additional layer
    this.encryptionKey = this.getOrCreateEncryptionKey();
  }

  private getOrCreateEncryptionKey(): string {
    // This would ideally come from a secure hardware module or be derived
    // For now, we'll use a combination of device-specific data
    const deviceData = `${Platform.OS}-${Platform.Version}-VendyApp-v1.0.0`;
    return CryptoJS.SHA256(deviceData).toString();
  }

  private encrypt(data: string): EncryptedData {
    const iv = CryptoJS.lib.WordArray.random(16);
    const encrypted = CryptoJS.AES.encrypt(data, this.encryptionKey, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    
    return {
      data: encrypted.toString(),
      iv: iv.toString(),
      timestamp: Date.now(),
      version: '1.0.0'
    };
  }

  private decrypt(encryptedData: EncryptedData): string {
    const decrypted = CryptoJS.AES.decrypt(encryptedData.data, this.encryptionKey, {
      iv: CryptoJS.enc.Hex.parse(encryptedData.iv),
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });
    
    return decrypted.toString(CryptoJS.enc.Utf8);
  }

  private getCacheKey(key: string): string {
    return `cache_${key}`;
  }

  private isCacheValid(timestamp: number): boolean {
    return Date.now() - timestamp < this.cacheTimeout;
  }

  /**
   * Store sensitive data securely (tokens, pins, etc.)
   */
  async setSecureItem(key: string, value: string, options?: SecureStorageOptions): Promise<boolean> {
    try {
      // Validate inputs
      if (!key || !value) {
        logger.error(`Invalid parameters for secure storage: key=${!!key}, value=${!!value}`, null, 'storage');
        return false;
      }

      const keychainOptions = {
        ...this.defaultOptions,
        service: `${this.serviceName}_${key}`,
        ...options,
      };

      // Additional safety check for Android null pointer issues
      if (Platform.OS === 'android' && options?.accessControl) {
        // Check if the device supports the requested access control
        try {
          const supportedBiometry = await Keychain.getSupportedBiometryType();
          if (!supportedBiometry && options.accessControl !== Keychain.ACCESS_CONTROL.DEVICE_PASSCODE) {
            logger.warn(`Biometric access control not supported, falling back to device passcode for: ${key}`, null, 'storage');
            keychainOptions.accessControl = Keychain.ACCESS_CONTROL.DEVICE_PASSCODE;
            keychainOptions.authenticationType = Keychain.AUTHENTICATION_TYPE.DEVICE_PASSCODE_OR_BIOMETRICS;
          }
        } catch (biometryCheckError) {
          logger.warn(`Could not check biometry support, using default options for: ${key}`, biometryCheckError, 'storage');
          // Remove problematic options
          delete keychainOptions.accessControl;
          delete keychainOptions.authenticationType;
        }
      }

      await Keychain.setInternetCredentials(
        key,
        key, // username (we use key as username)
        value, // password (actual value)
        keychainOptions
      );

      logger.info(`Stored secure item: ${key}`, null, 'storage');
      return true;
    } catch (error) {
      logger.error(`Failed to store secure item: ${key}`, error, 'storage');

      // Check for specific Android null pointer errors
      if (error && typeof error === 'object' && 'code' in error && error.code === 'E_UNKNOWN_ERROR') {
        logger.warn(`Android keychain error detected, trying simplified storage for: ${key}`, null, 'storage');

        // Try with minimal options for Android compatibility
        try {
          await Keychain.setInternetCredentials(
            key,
            key,
            value,
            {
              service: `${this.serviceName}_${key}`,
              // No access control or authentication type for maximum compatibility
            }
          );
          logger.info(`Stored secure item with simplified options: ${key}`, null, 'storage');
          return true;
        } catch (simplifiedError) {
          logger.error(`Simplified storage also failed for: ${key}`, simplifiedError, 'storage');
        }
      }

      // Fallback to AsyncStorage for non-critical data
      if (this.shouldFallbackToAsyncStorage(key)) {
        try {
          await AsyncStorage.setItem(`secure_${key}`, value);
          logger.warn(`Fallback to AsyncStorage for: ${key}`, null, 'storage');
          return true;
        } catch (fallbackError) {
          logger.error(`AsyncStorage fallback failed for: ${key}`, fallbackError, 'storage');
        }
      }

      return false;
    }
  }

  /**
   * Retrieve sensitive data securely with robust error handling
   */
  async getSecureItem(key: string, options?: SecureStorageOptions): Promise<string | null> {
    try {
      // Validate input
      if (!key) {
        logger.error('Invalid key for secure item retrieval', null, 'storage');
        return null;
      }

      const keychainOptions = {
        ...this.defaultOptions,
        service: `${this.serviceName}_${key}`,
        ...options,
      };

      // Additional safety check for Android null pointer issues
      if (Platform.OS === 'android' && options?.accessControl) {
        try {
          const supportedBiometry = await Keychain.getSupportedBiometryType();
          if (!supportedBiometry && options.accessControl !== Keychain.ACCESS_CONTROL.DEVICE_PASSCODE) {
            logger.debug(`Biometric access control not supported for retrieval, using default for: ${key}`, null, 'storage');
            keychainOptions.accessControl = undefined;
            keychainOptions.authenticationType = undefined;
          }
        } catch (biometryCheckError) {
          logger.debug(`Could not check biometry support for retrieval, using default options for: ${key}`, biometryCheckError, 'storage');
          delete keychainOptions.accessControl;
          delete keychainOptions.authenticationType;
        }
      }

      const credentials = await Keychain.getInternetCredentials(key, keychainOptions);
      if (credentials && typeof credentials === 'object' && 'password' in credentials) {
        logger.debug(`Retrieved secure item: ${key}`, null, 'storage');
        return credentials.password;
      }
      return null;
    } catch (error) {
      logger.debug(`Failed to retrieve secure item: ${key}`, error, 'storage');

      // Check for specific Android null pointer errors
      if (error && typeof error === 'object' && 'code' in error && error.code === 'E_UNKNOWN_ERROR') {
        logger.debug(`Android keychain error detected, trying simplified retrieval for: ${key}`, null, 'storage');

        // Try with minimal options for Android compatibility
        try {
          const credentials = await Keychain.getInternetCredentials(key, {
            service: `${this.serviceName}_${key}`,
            // No access control or authentication type for maximum compatibility
          });

          if (credentials && typeof credentials === 'object' && 'password' in credentials) {
            logger.debug(`Retrieved secure item with simplified options: ${key}`, null, 'storage');
            return credentials.password;
          }
        } catch (simplifiedError) {
          logger.debug(`Simplified retrieval also failed for: ${key}`, simplifiedError, 'storage');
        }
      }

      // Fallback to AsyncStorage
      if (this.shouldFallbackToAsyncStorage(key)) {
        try {
          const value = await AsyncStorage.getItem(`secure_${key}`);
          if (value) {
            logger.debug(`Retrieved from AsyncStorage fallback: ${key}`, null, 'storage');
            return value;
          }
        } catch (fallbackError) {
          logger.debug(`AsyncStorage fallback retrieval failed for: ${key}`, fallbackError, 'storage');
        }
      }

      return null;
    }
  }

  /**
   * Remove sensitive data
   */
  async removeSecureItem(key: string): Promise<boolean> {
    try {
      await Keychain.resetInternetCredentials({ service: `${this.serviceName}_${key}` });
      logger.info(`Removed secure item: ${key}`, null, 'storage');
      
      // Also remove from AsyncStorage fallback
      try {
        await AsyncStorage.removeItem(`secure_${key}`);
      } catch (fallbackError) {
        // Ignore fallback errors
      }
      
      return true;
    } catch (error) {
      logger.error(`Failed to remove secure item: ${key}`, error, 'storage');
      return false;
    }
  }

  /**
   * Check if biometric authentication is available
   */
  async isBiometricAvailable(): Promise<boolean> {
    try {
      const biometryType = await Keychain.getSupportedBiometryType();
      return biometryType !== null;
    } catch (error) {
      logger.error('Biometric availability check failed', error, 'storage');
      return false;
    }
  }

  /**
   * Get supported biometry type
   */
  async getBiometryType(): Promise<Keychain.BIOMETRY_TYPE | null> {
    try {
      return await Keychain.getSupportedBiometryType();
    } catch (error) {
      logger.error('Failed to get biometry type', error, 'storage');
      return null;
    }
  }

  /**
   * Store authentication tokens securely (LOW SECURITY - no biometric prompts)
   */
  async storeAuthTokens(accessToken: string, refreshToken: string): Promise<boolean> {
    try {
      // Try keychain first with no authentication requirements
      try {
        const success1 = await this.setSecureItem('accessToken', accessToken, this.lowSecurityOptions);
        const success2 = await this.setSecureItem('refreshToken', refreshToken, this.lowSecurityOptions);
        if (success1 && success2) {
          logger.info('Tokens stored in keychain (no auth required)', null, 'storage');
          return true;
        }
      } catch (keychainError) {
        logger.warn('Keychain storage failed, falling back to AsyncStorage', keychainError, 'storage');
      }

      // Fallback to AsyncStorage with encryption for better UX
      const encryptedAccessToken = this.encrypt(accessToken);
      const encryptedRefreshToken = this.encrypt(refreshToken);

      await AsyncStorage.setItem('secure_accessToken', JSON.stringify(encryptedAccessToken));
      await AsyncStorage.setItem('secure_refreshToken', JSON.stringify(encryptedRefreshToken));

      logger.info('Tokens stored in AsyncStorage (encrypted)', null, 'storage');
      return true;
    } catch (error) {
      logger.error('Failed to store auth tokens', error, 'storage');
      return false;
    }
  }

  /**
   * Retrieve authentication tokens with robust error handling (LOW SECURITY - no biometric prompts)
   */
  async getAuthTokens(): Promise<{ accessToken: string | null; refreshToken: string | null }> {
    try {
      // Try keychain first with no authentication requirements
      let accessToken: string | null = null;
      let refreshToken: string | null = null;

      try {
        // Try to get tokens individually to isolate any failures
        accessToken = await this.getSecureItem('accessToken', this.lowSecurityOptions);
        refreshToken = await this.getSecureItem('refreshToken', this.lowSecurityOptions);

        if (accessToken && refreshToken) {
          logger.debug('Tokens retrieved from keychain (no auth required)', null, 'storage');
          return { accessToken, refreshToken };
        }
      } catch (keychainError) {
        logger.debug('Keychain retrieval failed, trying AsyncStorage fallback', keychainError, 'storage');
      }

      // Fallback to AsyncStorage
      try {
        const encryptedAccessToken = await AsyncStorage.getItem('secure_accessToken');
        const encryptedRefreshToken = await AsyncStorage.getItem('secure_refreshToken');

        if (encryptedAccessToken && encryptedRefreshToken) {
          try {
            const decryptedAccessToken = this.decrypt(JSON.parse(encryptedAccessToken));
            const decryptedRefreshToken = this.decrypt(JSON.parse(encryptedRefreshToken));

            logger.debug('Tokens retrieved from AsyncStorage (decrypted)', null, 'storage');
            return {
              accessToken: decryptedAccessToken,
              refreshToken: decryptedRefreshToken
            };
          } catch (decryptError) {
            logger.warn('Failed to decrypt tokens from AsyncStorage', decryptError, 'storage');
            // Clear corrupted encrypted data
            await AsyncStorage.removeItem('secure_accessToken');
            await AsyncStorage.removeItem('secure_refreshToken');
          }
        }
      } catch (asyncStorageError) {
        logger.debug('AsyncStorage retrieval failed', asyncStorageError, 'storage');
      }

      // If we got here, no tokens were found
      logger.debug('No tokens found in any storage location', null, 'storage');
      return { accessToken: null, refreshToken: null };
    } catch (error) {
      logger.error('Unexpected error retrieving auth tokens', {
        error: error instanceof Error ? error.message : String(error),
        type: typeof error,
        stack: error instanceof Error ? error.stack : undefined
      }, 'storage');
      return { accessToken: null, refreshToken: null };
    }
  }

  /**
   * Get access token quickly for API calls with robust error handling (NO AUTHENTICATION PROMPTS)
   */
  async getAccessToken(): Promise<string | null> {
    try {
      // Try keychain first
      try {
        const token = await this.getSecureItem('accessToken', this.lowSecurityOptions);
        if (token) {
          logger.debug('Access token retrieved from keychain', null, 'storage');
          return token;
        }
      } catch (keychainError) {
        logger.debug('Keychain access token retrieval failed, trying AsyncStorage', keychainError, 'storage');
      }

      // Fallback to AsyncStorage
      try {
        const encryptedToken = await AsyncStorage.getItem('secure_accessToken');
        if (encryptedToken) {
          try {
            const decryptedToken = this.decrypt(JSON.parse(encryptedToken));
            logger.debug('Access token retrieved from AsyncStorage', null, 'storage');
            return decryptedToken;
          } catch (decryptError) {
            logger.warn('Failed to decrypt access token from AsyncStorage', decryptError, 'storage');
            // Clear corrupted data
            await AsyncStorage.removeItem('secure_accessToken');
          }
        }
      } catch (asyncStorageError) {
        logger.debug('AsyncStorage access token retrieval failed', asyncStorageError, 'storage');
      }

      logger.debug('No access token found in any storage location', null, 'storage');
      return null;
    } catch (error) {
      logger.error('Unexpected error retrieving access token', {
        error: error instanceof Error ? error.message : String(error),
        type: typeof error,
        stack: error instanceof Error ? error.stack : undefined
      }, 'storage');
      return null;
    }
  }

  /**
   * Get refresh token quickly for token refresh with robust error handling (NO AUTHENTICATION PROMPTS)
   */
  async getRefreshToken(): Promise<string | null> {
    try {
      // Try keychain first
      try {
        const token = await this.getSecureItem('refreshToken', this.lowSecurityOptions);
        if (token) {
          logger.debug('Refresh token retrieved from keychain', null, 'storage');
          return token;
        }
      } catch (keychainError) {
        logger.debug('Keychain refresh token retrieval failed, trying AsyncStorage', keychainError, 'storage');
      }

      // Fallback to AsyncStorage
      try {
        const encryptedToken = await AsyncStorage.getItem('secure_refreshToken');
        if (encryptedToken) {
          try {
            const decryptedToken = this.decrypt(JSON.parse(encryptedToken));
            logger.debug('Refresh token retrieved from AsyncStorage', null, 'storage');
            return decryptedToken;
          } catch (decryptError) {
            logger.warn('Failed to decrypt refresh token from AsyncStorage', decryptError, 'storage');
            // Clear corrupted data
            await AsyncStorage.removeItem('secure_refreshToken');
          }
        }
      } catch (asyncStorageError) {
        logger.debug('AsyncStorage refresh token retrieval failed', asyncStorageError, 'storage');
      }

      logger.debug('No refresh token found in any storage location', null, 'storage');
      return null;
    } catch (error) {
      logger.error('Unexpected error retrieving refresh token', {
        error: error instanceof Error ? error.message : String(error),
        type: typeof error,
        stack: error instanceof Error ? error.stack : undefined
      }, 'storage');
      return null;
    }
  }

  /**
   * Clear authentication tokens only
   */
  async clearAuthTokens(): Promise<boolean> {
    try {
      // Clear from keychain
      const keychainResults = await Promise.all([
        this.removeSecureItem('accessToken'),
        this.removeSecureItem('refreshToken'),
      ]);

      // Clear from AsyncStorage
      const asyncResults = await Promise.all([
        AsyncStorage.removeItem('secure_accessToken'),
        AsyncStorage.removeItem('secure_refreshToken'),
      ]);

      logger.info('Authentication tokens cleared successfully', null, 'storage');
      return true;
    } catch (error) {
      logger.error('Failed to clear auth tokens', error, 'storage');
      return false;
    }
  }

  /**
   * Clear all authentication data
   */
  async clearAuthData(): Promise<boolean> {
    try {
      // Clear from keychain
      const keychainResults = await Promise.all([
        this.removeSecureItem('accessToken'),
        this.removeSecureItem('refreshToken'),
        this.removeSecureItem('userPin'),
        this.removeSecureItem('biometricKey'),
      ]);

      // Clear from AsyncStorage fallback
      const asyncStorageResults = await Promise.all([
        AsyncStorage.removeItem('secure_accessToken'),
        AsyncStorage.removeItem('secure_refreshToken'),
      ]);

      logger.info('Authentication data cleared from all storage locations', null, 'storage');
      return keychainResults.every(result => result);
    } catch (error) {
      logger.error('Failed to clear auth data', error, 'storage');
      return false;
    }
  }

  /**
   * Store user PIN securely with robust fallback handling
   */
  async storeUserPin(pin: string): Promise<boolean> {
    try {
      // First, check if biometric authentication is available
      const biometricAvailable = await this.isBiometricAvailable();

      if (biometricAvailable) {
        // Try with biometric authentication first
        try {
          const success = await this.setSecureItem('userPin', pin, {
            ...this.mediumSecurityOptions,
            touchID: true,
            showModal: true,
            kLocalizedFallbackTitle: 'Use Passcode',
          });

          if (success) {
            logger.info('PIN stored with biometric protection', null, 'storage');
            return true;
          }
        } catch (biometricError) {
          logger.warn('Biometric PIN storage failed, trying device passcode only', biometricError, 'storage');
        }
      }

      // Fallback to device passcode only (more compatible)
      try {
        const success = await this.setSecureItem('userPin', pin, {
          accessControl: Keychain.ACCESS_CONTROL.DEVICE_PASSCODE,
          authenticationType: Keychain.AUTHENTICATION_TYPE.DEVICE_PASSCODE_OR_BIOMETRICS,
          accessGroup: Platform.OS === 'ios' ? 'group.com.vendy.app' : undefined,
        });

        if (success) {
          logger.info('PIN stored with device passcode protection', null, 'storage');
          return true;
        }
      } catch (passcodeError) {
        logger.warn('Device passcode PIN storage failed, trying low security', passcodeError, 'storage');
      }

      // Final fallback to low security (no authentication required)
      const success = await this.setSecureItem('userPin', pin, this.lowSecurityOptions);
      if (success) {
        logger.warn('PIN stored with low security (no authentication required)', null, 'storage');
        return true;
      }

      return false;
    } catch (error) {
      logger.error('All PIN storage methods failed', error, 'storage');
      return false;
    }
  }

  /**
   * Check if user PIN is already stored
   */
  async isPinStored(): Promise<boolean> {
    try {
      // Try different security levels to check if PIN exists
      const securityLevels = [
        {
          ...this.mediumSecurityOptions,
          touchID: false, // Don't show biometric prompt for checking
          showModal: false,
        },
        {
          accessControl: Keychain.ACCESS_CONTROL.DEVICE_PASSCODE,
          authenticationType: Keychain.AUTHENTICATION_TYPE.DEVICE_PASSCODE_OR_BIOMETRICS,
          accessGroup: Platform.OS === 'ios' ? 'group.com.vendy.app' : undefined,
        },
        this.lowSecurityOptions
      ];

      for (const options of securityLevels) {
        try {
          const credentials = await Keychain.getInternetCredentials('userPin', {
            service: `${this.serviceName}_userPin`,
            ...options,
          });

          if (credentials && typeof credentials === 'object' && 'password' in credentials && credentials.password) {
            return true;
          }
        } catch (error) {
          // Continue to next security level
          continue;
        }
      }

      // Also check AsyncStorage fallback
      try {
        const fallbackPin = await AsyncStorage.getItem('secure_userPin');
        return !!fallbackPin;
      } catch (error) {
        // Ignore AsyncStorage errors
      }

      return false;
    } catch (error) {
      logger.error('Failed to check if PIN is stored', error, 'storage');
      return false;
    }
  }

  /**
   * Verify user PIN with robust fallback handling
   */
  async verifyUserPin(inputPin: string): Promise<boolean> {
    try {
      // Try different security levels in order of preference
      const securityLevels = [
        // First try biometric if available
        {
          ...this.mediumSecurityOptions,
          touchID: true,
          showModal: true,
          kLocalizedFallbackTitle: 'Use Passcode',
        },
        // Then try device passcode only
        {
          accessControl: Keychain.ACCESS_CONTROL.DEVICE_PASSCODE,
          authenticationType: Keychain.AUTHENTICATION_TYPE.DEVICE_PASSCODE_OR_BIOMETRICS,
          accessGroup: Platform.OS === 'ios' ? 'group.com.vendy.app' : undefined,
        },
        // Finally try low security
        this.lowSecurityOptions
      ];

      for (const options of securityLevels) {
        try {
          const storedPin = await this.getSecureItem('userPin', options);
          if (storedPin) {
            const isValid = storedPin === inputPin;
            if (isValid) {
              logger.info('PIN verification successful', null, 'storage');
            }
            return isValid;
          }
        } catch (error) {
          // Continue to next security level
          logger.debug('PIN verification failed with current security level, trying next', error, 'storage');
          continue;
        }
      }

      logger.warn('PIN not found in any security level', null, 'storage');
      return false;
    } catch (error) {
      logger.error('PIN verification failed completely', error, 'storage');
      return false;
    }
  }

  /**
   * Store biometric authentication key (HIGH SECURITY - biometric required)
   */
  async storeBiometricKey(key: string): Promise<boolean> {
    return this.setSecureItem('biometricKey', key, this.highSecurityOptions);
  }

  /**
   * Retrieve biometric authentication key (HIGH SECURITY - biometric required)
   */
  async getBiometricKey(): Promise<string | null> {
    return this.getSecureItem('biometricKey', this.highSecurityOptions);
  }

  /**
   * Store biometric toggle without requiring biometric authentication (NO SECURITY)
   */
  async setBiometricToggle(value: boolean): Promise<boolean> {
    try {
      // Use simple keychain storage without any authentication requirements
      await Keychain.setInternetCredentials(
        'biometricEnabled',
        'biometricEnabled',
        value ? 'true' : 'false',
        {
          service: `${this.serviceName}_biometricEnabled`,
          // No accessControl or authenticationType for simple preference
          // This allows access without any prompts
        }
      );
      return true;
    } catch (error) {
      logger.error('Failed to store biometric toggle', error, 'storage');
      return false;
    }
  }

  /**
   * Get biometric toggle without requiring biometric authentication (NO SECURITY)
   */
  async getBiometricToggle(): Promise<boolean> {
    try {
      const credentials = await Keychain.getInternetCredentials('biometricEnabled', {
        service: `${this.serviceName}_biometricEnabled`,
        // No accessControl or authenticationType for simple preference
        // This allows access without any prompts
      });
      if (credentials && typeof credentials === 'object' && 'password' in credentials) {
        return credentials.password === 'true';
      }
      return false;
    } catch (error) {
      logger.error('Failed to get biometric toggle', error, 'storage');
      return false;
    }
  }

  /**
   * Check if we should fallback to AsyncStorage for non-critical data
   */
  private shouldFallbackToAsyncStorage(key: string): boolean {
    // For tokens, we now prefer AsyncStorage fallback for better UX
    const tokenKeys = ['accessToken', 'refreshToken'];
    if (tokenKeys.includes(key)) {
      return true;
    }

    // Don't fallback for critical security data
    const criticalKeys = ['userPin', 'biometricKey'];
    return !criticalKeys.includes(key);
  }

  /**
   * Test if keychain access works without prompts
   */
  async testKeychainAccess(): Promise<boolean> {
    try {
      const testKey = 'test_keychain_access';
      const testValue = 'test_value';

      // Try to store and retrieve a test value
      await Keychain.setInternetCredentials(testKey, testKey, testValue, this.lowSecurityOptions);
      const result = await Keychain.getInternetCredentials(testKey, this.lowSecurityOptions);

      // Clean up
      try {
        await Keychain.resetInternetCredentials({ service: testKey });
      } catch (cleanupError) {
        // Ignore cleanup errors
      }

      return result && typeof result === 'object' && 'password' in result && result.password === testValue;
    } catch (error) {
      logger.debug('Keychain access test failed, will use AsyncStorage fallback', error, 'storage');
      return false;
    }
  }

  /**
   * Get all stored keys (for debugging)
   */
  async getAllStoredKeys(): Promise<string[]> {
    try {
      // This is a debug method - in production, you might want to disable this
      if (!__DEV__) {
        return [];
      }

      const allKeys = await AsyncStorage.getAllKeys();
      const secureKeys = allKeys.filter(key => key.startsWith('secure_'));
      
      logger.info('Retrieved stored keys for debugging', { keysCount: secureKeys.length }, 'storage');
      return secureKeys;
    } catch (error) {
      logger.error('Failed to get stored keys', error, 'storage');
      return [];
    }
  }

  /**
   * Reset all secure storage (for logout/reset)
   */
  async resetAllSecureStorage(): Promise<boolean> {
    try {
      // Clear keychain items
      const keychainKeys = ['accessToken', 'refreshToken', 'userPin', 'biometricKey'];
      const keychainResults = await Promise.all(
        keychainKeys.map(key => this.removeSecureItem(key))
      );

      // Clear AsyncStorage fallback items
      const allKeys = await AsyncStorage.getAllKeys();
      const secureKeys = allKeys.filter(key => key.startsWith('secure_'));
      if (secureKeys.length > 0) {
        await AsyncStorage.multiRemove(secureKeys);
      }

      logger.info('Reset all secure storage', null, 'storage');
      return keychainResults.every(result => result);
    } catch (error) {
      logger.error('Failed to reset secure storage', error, 'storage');
      return false;
    }
  }
}

export const secureStorage = new SecureStorageService();
export default secureStorage;