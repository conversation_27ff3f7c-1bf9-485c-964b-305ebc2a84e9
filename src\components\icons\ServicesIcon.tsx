import React from 'react';
import Svg, { Path } from 'react-native-svg';

interface ServicesIconProps {
  size?: number;
  color?: string;
}

const ServicesIcon: React.FC<ServicesIconProps> = ({ 
  size = 24, 
  color = '#000000' 
}) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M4 6H20V8H4V6ZM4 11H20V13H4V11ZM4 16H20V18H4V16Z"
        fill={color}
      />
    </Svg>
  );
};

export default ServicesIcon;